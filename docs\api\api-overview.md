# Community Services API Overview

## Introduction

This document provides a comprehensive overview of the Community Services API, which powers the frontend application. The API is organized into several domains, each handling specific business functionality.

## Base URL

All API endpoints are prefixed with `/api/v1/`

## Authentication

All endpoints require authentication unless otherwise specified. Include the authorization header:
```
Authorization: Bearer <your_token>
```

## API Domains

### [Users API](./users.md)
**Base Path**: `/api/v1/users`

Handles user management, profiles, discovery, and HeyWhale platform integration.

**Key Features**:
- User profile management (basic, dynamic, student, rich profiles)
- User discovery and search by various criteria
- HeyWhale integration (badges, tags)
- Cache management

**Main Endpoints**:
- `GET /users/{user_id}/profile` - Get user profile
- `POST /users/discovery` - Discover users by criteria
- `GET /users/search` - Search users by identifier
- `GET /users/integrations/heywhale/tags/tree` - Get HeyWhale tags
- `POST /users/integrations/heywhale/badges/sync` - Sync badges

---

### [Community API](./community.md)
**Base Path**: `/api/v1/community`

Manages community-related data including universities, majors, and user discovery.

**Key Features**:
- Universities and majors listings
- Geographic and institutional filtering

**Main Endpoints**:
- `GET /community/universities` - List universities
- `GET /community/majors` - List majors

---

### [Competitions API](./competitions.md)
**Base Path**: `/api/v1/competitions`

Handles competition user registration and participation data.

**Key Features**:
- Competition user registration by type and identity
- Multiple profile types (basic, detail, full_form)

**Main Endpoints**:
- `POST /competitions/register` - Get competition users by type
- `POST /competitions/register/identity` - Get competition users by identity

---

### [Camp Admin API](./camp-admin.md)
**Base Path**: `/api/v1/camp-admin`

Provides administrative functions for routes, qualifications, credits, and logs.

**Key Features**:
- Routes and competitions management
- Qualification scoring rules
- Credit award and revocation
- Administrative logs

**Main Endpoints**:
- `GET /camp-admin/routes` - List routes
- `GET /camp-admin/` - List competitions
- `GET /camp-admin/qualifications` - List qualifications
- `POST /camp-admin/credits/award` - Award credits
- `GET /camp-admin/credits/history` - Get credit history

---

### [Analytics API](./analytics.md)
**Base Path**: `/api/v1/analytics`

Handles rankings, statistics, event tracking, and ShenCe analytics integration.

**Key Features**:
- School and user rankings
- Summary statistics
- Event tracking
- ShenCe integration for analytics tags

**Main Endpoints**:
- `GET /analytics/health` - Health check
- `POST /analytics/camp-only/statistics/summary` - Get summary statistics
- `POST /analytics/camp-only/rankings/schools` - Get school rankings
- `POST /analytics/camp-only/rankings/users/total` - Get user rankings
- `POST /analytics/integrations/shence/tags` - Create ShenCe tags

---

### [Integrations API](./integrations.md)
**Base Path**: `/api/v1/integrations`

Provides file uploads, webhook management, and cross-integration synchronization.

**Key Features**:
- File upload and processing
- Webhook execution
- Health monitoring
- Integration synchronization

**Main Endpoints**:
- `POST /integrations/files/upload` - Upload files
- `POST /integrations/webhooks/execute` - Execute webhooks
- `GET /integrations/health` - Check integration health
- `POST /integrations/sync` - Manual sync

## Common Response Format

### Success Response
```json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": { ... }
  }
}
```

## Status Codes

- `200 OK` - Request successful
- `201 Created` - Resource created successfully
- `400 Bad Request` - Invalid request data
- `401 Unauthorized` - Authentication required
- `403 Forbidden` - Insufficient permissions
- `404 Not Found` - Resource not found
- `422 Unprocessable Entity` - Validation errors
- `500 Internal Server Error` - Server error
- `503 Service Unavailable` - Service temporarily unavailable

## Pagination

List endpoints support pagination with the following parameters:
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20, max: 100)
- `offset` - Number of items to skip (alternative to page)

### Pagination Response
```json
{
  "success": true,
  "data": [...],
  "total": 150,
  "limit": 20,
  "offset": 0
}
```

## Permission System

The API uses a role-based permission system:

- **User Permissions**: `user_read_permission`, `user_discovery_permission`
- **Admin Permissions**: `admin_access`, `super_admin_access`
- **Integration Permissions**: `heywhale_access_permission`, `shence_access_permission`
- **Analytics Permissions**: `ranking_access`, `statistics_access`, `event_tracking_access`

## Caching

Many endpoints implement caching for performance:
- User profiles: 10 minutes TTL
- User discovery: 3 minutes TTL
- HeyWhale tags: 1 hour TTL
- Search results: 3 minutes TTL

Cache can be managed through the Users API cache endpoints.

## Error Handling Best Practices

1. **Check Response Status**: Always check the `success` field
2. **Handle Specific Errors**: Use error codes for specific handling
3. **Graceful Degradation**: Handle service unavailability
4. **Retry Logic**: Implement appropriate retry for transient errors
5. **User Feedback**: Provide meaningful error messages to users

## Frontend Integration Tips

1. **Authentication**: Store and refresh tokens properly
2. **Loading States**: Show loading indicators for async operations
3. **Error Boundaries**: Implement error boundaries for API failures
4. **Pagination**: Implement infinite scroll or pagination controls
5. **Caching**: Leverage browser caching for static data
6. **Optimistic Updates**: Update UI optimistically where appropriate

## Development Workflow

1. **API First**: Design API contracts before implementation
2. **Documentation**: Keep documentation updated with changes
3. **Testing**: Test all endpoints thoroughly
4. **Versioning**: Use semantic versioning for API changes
5. **Monitoring**: Monitor API performance and errors

## Support and Resources

- **API Documentation**: Individual domain documentation files
- **Error Codes**: Comprehensive error code reference
- **Examples**: Code examples for common operations
- **Troubleshooting**: Common issues and solutions

For additional support, contact the development team or refer to the individual API documentation files for detailed endpoint specifications.
