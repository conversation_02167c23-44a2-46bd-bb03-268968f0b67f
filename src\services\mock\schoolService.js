/**
 * Mock School API Service
 */

import { initializeMockData } from './mockData.js'

// Initialize mock data
const mockData = initializeMockData()
let { schools, users } = mockData

// Helper functions
const delay = (ms = 500) => new Promise(resolve => setTimeout(resolve, ms))

const paginate = (data, page = 1, limit = 20) => {
  const offset = (page - 1) * limit
  const paginatedData = data.slice(offset, offset + limit)
  
  return {
    data: paginatedData,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: data.length,
      pages: Math.ceil(data.length / limit),
      hasNext: offset + limit < data.length,
      hasPrev: page > 1
    }
  }
}

const filterSchools = (schools, params) => {
  let filtered = [...schools]
  
  if (params.type) {
    filtered = filtered.filter(school => school.type === params.type)
  }
  
  if (params.level) {
    filtered = filtered.filter(school => school.level === params.level)
  }
  
  if (params.status) {
    filtered = filtered.filter(school => school.status === params.status)
  }
  
  if (params.country) {
    filtered = filtered.filter(school => school.country === params.country)
  }
  
  if (params.province) {
    filtered = filtered.filter(school => school.province === params.province)
  }
  
  if (params.city) {
    filtered = filtered.filter(school => school.city === params.city)
  }
  
  if (params.search) {
    const searchLower = params.search.toLowerCase()
    filtered = filtered.filter(school => 
      school.name.toLowerCase().includes(searchLower) ||
      school.shortName?.toLowerCase().includes(searchLower) ||
      school.description?.toLowerCase().includes(searchLower)
    )
  }
  
  if (params.isVerified !== undefined) {
    filtered = filtered.filter(school => school.isVerified === params.isVerified)
  }
  
  if (params.isPartner !== undefined) {
    filtered = filtered.filter(school => school.isPartner === params.isPartner)
  }
  
  // Sorting
  if (params.sortBy) {
    filtered.sort((a, b) => {
      let aVal = a[params.sortBy]
      let bVal = b[params.sortBy]
      
      if (params.sortBy === 'participationRate') {
        aVal = a.stats.participationRate
        bVal = b.stats.participationRate
      } else if (params.sortBy === 'averageScore') {
        aVal = a.stats.averageScore
        bVal = b.stats.averageScore
      }
      
      if (typeof aVal === 'string') {
        aVal = aVal.toLowerCase()
        bVal = bVal.toLowerCase()
      }
      
      if (params.sortOrder === 'desc') {
        return bVal > aVal ? 1 : -1
      }
      return aVal > bVal ? 1 : -1
    })
  }
  
  return filtered
}

// Mock API functions
export const schoolService = {
  // Get schools list
  async getSchools(params = {}) {
    await delay()
    
    try {
      const filtered = filterSchools(schools, params)
      const result = paginate(filtered, params.page, params.limit)
      
      return {
        success: true,
        ...result
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'FETCH_ERROR',
          message: 'Failed to fetch schools'
        }
      }
    }
  },

  // Get school by ID
  async getSchool(id) {
    await delay()
    
    try {
      const school = schools.find(s => s.id === id)
      
      if (!school) {
        return {
          success: false,
          error: {
            code: 'SCHOOL_NOT_FOUND',
            message: 'School not found'
          }
        }
      }
      
      return {
        success: true,
        data: school
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'FETCH_ERROR',
          message: 'Failed to fetch school'
        }
      }
    }
  },

  // Create school
  async createSchool(schoolData) {
    await delay()
    
    try {
      const newSchool = {
        id: `school_${schools.length + 1}`,
        ...schoolData,
        registeredAt: new Date().toISOString(),
        lastActiveAt: new Date().toISOString(),
        isVerified: false,
        isPartner: false,
        stats: {
          totalStudents: schoolData.studentCount || 0,
          activeStudents: 0,
          totalCompetitions: 0,
          totalSubmissions: 0,
          totalCreditsEarned: 0,
          averageScore: 0,
          participationRate: 0,
          completionRate: 0,
          winRate: 0
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      schools.push(newSchool)
      
      return {
        success: true,
        data: newSchool,
        message: 'School created successfully'
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'CREATE_ERROR',
          message: 'Failed to create school'
        }
      }
    }
  },

  // Update school
  async updateSchool(id, schoolData) {
    await delay()
    
    try {
      const index = schools.findIndex(s => s.id === id)
      
      if (index === -1) {
        return {
          success: false,
          error: {
            code: 'SCHOOL_NOT_FOUND',
            message: 'School not found'
          }
        }
      }
      
      schools[index] = {
        ...schools[index],
        ...schoolData,
        updatedAt: new Date().toISOString()
      }
      
      return {
        success: true,
        data: schools[index],
        message: 'School updated successfully'
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'UPDATE_ERROR',
          message: 'Failed to update school'
        }
      }
    }
  },

  // Get school analytics
  async getSchoolAnalytics(schoolId, params = {}) {
    await delay()
    
    try {
      const school = schools.find(s => s.id === schoolId)
      
      if (!school) {
        return {
          success: false,
          error: {
            code: 'SCHOOL_NOT_FOUND',
            message: 'School not found'
          }
        }
      }
      
      // Mock analytics data
      const analytics = {
        school,
        period: params.period || 'month',
        overview: {
          totalStudents: {
            label: 'Total Students',
            value: school.stats.totalStudents,
            change: 5.2,
            changeType: 'increase'
          },
          activeStudents: {
            label: 'Active Students',
            value: school.stats.activeStudents,
            change: 3.1,
            changeType: 'increase'
          },
          participationRate: {
            label: 'Participation Rate',
            value: Math.round(school.stats.participationRate * 100),
            change: 2.3,
            changeType: 'increase',
            format: 'percentage'
          },
          averageScore: {
            label: 'Average Score',
            value: school.stats.averageScore,
            change: -1.2,
            changeType: 'decrease'
          }
        },
        participation: {
          byMonth: [
            { date: '2024-01', value: 45 },
            { date: '2024-02', value: 67 },
            { date: '2024-03', value: 89 }
          ],
          byCompetitionType: [
            { type: 'hackathon', count: 23 },
            { type: 'contest', count: 45 },
            { type: 'challenge', count: 21 }
          ],
          byDepartment: [
            { department: 'Computer Science', count: 67 },
            { department: 'Engineering', count: 45 },
            { department: 'Mathematics', count: 23 }
          ]
        },
        performance: {
          scoreDistribution: [
            { range: '90-100', count: 12 },
            { range: '80-89', count: 34 },
            { range: '70-79', count: 45 },
            { range: '60-69', count: 23 },
            { range: '0-59', count: 8 }
          ],
          topPerformers: users.slice(0, 5).map(user => ({
            ...user,
            totalCredits: Math.floor(Math.random() * 1000) + 500,
            averageScore: Math.floor(Math.random() * 30) + 70
          })),
          improvementTrend: [
            { date: '2024-01', value: 72.5 },
            { date: '2024-02', value: 74.2 },
            { date: '2024-03', value: 76.8 }
          ]
        },
        engagement: {
          activeUsersTrend: [
            { date: '2024-01', value: 234 },
            { date: '2024-02', value: 267 },
            { date: '2024-03', value: 289 }
          ],
          retentionRate: 0.78,
          averageSessionDuration: 45.6
        },
        benchmarks: {
          vsNationalAverage: 12.5,
          vsRegionalAverage: 8.3,
          vsSimilarSchools: 5.7,
          ranking: {
            overall: Math.floor(Math.random() * 100) + 1,
            byCategory: {
              'programming': Math.floor(Math.random() * 50) + 1,
              'data_science': Math.floor(Math.random() * 50) + 1,
              'ai_ml': Math.floor(Math.random() * 50) + 1
            }
          }
        }
      }
      
      return {
        success: true,
        data: analytics
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'ANALYTICS_ERROR',
          message: 'Failed to fetch school analytics'
        }
      }
    }
  },

  // Get school statistics
  async getSchoolStats() {
    await delay()
    
    try {
      const totalSchools = schools.length
      const totalStudents = schools.reduce((sum, s) => sum + s.stats.totalStudents, 0)
      const averageParticipationRate = schools.reduce((sum, s) => sum + s.stats.participationRate, 0) / schools.length
      
      const topPerformingSchools = schools
        .sort((a, b) => b.stats.averageScore - a.stats.averageScore)
        .slice(0, 10)
        .map((school, index) => ({
          school,
          rank: index + 1,
          score: school.stats.averageScore,
          metrics: {
            participationRate: school.stats.participationRate,
            averageScore: school.stats.averageScore,
            totalCredits: school.stats.totalCreditsEarned
          }
        }))
      
      const geographicDistribution = schools.reduce((acc, s) => {
        acc[s.country] = (acc[s.country] || 0) + 1
        return acc
      }, {})
      
      const typeDistribution = schools.reduce((acc, s) => {
        acc[s.type] = (acc[s.type] || 0) + 1
        return acc
      }, {})
      
      const levelDistribution = schools.reduce((acc, s) => {
        acc[s.level] = (acc[s.level] || 0) + 1
        return acc
      }, {})
      
      const stats = {
        totalSchools,
        totalStudents,
        averageParticipationRate: Math.round(averageParticipationRate * 100) / 100,
        topPerformingSchools,
        geographicDistribution,
        typeDistribution,
        levelDistribution
      }
      
      return {
        success: true,
        data: stats
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'STATS_ERROR',
          message: 'Failed to fetch school statistics'
        }
      }
    }
  }
}
