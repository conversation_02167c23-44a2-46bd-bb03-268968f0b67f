# API Documentation

This directory contains comprehensive documentation for all API endpoints used in the Community Services Administrative System.

## Overview

The API follows RESTful conventions and returns JSON responses. All endpoints require authentication via JWT tokens.

**Base URL**: `http://community-workers:8000/v1/community`

## Authentication

All API requests must include a valid JWT token in the Authorization header:

```
Authorization: Bearer <jwt_token>
```

## Common Response Format

### Success Response
```json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": { ... }
  }
}
```

## API Modules

- [Competitions](./competitions.md) - Competition management endpoints
- [Credits](./credits.md) - Credit distribution and management
- [Schools](./schools.md) - School statistics and analytics
- [Users](./users.md) - User management and authentication
- [Common](./common.md) - Shared data structures and utilities

## Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

## Rate Limiting

API requests are limited to 1000 requests per hour per user.

## Pagination

List endpoints support pagination with the following parameters:

- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20, max: 100)
- `sort` - Sort field
- `order` - Sort order (asc/desc)

### Pagination Response
```json
{
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "pages": 8,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## Data Types

All TypeScript interfaces are defined in `src/services/types/` directory.

## Development Notes

- All API endpoints are currently mocked for development
- Real API integration will be implemented in Phase 3
- Mock data is stored in `src/services/mock/` directory
