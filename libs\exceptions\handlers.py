"""
Custom exception classes and FastAPI exception handlers.

This module provides:
- Custom application exceptions for all service layers
- FastAPI exception handlers for consistent error responses
- Database error handling (PostgreSQL, MongoDB, Redis)
- Service and infrastructure error handling
- Security and validation error handling
"""

from fastapi import HTTPException, Request, status
from fastapi.responses import JSONResponse
from typing import Union, Dict, Any, Optional
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
from tortoise.exceptions import OperationalError, DoesNotExist
from aiohttp.client_exceptions import ServerTimeoutError, ClientError
from libs.schemas.api.responses import ErrorResponse    
import asyncio
import logging

from core.config import settings

logger = logging.getLogger(__name__)


# ——— Database Exception Classes ———

class DatabaseConnectionError(Exception):
    """Exception for database connection failures."""
    
    def __init__(self, database_type: str, message: str, details: Dict[str, Any] = None):
        self.database_type = database_type
        self.message = message
        self.details = details or {}
        super().__init__(message)


class MongoDBError(Exception):
    """Exception for MongoDB-specific errors."""
    
    def __init__(self, operation: str, message: str, collection: str = None, details: Dict[str, Any] = None):
        self.operation = operation
        self.message = message
        self.collection = collection
        self.details = details or {}
        super().__init__(message)


class CacheError(Exception):
    """Exception for caching system errors."""
    
    def __init__(self, cache_type: str, operation: str, message: str, key: str = None):
        self.cache_type = cache_type
        self.operation = operation
        self.message = message
        self.key = key
        super().__init__(message)


class PostgreSQLError(Exception):
    """Exception for low-level PostgreSQL errors."""
    
    def __init__(self, query: str, message: str, error_code: str = None):
        self.query = query
        self.message = message
        self.error_code = error_code
        super().__init__(message)


# ——— Service Exception Classes ———

class ServiceException(Exception):
    """Custom application exception for general service errors."""

    def __init__(self, code: int, errmsg: str, data: Dict[str, Any] = None, service: str = None):
        self.code = code
        self.errmsg = errmsg
        self.data = data or {}
        self.service = service
        super().__init__(errmsg)


class ExternalServiceError(Exception):
    """Exception for external service integration failures."""
    
    def __init__(self, service_name: str, operation: str, message: str, status_code: int = None):
        self.service_name = service_name
        self.operation = operation
        self.message = message
        self.status_code = status_code
        super().__init__(message)


class BusinessLogicError(Exception):
    """Exception for business rule violations.
    
    Business rules are applied and set explicitly and externally.
    """
    
    def __init__(self, rule: str, message: str, context: Dict[str, Any] = None):
        self.rule = rule
        self.message = message
        self.context = context or {}
        super().__init__(message)


# ——— Infrastructure Exception Classes ———

class ResourceExhaustionError(Exception):
    """Exception for resource exhaustion (memory, connections, etc.)."""
    
    def __init__(self, resource_type: str, message: str, current_usage: str = None):
        self.resource_type = resource_type
        self.message = message
        self.current_usage = current_usage
        super().__init__(message)


class ConfigurationError(Exception):
    """Exception for configuration-related errors."""
    
    def __init__(self, config_key: str, message: str, expected_type: str = None):
        self.config_key = config_key
        self.message = message
        self.expected_type = expected_type
        super().__init__(message)


class NetworkError(Exception):
    """Exception for network-related errors."""
    
    def __init__(self, operation: str, message: str, endpoint: str = None, timeout: float = None):
        self.operation = operation
        self.message = message
        self.endpoint = endpoint
        self.timeout = timeout
        super().__init__(message)


# ——— Security Exception Classes ———

class AuthenticationError(Exception):
    """Exception for authentication failures."""
    
    def __init__(self, message: str, auth_type: str = "jwt", details: Dict[str, Any] = None):
        self.message = message
        self.auth_type = auth_type
        self.details = details or {}
        super().__init__(message)


class AuthorizationError(Exception):
    """Exception for authorization/permission failures."""
    
    def __init__(self, resource: str, action: str, user_id: str = None, required_permission: str = None):
        self.resource = resource
        self.action = action
        self.user_id = user_id
        self.required_permission = required_permission
        super().__init__(f"Access denied to {action} on {resource}")


class RateLimitError(Exception):
    """Exception for rate limiting violations."""
    
    def __init__(self, limit_type: str, current_rate: int, max_rate: int, reset_time: int = None):
        self.limit_type = limit_type
        self.current_rate = current_rate
        self.max_rate = max_rate
        self.reset_time = reset_time
        super().__init__(f"Rate limit exceeded: {current_rate}/{max_rate}")


# ——— Legacy Exception Classes (keeping for compatibility) ———

class UnicornException(Exception):
    """Legacy exception for general application errors."""

    def __init__(self, code: int, errmsg: str, data: Dict[str, Any] = None):
        self.code = code
        self.errmsg = errmsg
        self.data = data or {}
        super().__init__(errmsg)


class WrongContentErr(Exception):
    """Exception for illegal content input."""

    code = 455
    data = "illegal_content"

    def __init__(self, errmsg: str):
        self.errmsg = errmsg
        super().__init__(errmsg)


class RunOutTimeErr(Exception):
    """Exception for feature usage limit exceeded."""

    code = 475
    data = "run_out_time"

    def __init__(self, errmsg: str):
        self.errmsg = errmsg
        super().__init__(errmsg)


class OathErr(Exception):
    """Exception for authentication testing errors."""

    code = 444
    data = "测试鉴权报错"

    def __init__(self, errmsg: str):
        self.errmsg = errmsg
        super().__init__(errmsg)


# ——— Database Exception Handlers ———

async def database_connection_handler(_: Request, exc: DatabaseConnectionError) -> JSONResponse:
    """Handle database connection errors."""
    logger.error(f"Database connection error ({exc.database_type}): {exc.message}", extra=exc.details)
    
    return JSONResponse(
        {
            "success": False,
            "error": "DATABASE_CONNECTION_ERROR",
            "message": f"数据库连接失败 ({exc.database_type})",
            "data": {
                "database_type": exc.database_type,
                "details": exc.details
            },
            "code": 503
        },
        status_code=503,
    )


async def mongodb_error_handler(_: Request, exc: MongoDBError) -> JSONResponse:
    """Handle MongoDB-specific errors."""
    logger.error(f"MongoDB error in {exc.operation}: {exc.message}", extra={
        "collection": exc.collection,
        "details": exc.details
    })
    
    return JSONResponse(
        {
            "success": False,
            "error": "MONGODB_ERROR",
            "message": f"MongoDB操作失败: {exc.operation}",
            "data": {
                "operation": exc.operation,
                "collection": exc.collection,
                "details": exc.details
            },
            "code": 500
        },
        status_code=500,
    )


async def cache_error_handler(_: Request, exc: CacheError) -> JSONResponse:
    """Handle caching system errors."""
    logger.warning(f"Cache error ({exc.cache_type}): {exc.operation} - {exc.message}", extra={
        "key": exc.key
    })
    
    return JSONResponse(
        {
            "success": False,
            "error": "CACHE_ERROR",
            "message": f"缓存操作失败: {exc.operation}",
            "data": {
                "cache_type": exc.cache_type,
                "operation": exc.operation,
                "key": exc.key
            },
            "code": 503
        },
        status_code=503,
    )


async def postgresql_error_handler(_: Request, exc: PostgreSQLError) -> JSONResponse:
    """Handle low-level PostgreSQL errors."""
    logger.error(f"PostgreSQL error: {exc.message}", extra={
        "query": exc.query[:100] + "..." if len(exc.query) > 100 else exc.query,
        "error_code": exc.error_code
    })
    
    return JSONResponse(
        {
            "success": False,
            "error": "POSTGRESQL_ERROR",
            "message": "数据库查询失败",
            "data": {
                "error_code": exc.error_code,
                "query_preview": exc.query[:50] + "..." if len(exc.query) > 50 else exc.query
            },
            "code": 500
        },
        status_code=500,
    )


async def pg_does_not_exist_handler(_: Request, exc: DoesNotExist) -> JSONResponse:
    """Handle Tortoise ORM DoesNotExist exceptions."""
    return JSONResponse(
        {
            "success": False,
            "error": "NOT_FOUND",
            "message": "请求的资源不存在",
            "data": {},
            "code": 404
        },
        status_code=404,
    )


async def pg_operational_error_handler(_: Request, exc: OperationalError) -> JSONResponse:
    """Handle Tortoise ORM operational errors."""
    logger.error(f"Tortoise ORM operational error: {exc}")
    
    return JSONResponse(
        ErrorResponse(
            error="DATABASE_OPERATIONAL_ERROR",
            msg="数据库操作失败",
            data={"error_type": "tortoise_orm"},
            code=500
        ).model_dump(), 
        status_code=500,
    )


# ——— Service Exception Handlers ———

async def service_exception_handler(_: Request, exc: ServiceException) -> JSONResponse:
    """Handle ServiceException."""
    logger.error(f"Service error ({exc.service}): {exc.errmsg}", extra=exc.data)
    
    return JSONResponse(
        ErrorResponse(
            error="SERVICE_ERROR",
            msg=exc.errmsg,
            data=exc.data,
            code=exc.code
        ).model_dump(),
        status_code=exc.code if 400 <= exc.code < 600 else 500,
    )


async def external_service_error_handler(_: Request, exc: ExternalServiceError) -> JSONResponse:
    """Handle external service integration errors."""
    logger.error(f"External service error ({exc.service_name}): {exc.operation} - {exc.message}")
    
    return JSONResponse(
        ErrorResponse(
            error="EXTERNAL_SERVICE_ERROR",
            msg=f"外部服务调用失败: {exc.service_name}",
            data={
                "service": exc.service_name,
                "operation": exc.operation,
                "status_code": exc.status_code
            },
            code=502
        ).model_dump(),
        status_code=502,
    )


async def business_logic_error_handler(_: Request, exc: BusinessLogicError) -> JSONResponse:
    """Handle business logic errors."""
    logger.warning(f"Business rule violation: {exc.rule} - {exc.message}", extra=exc.context)
    
    return JSONResponse(
        ErrorResponse(
            error="BUSINESS_RULE_VIOLATION",
            msg=exc.message,
            data={
                "rule": exc.rule,
                "context": exc.context
            },
            code=400
        ).model_dump(),
        status_code=400,
    )


# ——— Infrastructure Exception Handlers ———

async def resource_exhaustion_handler(_: Request, exc: ResourceExhaustionError) -> JSONResponse:
    """Handle resource exhaustion errors."""
    logger.critical(f"Resource exhaustion ({exc.resource_type}): {exc.message}", extra={
        "current_usage": exc.current_usage
    })
    
    return JSONResponse(
        ErrorResponse(
            error="RESOURCE_EXHAUSTED",
            msg=f"系统资源不足: {exc.resource_type}",
            data={
                "resource_type": exc.resource_type,
                "current_usage": exc.current_usage
            },
            code=503
        ).model_dump(),
        status_code=503,
    )


async def configuration_error_handler(_: Request, exc: ConfigurationError) -> JSONResponse:
    """Handle configuration errors."""
    logger.error(f"Configuration error: {exc.config_key} - {exc.message}")
    
    return JSONResponse(
        ErrorResponse(
            error="CONFIGURATION_ERROR",
            msg="系统配置错误",
            data={
                "config_key": exc.config_key,
                "expected_type": exc.expected_type
            },
            code=500
        ).model_dump(),
        status_code=500,
    )


async def network_error_handler(_: Request, exc: NetworkError) -> JSONResponse:
    """Handle network errors."""
    logger.error(f"Network error: {exc.operation} - {exc.message}", extra={
        "endpoint": exc.endpoint,
        "timeout": exc.timeout
    })
    
    return JSONResponse(
        ErrorResponse(
            error="NETWORK_ERROR",
            msg=f"网络连接失败: {exc.operation}",
            data={
                "operation": exc.operation,
                "endpoint": exc.endpoint,
                "timeout": exc.timeout
            },
            code=502
        ).model_dump(),
        status_code=502,
    )


# ——— Security Exception Handlers ———

async def authentication_error_handler(_: Request, exc: AuthenticationError) -> JSONResponse:
    """Handle authentication errors."""
    logger.warning(f"Authentication failed ({exc.auth_type}): {exc.message}", extra=exc.details)
    
    return JSONResponse(
        ErrorResponse(
            error="AUTHENTICATION_FAILED",
            msg="身份验证失败",
            data={
                "auth_type": exc.auth_type,
                "details": exc.details
            },
            code=401
        ).model_dump(),
        status_code=401,
    )


async def authorization_error_handler(_: Request, exc: AuthorizationError) -> JSONResponse:
    """Handle authorization errors."""
    logger.warning(f"Authorization failed: {exc.user_id} cannot {exc.action} on {exc.resource}")
    
    return JSONResponse(
        ErrorResponse(
            error="AUTHORIZATION_FAILED",
            msg="权限不足",
            data={
                "resource": exc.resource,
                "action": exc.action,
                "required_permission": exc.required_permission
            },
            code=403
        ).model_dump(),
        status_code=403,
    )


async def rate_limit_error_handler(_: Request, exc: RateLimitError) -> JSONResponse:
    """Handle rate limiting errors."""
    logger.warning(f"Rate limit exceeded: {exc.limit_type} - {exc.current_rate}/{exc.max_rate}")
    
    return JSONResponse(
        ErrorResponse(
            error="RATE_LIMIT_EXCEEDED",
            msg="请求频率超限",
            data={
                "limit_type": exc.limit_type,
                "current_rate": exc.current_rate,
                "max_rate": exc.max_rate,
                "reset_time": exc.reset_time
            },
            code=429
        ).model_dump(),
        status_code=429,
        headers={"Retry-After": str(exc.reset_time)} if exc.reset_time else None,
    )


# ——— Legacy Exception Handlers ———

async def unicorn_exception_handler(_: Request, exc: UnicornException) -> JSONResponse:
    """Handle UnicornException (legacy)."""
    logger.error(f"UnicornException: {exc.errmsg}", extra=exc.data)
    
    return JSONResponse(
        ErrorResponse(
            error="UNICORN_ERROR",
            msg=exc.errmsg,
            data=exc.data,
            code=exc.code
        ).model_dump(),
        status_code=exc.code if 400 <= exc.code < 600 else 500,
    )


async def wrong_content_handler(_: Request, exc: WrongContentErr) -> JSONResponse:
    """Handle WrongContentErr."""
    return JSONResponse(
        ErrorResponse(
            error="ILLEGAL_CONTENT",
            msg=exc.errmsg,
            data={"content_type": exc.data},
            code=exc.code
        ).model_dump(),
        status_code=status.HTTP_400_BAD_REQUEST,
    )


async def run_out_time_handler(_: Request, exc: RunOutTimeErr) -> JSONResponse:
    """Handle RunOutTimeErr."""
    return JSONResponse(
        ErrorResponse(
            error="USAGE_LIMIT_EXCEEDED",
            msg=exc.errmsg,
            data={"limit_type": exc.data},
            code=exc.code
        ).model_dump(),
        status_code=status.HTTP_400_BAD_REQUEST,
    )


async def oath_error_handler(_: Request, exc: OathErr) -> JSONResponse:
    """Handle OathErr."""
    return JSONResponse(
        ErrorResponse(
            error="AUTH_TEST_ERROR",
            msg=f"测试鉴权报错: {str(exc)}",
            data={"test_type": exc.data},
            code=status.HTTP_418_IM_A_TEAPOT
        ).model_dump(),
        status_code=418,
    )


# ——— HTTP and Validation Exception Handlers ———

async def http_error_handler(_: Request, exc: HTTPException) -> JSONResponse:
    """Handle HTTP exceptions."""
    error_code = 408 if exc.detail == "凭证已过期" else exc.status_code
    
    return JSONResponse(
        ErrorResponse(
            error="HTTP_ERROR",
            msg=exc.detail,
            data={"status_code": exc.status_code},
            code=error_code
        ).model_dump(),
        status_code=exc.status_code,
        headers=exc.headers,
    )


async def http422_error_handler(_: Request, exc: Union[RequestValidationError, ValidationError]) -> JSONResponse:
    """Handle validation errors."""
    return JSONResponse(
        ErrorResponse(
            error="VALIDATION_ERROR",
            msg="数据校验错误",
            data={"errors": exc.errors()},
            code=status.HTTP_422_UNPROCESSABLE_ENTITY
        ).model_dump(),
        status_code=422,
    )


async def timeout_error_handler(req: Request, exc: Union[ServerTimeoutError, asyncio.TimeoutError]) -> JSONResponse:
    """Handle timeout errors."""
    if isinstance(exc, ServerTimeoutError):
        msg_head = "外部服务超时"
    elif isinstance(exc, asyncio.TimeoutError):
        msg_head = "操作超时"
    else:
        msg_head = "请求超时"

    logger.error(f"Timeout error: {msg_head} - {str(exc)}")

    return JSONResponse(
        ErrorResponse(
            error="TIMEOUT_ERROR",
            msg=f"{msg_head}: {str(exc)}",
            data={"timeout_type": type(exc).__name__},
            code=504
        ).model_dump(),
        status_code=status.HTTP_504_GATEWAY_TIMEOUT,
    )


async def client_error_handler(_: Request, exc: ClientError) -> JSONResponse:
    """Handle aiohttp client errors."""
    logger.error(f"HTTP client error: {str(exc)}")
    
    return JSONResponse(
        ErrorResponse(
            error="CLIENT_ERROR",
            msg="客户端请求错误",
            data={"error_type": type(exc).__name__},
            code=400
        ).model_dump(),
        status_code=400,
    )


async def general_handler(req: Request, exc: Exception) -> JSONResponse:
    """Handle all other exceptions not caught by specific handlers."""
    logger.exception(f"Unhandled exception: {type(exc).__name__}: {str(exc)}")
    
    if settings.debug:
        msg = f"内部错误: {str(exc)}"
        data = {
            "exception_type": type(exc).__name__,
            "exception_details": str(exc)
        }
    else:
        msg = "内部服务器错误"
        data = {"exception_type": type(exc).__name__}

    return JSONResponse(
        ErrorResponse(
            error="INTERNAL_SERVER_ERROR",
            msg=msg,
            data=data,
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        ).model_dump(),
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
    )
