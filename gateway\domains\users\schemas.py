"""
User Management API Schemas.

Request and response schemas for user-related endpoints:
- User profile management
- User discovery and search
- User identification and resolution
- HeyWhale platform integration
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from libs.schemas.api.base  import BaseDataListResponse

# ——— Request Schemas ———

class UserDiscoveryRequest(BaseModel):
    """Request schema for user discovery by various criteria."""

    register_start_date: str = Field(
        ..., description="Registration start date (YYYY-MM-DD)"
    )
    locations: Optional[List[str]] = Field(None, description="Filter by locations")
    universities: Optional[List[str]] = Field(
        None, description="Filter by universities"
    )
    majors: Optional[List[str]] = Field(None, description="Filter by majors")
    identity: Optional[List[str]] = Field(None, description="Filter by identity types")
    experiences: Optional[List[str]] = Field(
        None, description="Filter by experience levels"
    )


class UserSearchRequest(BaseModel):
    """Request schema for user search operations."""

    query: str = Field(..., description="Search query")
    search_type: str = Field(
        "name", description="Search type (name, email, phone, university)"
    )
    limit: int = Field(20, ge=1, le=1000, description="Number of users to return")
    offset: int = Field(0, ge=0, description="Number of users to skip")


class UserIdResolutionRequest(BaseModel):
    """Request schema for resolving user IDs by identifier."""

    identifiers: List[str] = Field(..., description="List of identifiers to resolve")
    identifier_type: str = Field(
        ..., description="Type of identifier (phone, email, user_id, external_id)"
    )


# ——— Response Schemas ———
# 用户的信息表，分为两种：
# 1. 动态数据，包含了最后一次登录的地点，时间，以及用户标签
# 2. 静态数据，包含了用户的基本信息，如用户名，邮箱，手机号，学校，专业，身份，经验等


class UserBasicProfile(BaseModel):
    """Response schema for user basic profile data."""

    user_id: str
    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    identity: Optional[str] = None
    join_date: Optional[datetime] = None
    
class UserDynamicProfile(UserBasicProfile):
    """Response schema for user dynamic profile data."""

    user_id: str
    last_login_location: Optional[str] = None
    last_login_time: Optional[datetime] = None
    
class UserStudentProfile(UserBasicProfile):
    """Response schema for user student profile data."""

    university: Optional[str] = None
    major: Optional[str] = None
    grade: Optional[str] = None
    degree: Optional[str] = None
    
class UserRichProfile(UserDynamicProfile):
    """Response schema for user rich profile data."""

    experience: Optional[str] = None
    tags: Optional[List[str]] = None
    # 用户所属的组织（学校 or 公司）
    organization: Optional[str] = None
    # 用户的职位（职位 or 年级）
    position: Optional[str] = None
    # 用户的专业（学校专业 or 研究领域）
    field:Optional[str] = None
    

# ——— List Response Schemas ———


class UserProfileListResponse(BaseDataListResponse):
    """Response schema for user profile list."""

    data: List[UserBasicProfile | 
               UserDynamicProfile | 
               UserStudentProfile | 
               UserRichProfile] = Field(...,
                                        description="List of user profile data",
                                        summary="List of user profile data")




# ——— Export schemas ———
__all__ = [
    # List request schemas
    "UserDiscoveryRequest",
    "UserSearchRequest",
    "UserIdResolutionRequest",
    # 单个用户信息
    "UserBasicProfile",
    "UserDynamicProfile",
    "UserStudentProfile",
    "UserRichProfile", 
    # List response schemas
    "UserProfileListResponse",
]
