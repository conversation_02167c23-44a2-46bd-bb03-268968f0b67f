import streamlit as st
import datetime
from libs.api import get_register_info, get_competition_user_by_identity
import pandas as pd

st.title("比赛相关")

if "data" not in st.session_state:
    st.session_state.data = ""


@st.cache_data
def convert_df(df):
    # IMPORTANT: Cache the conversion to prevent computation on every rerun
    return df.to_csv(index=False).encode("utf-8-sig")


@st.experimental_dialog("开始下载")
def download_data():
    st.write("即将下载【{}】的数据，点击确认".format("比赛"))
    st.download_button(
        label="下载",
        data=st.session_state.data,
        file_name="register_info.csv",
        mime="text/csv",
        use_container_width=True,
    )


with st.expander("根据比赛类型筛选用户"):
    competition_type_mapping = {
        "数据分析赛": "DATA_ANALYSIS",
        "算法赛": "ALGORITHM",
        "方案赛": "SCHEME",
    }
    competition_type = st.selectbox("比赛类型", options=competition_type_mapping.keys())
    competition_type_value = competition_type_mapping[competition_type]
    register_start_date = st.date_input(
        "日期",
        value=datetime.datetime.now().date() - datetime.timedelta(days=90),
        help="选择日期后，只会提供日期之后的数据",
    )
    if st.button("查询", key="query_competition_type"):
        response = get_register_info(
            {
                "competition_type": competition_type_value,
                "register_start_date": register_start_date.strftime("%Y-%m-%d"),
            }
        )
        st.session_state.data = convert_df(pd.DataFrame(response.json()["data"]))
        download_data()

with st.expander("根据用户身份筛选比赛用户"):
    identity_mapping = {
        "学生": "student",
        "在职": "employed",
    }
    identity_type = st.selectbox("用户类型", options=identity_mapping.keys())
    register_start_date = st.date_input(
        "日期",
        value=datetime.datetime.now().date() - datetime.timedelta(days=90),
        help="选择日期后，只会提供日期之后的数据",
        key="sd_identity",
    )
    if st.button("查询", key="query_identity"):
        response = get_competition_user_by_identity(
            {
                "identity": identity_type,
                "register_start_date": register_start_date.strftime("%Y-%m-%d"),
            }
        )
        st.session_state.data = convert_df(pd.DataFrame(response.json()["data"]))
        download_data()
