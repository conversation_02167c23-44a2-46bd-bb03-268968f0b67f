import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// Import views
const Login = () => import('@/views/auth/Login.vue')
const HomePage = () => import('@/views/home/<USER>')
const Dashboard = () => import('@/views/dashboard/Dashboard.vue')
const Layout = () => import('@/components/layout/Layout.vue')

const routes = [
  {
    path: '/login',
    name: 'login',
    component: Login,
    meta: {
      requiresGuest: true,
      title: 'Login'
    }
  },

  {
    path: '/',
    component: Layout,
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'home',
        component: HomePage,
        meta: {
          title: 'Home'
        }
      },
      {
        path: 'dashboard',
        name: 'dashboard',
        component: Dashboard,
        meta: {
          title: 'Dashboard'
        }
      },
      {
        path: 'competitions',
        name: 'competitions',
        component: () => import('@/views/competitions/CompetitionList.vue'),
        meta: {
          title: 'Competitions'
        }
      },
      {
        path: 'competitions/create',
        name: 'competition-create',
        component: () => import('@/views/competitions/CompetitionCreate.vue'),
        meta: {
          title: 'Create Competition'
        }
      },
      {
        path: 'competitions/:id/qualifications',
        name: 'competition-qualifications',
        component: () => import('@/views/competitions/CompetitionDetail.vue'),
        meta: {
          title: 'Competition Qualifications'
        }
      },
      {
        path: 'competitions/:id/edit',
        name: 'competition-edit',
        component: () => import('@/views/competitions/CompetitionCreate.vue'),
        meta: {
          title: 'Edit Competition'
        }
      },
      {
        path: 'competitions/:competitionId/qualifications/create',
        name: 'qualification-create',
        component: () => import('@/views/competitions/QualificationForm.vue'),
        meta: {
          title: 'Create Qualification'
        }
      },
      {
        path: 'competitions/:competitionId/qualifications/:qualificationId/edit',
        name: 'qualification-edit',
        component: () => import('@/views/competitions/QualificationForm.vue'),
        meta: {
          title: 'Edit Qualification'
        }
      },
      {
        path: 'credits',
        name: 'credits',
        component: () => import('@/views/credits/CreditManagement.vue'),
        meta: {
          title: 'Credit Management'
        }
      },
      {
        path: 'qualifications',
        name: 'qualifications',
        component: () => import('@/views/qualifications/QualificationList.vue'),
        meta: {
          title: 'Qualification Management'
        }
      },
      {
        path: 'schools',
        name: 'schools',
        component: () => import('@/views/schools/SchoolStatistics.vue'),
        meta: {
          title: 'School Statistics'
        }
      },
      {
        path: 'admin',
        name: 'admin',
        component: () => import('@/views/admin/AdminTools.vue'),
        meta: {
          title: 'Admin Tools'
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('@/views/error/NotFound.vue'),
    meta: {
      title: 'Page Not Found'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(_, __, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// Navigation guards
router.beforeEach(async (to, _, next) => {
  const authStore = useAuthStore()

  // Set page title
  document.title = to.meta.title ? `${to.meta.title} - Community Services Admin` : 'Community Services Admin'

  // Initialize auth state from stored tokens if needed
  if (!authStore.isLoggedIn && !authStore.loading) {
    try {
      await authStore.initializeAuth()
    } catch (error) {
      console.error('Auth initialization failed:', error)
    }
  }

  // Check authentication requirements - simplified for admin-only system
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      next({ name: 'login', query: { redirect: to.fullPath } })
      return
    }
    // All authenticated users have full access in this admin-only system
  } else if (to.meta.requiresGuest && authStore.isAuthenticated) {
    // Redirect authenticated users away from guest-only pages
    const redirectPath = to.query.redirect || '/dashboard'
    next(redirectPath)
    return
  }

  next()
})

// Global error handler for navigation
router.onError((error) => {
  console.error('Router navigation error:', error)

  // If it's an authentication error, redirect to login
  if (error.message?.includes('auth') || error.message?.includes('token')) {
    const authStore = useAuthStore()
    authStore.logout()
    router.push({ name: 'login' })
  }
})

export default router
