"""
Shared Integration Router.

Handles generic integration endpoints that don't belong to specific domains:
- File upload and processing services
- Webhook management and execution
- Integration health monitoring and configuration
- Cross-integration synchronization
"""

from fastapi import APIRouter, HTTPException, Query, Body, Depends, UploadFile, File
from typing import Optional
import logging
from datetime import datetime

from .schemas import (
    FileProcessingRequest,
    WebhookRequest,
)
from .dependencies import (
    require_integration_access,
    require_file_upload_permission,
    require_webhook_permission,
    require_integration_health_permission,
    validate_integration_type,
    validate_file_type,
    validate_webhook_url,
    get_file_upload_service,
    get_webhook_service,
    process_uploaded_file,
    execute_webhook,
)
from libs.schemas.api.responses import success
from libs.validation import validate_pagination

logger = logging.getLogger(__name__)

# Create router with prefix for shared integration endpoints
router = APIRouter(
    prefix="/integrations",
    tags=["integrations"],
    responses={404: {"description": "Not found"}},
)


# ——— File Upload endpoints ———


@router.post(
    "/files/upload",
    summary="Upload file",
    description="Upload file for processing by integration services",
)
async def upload_file(
    file: UploadFile = File(...),
    destination: str = Query(..., description="Upload destination"),
    user_id: str = Depends(require_file_upload_permission),
    service=Depends(get_file_upload_service),
):
    """Upload file for integration processing."""
    try:
        validate_integration_type(destination)
        validate_file_type(file.content_type or "")

        result = await process_uploaded_file(file, file.content_type or "")

        if result.get("error"):
            raise HTTPException(
                status_code=500, detail=f"File upload failed: {result['error']}"
            )

        response_data = {
            "success": True,
            "file_id": result.get("file_id", ""),
            "file_name": file.filename or "",
            "file_size": result.get("file_size", 0),
            "upload_url": result.get("upload_url"),
            "status": "uploaded",
            "message": "File uploaded successfully",
        }

        return success(data=response_data)
    except Exception as e:
        logger.error(f"Error uploading file: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post(
    "/files/{file_id}/process",
    summary="Process uploaded file",
    description="Process an uploaded file with specified parameters",
)
async def process_file(
    file_id: str,
    processing_data: FileProcessingRequest = Body(...),
    user_id: str = Depends(require_file_upload_permission),
    service=Depends(get_file_upload_service),
):
    """Process an uploaded file."""
    try:
        # TODO: Implement file processing logic
        response_data = {
            "success": True,
            "file_id": file_id,
            "processing_type": processing_data.processing_type,
            "status": "processing",
            "message": "File processing started",
        }

        return success(data=response_data)
    except Exception as e:
        logger.error(f"Error processing file {file_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# ——— Webhook endpoints ———


@router.post(
    "/webhooks/execute",
    summary="Execute webhook",
    description="Execute a webhook with specified payload",
)
async def execute_webhook_endpoint(
    webhook_data: WebhookRequest = Body(...),
    user_id: str = Depends(require_webhook_permission),
    service=Depends(get_webhook_service),
):
    """Execute a webhook."""
    try:
        validate_webhook_url(webhook_data.url)

        result = await execute_webhook(
            url=webhook_data.url,
            payload=webhook_data.payload,
            headers=webhook_data.headers,
            method=webhook_data.method,
        )

        if result.get("error"):
            raise HTTPException(
                status_code=500, detail=f"Webhook execution failed: {result['error']}"
            )

        response_data = {
            "success": True,
            "webhook_id": result.get("webhook_id", ""),
            "status_code": result.get("status_code"),
            "response_body": result.get("response_body"),
            "execution_time_ms": result.get("execution_time_ms", 0),
            "retry_count": webhook_data.retry_count or 0,
        }

        return success(data=response_data)
    except Exception as e:
        logger.error(f"Error executing webhook: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# ——— Health and monitoring endpoints ———


@router.get(
    "/health",
    summary="Check integration health",
    description="Check health status of integration services",
)
async def check_integration_health(
    integration_type: Optional[str] = Query(
        None, description="Specific integration to check"
    ),
    include_details: bool = Query(
        False, description="Include detailed health information"
    ),
    user_id: str = Depends(require_integration_health_permission),
):
    """Check integration health status."""
    try:
        from .dependencies import (
            check_shence_health,
            check_heywhale_health,
        )

        health_results = {}

        if not integration_type or integration_type == "shence":
            health_results["shence"] = await check_shence_health()

        if not integration_type or integration_type == "heywhale":
            health_results["heywhale"] = await check_heywhale_health()

        # Determine overall health status
        overall_status = "healthy"
        for service, result in health_results.items():
            if result.get("status") != "healthy":
                overall_status = "unhealthy"
                break

        response_data = {
            "status": overall_status,
            "services": health_results if include_details else None,
            "checked_at": datetime.now().isoformat(),
        }

        return success(data=response_data)
    except Exception as e:
        logger.error(f"Error checking integration health: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# ——— Synchronization endpoints ———


@router.post(
    "/sync",
    summary="Manual integration sync",
    description="Manually trigger synchronization for integration services",
)
async def manual_integration_sync(
    sync_data: dict  = Body(...),
    user_id: str = Depends(require_integration_access),
):
    """Manually trigger integration synchronization."""
    try:
        validate_integration_type(sync_data.integration_type)

        # TODO: Implement actual sync logic based on integration type
        response_data = {
            "sync_id": f"sync_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "integration_type": sync_data.integration_type,
            "status": "pending",
            "started_at": datetime.now(),
            "message": f"Sync started for {sync_data.integration_type}",
        }

        return success(data=response_data)
    except Exception as e:
        logger.error(f"Error starting integration sync: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get(
    "/sync/history",
    summary="Get sync history",
    description="Get synchronization history for integration services",
)
async def get_sync_history(
    integration_type: Optional[str] = Query(
        None, description="Filter by integration type"
    ),
    limit: int = Query(20, ge=1, le=100, description="Number of records to return"),
    offset: int = Query(0, ge=0, description="Number of records to skip"),
    user_id: str = Depends(require_integration_access),
):
    """Get integration synchronization history."""
    try:
        validate_pagination(limit, offset)

        if integration_type:
            validate_integration_type(integration_type)

        # TODO: Implement actual sync history retrieval
        response_data = {
            "sync_records": [],
            "total_count": 0,
            "success_rate": 100.0,
            "last_sync": None,
        }

        return success(data=response_data)
    except Exception as e:
        logger.error(f"Error getting sync history: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# ——— Configuration endpoints ———


@router.get(
    "/config/{integration_type}",
    summary="Get integration configuration",
    description="Get configuration for a specific integration",
)
async def get_integration_config(
    integration_type: str,
    user_id: str = Depends(require_integration_access),
):
    """Get integration configuration."""
    try:
        validate_integration_type(integration_type)

        # TODO: Implement actual config retrieval
        response_data = {
            "integration_type": integration_type,
            "is_enabled": True,
            "configuration": {},
            "last_updated": datetime.now(),
            "health_status": "healthy",
        }

        return success(data=response_data)
    except Exception as e:
        logger.error(f"Error getting integration config: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# ——— Legacy endpoints ———


@router.post(
    "/legacy/badges/sync",
    summary="[LEGACY] Sync badges",
    description="Legacy endpoint for badge synchronization - use domain-specific endpoints instead",
    deprecated=True,
)
async def legacy_sync_badges(
    users_payload: list = Body(..., description="User badge data payload"),
    user_id: str = Depends(require_integration_access),
):
    """Legacy badge sync endpoint - redirects to users domain."""
    raise HTTPException(
        status_code=410,
        detail="This endpoint has been moved to /users/integrations/heywhale/badges/sync",
    )
