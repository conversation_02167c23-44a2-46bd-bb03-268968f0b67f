import axios from 'axios'
import Cookies from 'js-cookie'
import { ElMessage } from 'element-plus'
import { mockApiInterceptor, isMockEnabled } from './mockApiHandler.js'
import { retryApiCall, isDevelopment } from '../utils/apiHelpers.js'

// API Configuration
const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api/v1',
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 30000,
  retryAttempts: parseInt(import.meta.env.VITE_RETRY_ATTEMPTS) || 3,
  retryDelay: 1000
}

// Create axios instance
const api = axios.create({
  baseURL: API_CONFIG.baseURL,
  timeout: API_CONFIG.timeout,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
})

// Request interceptor to add auth token and handle mock API
api.interceptors.request.use(
  (config) => {
    // Add authentication token
    const token = Cookies.get('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // Add request ID for tracking
    config.headers['X-Request-ID'] = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Add environment info in development
    if (isDevelopment()) {
      config.headers['X-Environment'] = import.meta.env.VITE_APP_ENV || 'development'
    }

    // Apply mock interceptor if enabled
    if (isMockEnabled()) {
      return mockApiInterceptor.request(config)
    }

    // Log request in development
    if (isDevelopment() && import.meta.env.VITE_DEBUG === 'true') {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {
        headers: config.headers,
        data: config.data
      })
    }

    return config
  },
  (error) => {
    console.error('❌ Request interceptor error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor for error handling and mock API
api.interceptors.response.use(
  (response) => {
    // Log response in development
    if (isDevelopment() && import.meta.env.VITE_DEBUG === 'true') {
      console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        data: response.data
      })
    }

    return response
  },
  async (error) => {
    // Try mock API first if enabled
    if (isMockEnabled() && error.config && error.config._isMockRequest) {
      try {
        const mockResponse = await mockApiInterceptor.response(error.config)
        return mockResponse
      } catch (mockError) {
        // If mock fails, continue with normal error handling
        error = mockError
      }
    }

    const { response, config } = error

    // Log error in development
    if (isDevelopment()) {
      console.error(`❌ API Error: ${config?.method?.toUpperCase()} ${config?.url}`, {
        status: response?.status,
        message: error.message,
        data: response?.data
      })
    }

    // Handle retry logic for specific errors
    const shouldRetry = (
      !response || // Network error
      response.status === 408 || // Request timeout
      response.status === 429 || // Too many requests
      response.status >= 500 // Server errors
    )

    if (shouldRetry && !config._retryCount) {
      config._retryCount = 0
    }

    if (shouldRetry && config._retryCount < API_CONFIG.retryAttempts) {
      config._retryCount += 1

      // Calculate delay with exponential backoff
      const delay = API_CONFIG.retryDelay * Math.pow(2, config._retryCount - 1)

      if (isDevelopment()) {
        console.log(`🔄 Retrying request (${config._retryCount}/${API_CONFIG.retryAttempts}) after ${delay}ms`)
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay))

      // Retry the request
      return api(config)
    }

    // Handle specific error responses
    if (response) {
      switch (response.status) {
        case 401:
          // Unauthorized - clear token and redirect to login
          Cookies.remove('auth_token')
          localStorage.removeItem('user_data')
          ElMessage.error('Session expired. Please login again.')
          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
          }
          break
        case 403:
          ElMessage.error('Access denied. You do not have permission to perform this action.')
          break
        case 404:
          ElMessage.error('Resource not found.')
          break
        case 408:
          ElMessage.error('Request timed out. Please try again.')
          break
        case 422:
          // Validation errors
          const message = response.data?.error?.message || response.data?.message || 'Validation error'
          ElMessage.error(message)
          break
        case 429:
          ElMessage.error('Too many requests. Please wait a moment and try again.')
          break
        case 500:
          ElMessage.error('Internal server error. Please try again later.')
          break
        case 502:
        case 503:
        case 504:
          ElMessage.error('Service temporarily unavailable. Please try again later.')
          break
        default:
          const errorMessage = response.data?.error?.message || response.data?.message || 'An error occurred'
          ElMessage.error(errorMessage)
      }
    } else if (error.request) {
      // Network error
      ElMessage.error('Network error. Please check your connection.')
    } else {
      // Other error
      ElMessage.error('An unexpected error occurred.')
    }

    return Promise.reject(error)
  }
)

// Create API instance with retry wrapper
const createApiWithRetry = (apiInstance) => {
  const wrappedApi = {}

  // Wrap common HTTP methods with retry logic
  const methods = ['get', 'post', 'put', 'patch', 'delete']

  methods.forEach(method => {
    wrappedApi[method] = async (...args) => {
      return retryApiCall(() => apiInstance[method](...args), API_CONFIG.retryAttempts, API_CONFIG.retryDelay)
    }
  })

  // Copy other properties
  Object.keys(apiInstance).forEach(key => {
    if (!methods.includes(key)) {
      wrappedApi[key] = apiInstance[key]
    }
  })

  return wrappedApi
}

// Export both the raw api and the wrapped version
export { api }
export default api

// Export configuration for use in other modules
export { API_CONFIG }
