/**
 * Feature Flags System
 * Provides runtime feature toggling and gradual rollout capabilities
 */

import { envConfig } from './envConfig.js'

/**
 * Feature flag definitions
 */
const FEATURE_FLAGS = {
  // Core Features
  ANALYTICS: 'analytics',
  COMPETITIONS: 'competitions',
  CREDITS: 'credits',
  SCHOOLS: 'schools',
  
  // API Features
  REAL_API: 'realApi',
  MOCK_API: 'mockApi',
  API_CACHING: 'apiCaching',
  API_RETRY: 'apiRetry',
  
  // UI Features
  DARK_MODE: 'darkMode',
  ADVANCED_SEARCH: 'advancedSearch',
  BULK_OPERATIONS: 'bulkOperations',
  EXPORT_FEATURES: 'exportFeatures',
  
  // Performance Features
  LAZY_LOADING: 'lazyLoading',
  VIRTUAL_SCROLLING: 'virtualScrolling',
  IMAGE_OPTIMIZATION: 'imageOptimization',
  
  // Security Features
  TWO_FACTOR_AUTH: 'twoFactorAuth',
  SESSION_TIMEOUT: 'sessionTimeout',
  AUDIT_LOGGING: 'auditLogging',
  
  // Experimental Features
  NEW_DASHBOARD: 'newDashboard',
  BETA_FEATURES: 'betaFeatures',
  EXPERIMENTAL_UI: 'experimentalUi'
}

/**
 * Default feature flag configuration
 */
const DEFAULT_FLAGS = {
  // Core Features (from environment)
  [FEATURE_FLAGS.ANALYTICS]: envConfig.features.analytics,
  [FEATURE_FLAGS.COMPETITIONS]: envConfig.features.competitions,
  [FEATURE_FLAGS.CREDITS]: envConfig.features.credits,
  [FEATURE_FLAGS.SCHOOLS]: envConfig.features.schools,
  
  // API Features
  [FEATURE_FLAGS.REAL_API]: !envConfig.mock.enabled,
  [FEATURE_FLAGS.MOCK_API]: envConfig.mock.enabled,
  [FEATURE_FLAGS.API_CACHING]: envConfig.cache.enabled,
  [FEATURE_FLAGS.API_RETRY]: true,
  
  // UI Features
  [FEATURE_FLAGS.DARK_MODE]: true,
  [FEATURE_FLAGS.ADVANCED_SEARCH]: true,
  [FEATURE_FLAGS.BULK_OPERATIONS]: true,
  [FEATURE_FLAGS.EXPORT_FEATURES]: true,
  
  // Performance Features
  [FEATURE_FLAGS.LAZY_LOADING]: true,
  [FEATURE_FLAGS.VIRTUAL_SCROLLING]: false,
  [FEATURE_FLAGS.IMAGE_OPTIMIZATION]: true,
  
  // Security Features
  [FEATURE_FLAGS.TWO_FACTOR_AUTH]: envConfig.security.csrfProtection,
  [FEATURE_FLAGS.SESSION_TIMEOUT]: true,
  [FEATURE_FLAGS.AUDIT_LOGGING]: envConfig.errorReporting.enabled,
  
  // Experimental Features
  [FEATURE_FLAGS.NEW_DASHBOARD]: false,
  [FEATURE_FLAGS.BETA_FEATURES]: envConfig.app.env === 'development',
  [FEATURE_FLAGS.EXPERIMENTAL_UI]: envConfig.app.env === 'development'
}

/**
 * Feature flag store
 */
class FeatureFlagStore {
  constructor() {
    this.flags = { ...DEFAULT_FLAGS }
    this.listeners = new Map()
    this.loadFromStorage()
  }

  /**
   * Load feature flags from localStorage (development only)
   */
  loadFromStorage() {
    if (envConfig.app.env === 'development') {
      try {
        const stored = localStorage.getItem('featureFlags')
        if (stored) {
          const parsedFlags = JSON.parse(stored)
          this.flags = { ...this.flags, ...parsedFlags }
        }
      } catch (error) {
        console.warn('Failed to load feature flags from storage:', error)
      }
    }
  }

  /**
   * Save feature flags to localStorage (development only)
   */
  saveToStorage() {
    if (envConfig.app.env === 'development') {
      try {
        localStorage.setItem('featureFlags', JSON.stringify(this.flags))
      } catch (error) {
        console.warn('Failed to save feature flags to storage:', error)
      }
    }
  }

  /**
   * Check if a feature is enabled
   * @param {string} flag - Feature flag name
   * @returns {boolean} Is feature enabled
   */
  isEnabled(flag) {
    return Boolean(this.flags[flag])
  }

  /**
   * Enable a feature flag
   * @param {string} flag - Feature flag name
   */
  enable(flag) {
    this.flags[flag] = true
    this.saveToStorage()
    this.notifyListeners(flag, true)
  }

  /**
   * Disable a feature flag
   * @param {string} flag - Feature flag name
   */
  disable(flag) {
    this.flags[flag] = false
    this.saveToStorage()
    this.notifyListeners(flag, false)
  }

  /**
   * Toggle a feature flag
   * @param {string} flag - Feature flag name
   */
  toggle(flag) {
    if (this.isEnabled(flag)) {
      this.disable(flag)
    } else {
      this.enable(flag)
    }
  }

  /**
   * Set multiple feature flags
   * @param {Object} flags - Object with flag names and values
   */
  setFlags(flags) {
    Object.entries(flags).forEach(([flag, value]) => {
      this.flags[flag] = Boolean(value)
      this.notifyListeners(flag, Boolean(value))
    })
    this.saveToStorage()
  }

  /**
   * Get all feature flags
   * @returns {Object} All feature flags
   */
  getAllFlags() {
    return { ...this.flags }
  }

  /**
   * Reset to default flags
   */
  reset() {
    this.flags = { ...DEFAULT_FLAGS }
    this.saveToStorage()
    Object.keys(this.flags).forEach(flag => {
      this.notifyListeners(flag, this.flags[flag])
    })
  }

  /**
   * Add listener for flag changes
   * @param {string} flag - Feature flag name
   * @param {Function} callback - Callback function
   */
  addListener(flag, callback) {
    if (!this.listeners.has(flag)) {
      this.listeners.set(flag, new Set())
    }
    this.listeners.get(flag).add(callback)
  }

  /**
   * Remove listener for flag changes
   * @param {string} flag - Feature flag name
   * @param {Function} callback - Callback function
   */
  removeListener(flag, callback) {
    if (this.listeners.has(flag)) {
      this.listeners.get(flag).delete(callback)
    }
  }

  /**
   * Notify listeners of flag changes
   * @param {string} flag - Feature flag name
   * @param {boolean} value - New flag value
   */
  notifyListeners(flag, value) {
    if (this.listeners.has(flag)) {
      this.listeners.get(flag).forEach(callback => {
        try {
          callback(value, flag)
        } catch (error) {
          console.error('Error in feature flag listener:', error)
        }
      })
    }
  }
}

// Create global feature flag store
const featureFlagStore = new FeatureFlagStore()

/**
 * Check if a feature is enabled
 * @param {string} flag - Feature flag name
 * @returns {boolean} Is feature enabled
 */
export const isFeatureEnabled = (flag) => {
  return featureFlagStore.isEnabled(flag)
}

/**
 * Enable a feature flag
 * @param {string} flag - Feature flag name
 */
export const enableFeature = (flag) => {
  featureFlagStore.enable(flag)
}

/**
 * Disable a feature flag
 * @param {string} flag - Feature flag name
 */
export const disableFeature = (flag) => {
  featureFlagStore.disable(flag)
}

/**
 * Toggle a feature flag
 * @param {string} flag - Feature flag name
 */
export const toggleFeature = (flag) => {
  featureFlagStore.toggle(flag)
}

/**
 * Get all feature flags
 * @returns {Object} All feature flags
 */
export const getAllFeatureFlags = () => {
  return featureFlagStore.getAllFlags()
}

/**
 * Add listener for flag changes
 * @param {string} flag - Feature flag name
 * @param {Function} callback - Callback function
 */
export const addFeatureFlagListener = (flag, callback) => {
  featureFlagStore.addListener(flag, callback)
}

/**
 * Remove listener for flag changes
 * @param {string} flag - Feature flag name
 * @param {Function} callback - Callback function
 */
export const removeFeatureFlagListener = (flag, callback) => {
  featureFlagStore.removeListener(flag, callback)
}

/**
 * Development helper to log all feature flags
 */
export const logFeatureFlags = () => {
  if (envConfig.debug.enabled) {
    console.group('🚩 Feature Flags')
    Object.entries(featureFlagStore.getAllFlags()).forEach(([flag, enabled]) => {
      console.log(`${flag}: ${enabled ? '✅' : '❌'}`)
    })
    console.groupEnd()
  }
}

// Export feature flag constants
export { FEATURE_FLAGS }

// Export store for advanced usage
export { featureFlagStore }

// Export default
export default {
  isEnabled: isFeatureEnabled,
  enable: enableFeature,
  disable: disableFeature,
  toggle: toggleFeature,
  getAll: getAllFeatureFlags,
  addListener: addFeatureFlagListener,
  removeListener: removeFeatureFlagListener,
  FEATURE_FLAGS
}
