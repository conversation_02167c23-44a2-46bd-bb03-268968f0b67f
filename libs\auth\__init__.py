"""
Authentication utilities for the library.

Contains authentication context, permission checking, and API authentication utilities.
"""

from .context import AuthContext, require_auth, create_auth_context, validate_user_id
from .authentication import (
    get_current_user_id,
    get_current_admin_id,
    get_current_user,
    require_authentication,
    require_admin_authentication,
    log_analytics_access,
    validate_jwt_token,
    get_api_key,
    require_api_key,
)
from .permissions import (
    BasePermissions,
    ResourcePermissions,
    RolePermissions,
    require_permission,
    require_role,
    require_any_role,
    require_resource_access,
    require_resource_ownership,
    check_resource_access,
    check_user_role,
    permission_required,
)

__all__ = [
    # Authentication context
    "AuthContext",
    "require_auth",
    "create_auth_context",
    "validate_user_id",
    
    # API authentication
    "get_current_user_id",
    "get_current_admin_id",
    "get_current_user",
    "require_authentication",
    "require_admin_authentication",
    "log_analytics_access",
    "validate_jwt_token",
    "get_api_key",
    "require_api_key",
    
    # Permissions
    "BasePermissions",
    "ResourcePermissions",
    "RolePermissions",
    "require_permission",
    "require_role",
    "require_any_role",
    "require_resource_access",
    "require_resource_ownership",
    "check_resource_access",
    "check_user_role",
    "permission_required",
] 