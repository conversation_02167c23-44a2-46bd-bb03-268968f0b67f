"""
Database configuration using modern Pydantic settings.

This module provides database connection management with:
- Tortoise ORM configuration
- Connection pooling
- Migration support
- Environment-based configuration
"""

from typing import Dict, Any
from tortoise import Tortoise
from tortoise.contrib.fastapi import register_tortoise
from fastapi import Fast<PERSON><PERSON>
from aerich.cli import Command
from os.path import join as os_join
import os

from core.config import settings, logger

# -----------------------数据库配置-----------------------------------

def get_tortoise_config() -> Dict[str, Any]:
    """
    Generate Tortoise ORM configuration from settings.

    Returns:
        Tortoise ORM configuration dictionary
    """
    # Use DATABASE_URL if available, otherwise fall back to individual settings
    if settings.database_url:
        # Fix PostgreSQL URL scheme for Tortoise ORM compatibility
        database_url = settings.database_url
        if database_url.startswith("postgresql://"):
            database_url = database_url.replace("postgresql://", "postgres://", 1)
        connection_config = database_url
    else:
        connection_config = {
            "engine": "tortoise.backends.asyncpg",
            "credentials": {
                "host": settings.database_host,
                "port": settings.database_port,
                "user": settings.database_user,
                "password": settings.database_password,
                "database": settings.database_name,
                "echo": settings.debug,  # Enable SQL logging in debug mode
            },
        }
    print(f"DEBUG_PRINT: Tortoise connection config: {connection_config}",flush=True)
    return {
        "connections": {"default": connection_config},
        "apps": {
            "models": {
                "models": ["libs.models.tables", "aerich.models"],
                "default_connection": "default",
            }
        },
        "use_tz": False,
        "timezone": settings.scheduler_timezone,
    }


async def init_database() -> None:
    """
    Initialize database connections and generate schemas.

    This function:
    1. Initializes Tortoise ORM with configuration
    2. Generates database schemas if they don't exist
    3. Logs the initialization process
    """
    try:
        logger.info("Initializing database connection...")

        config = get_tortoise_config()
        await Tortoise.init(config=config)

        logger.info("Generating database schemas...")
        await Tortoise.generate_schemas()

        logger.success("Database initialized successfully")

    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise


async def close_database() -> None:
    """
    Close database connections gracefully.
    """
    try:
        logger.info("Closing database connections...")
        await Tortoise.close_connections()
        logger.success("Database connections closed successfully")

    except Exception as e:
        logger.error(f"Error closing database connections: {e}")
        raise


def register_database(app: FastAPI) -> None:
    """
    Register Tortoise ORM with FastAPI application.

    Args:
        app: FastAPI application instance
    """
    config = get_tortoise_config()

    register_tortoise(
        app,
        config=config,
        generate_schemas=True,
        add_exception_handlers=True,
    )

    logger.info("Database registered with FastAPI application")

async def get_database():
    """
    Get the database connection.
    """
    conn = Tortoise.get_connection("default")
    print(f"DEBUG_PRINT: Database connection: {conn}",flush=True)
    return conn


# Legacy support - maintain compatibility with existing code
DB_ORM_CONFIG = get_tortoise_config()


async def generate_migrations(app_name: str = "models"):
    """
    用于生成数据库迁移文件的函数
    """
    # Initialize Tortoise ORM with the configuration
    await Tortoise.init(config=DB_ORM_CONFIG)

    # TODO: Adjust file_dir if migration location changes (e.g., root migrations/ or app/db/migrations)
    file_dir = "./migrations/"  # Default Aerich location, confirm if suitable
    app_migration_dir = os_join(file_dir, app_name)

    cmd = Command(tortoise_config=DB_ORM_CONFIG, app=app_name, location=file_dir)

    await (
        cmd.init()
    )  # Restore this call to initialize the Command object and Migrate class state

    # Check if the app's migration directory is empty (no .py files)
    if not os.path.exists(app_migration_dir) or not any(
        f.endswith(".py") for f in os.listdir(app_migration_dir)
    ):
        print(
            f"App migration directory '{app_migration_dir}' is empty or contains no .py files. Running init_db(safe=True)."
        )
        await cmd.init_db(
            safe=True
        )  # Attempt to create an empty initial migration if none exist

    print(f"Generating migration for app '{app_name}'...")
    migration_name = (
        input("Enter migration name (e.g., add_user_table): ")
        if os.environ.get("INTERACTIVE_MIGRATIONS", "0") == "1"
        else "auto_migration"
    )
    await cmd.migrate(name=migration_name)
    print(f"Migration {migration_name} generated for app '{app_name}'.")

    # Close Tortoise connections
    await Tortoise.close_connections()


async def apply_migrations(app_name: str = "models"):
    """
    根据migration下的迁移文件执行
    """
    file_dir = "./migrations/"
    cmd = Command(tortoise_config=DB_ORM_CONFIG, app=app_name, location=file_dir)
    await cmd.init()
    print(f"Applying migrations for app '{app_name}'...")
    await cmd.upgrade(run_in_transaction=True)
    print(f"Migrations applied for app '{app_name}'.")


# Renamed register_mysql to register_db for clarity
