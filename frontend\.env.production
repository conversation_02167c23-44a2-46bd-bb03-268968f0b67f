# Production API Configuration
VITE_API_BASE_URL=http://community-workers:8000/api/v1

# Mock API Configuration (disabled in production)
VITE_MOCK_API=false
VITE_MOCK_DELAY=0

# Application Configuration
VITE_APP_TITLE=Community Services Admin
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=production

# Debug Configuration (disabled in production)
VITE_DEBUG=false
VITE_LOG_LEVEL=error

# Feature Flags
VITE_FEATURE_ANALYTICS=true
VITE_FEATURE_COMPETITIONS=true
VITE_FEATURE_CREDITS=true
VITE_FEATURE_SCHOOLS=true

# Performance Configuration
VITE_API_TIMEOUT=30000
VITE_RETRY_ATTEMPTS=3

# Security Configuration
VITE_SECURE_COOKIES=true
VITE_CSRF_PROTECTION=true
VITE_HTTPS_ONLY=true

# Analytics Configuration
VITE_ENABLE_ANALYTICS=true
VITE_ANALYTICS_DEBUG=false

# Error Reporting
VITE_ERROR_REPORTING=true
VITE_ERROR_REPORTING_URL=http://community-error-service:8003/errors

# Cache Configuration
VITE_CACHE_ENABLED=true
VITE_CACHE_TTL=300000

# Build Configuration
VITE_BUILD_TIMESTAMP=${BUILD_TIMESTAMP}
VITE_BUILD_VERSION=${BUILD_VERSION}
VITE_GIT_COMMIT=${GIT_COMMIT}
