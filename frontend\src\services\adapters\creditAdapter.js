/**
 * Credit Data Adapter
 * Transforms credit and qualification data between API and frontend formats
 */

import { BaseAdapter, AdapterRegistry } from './BaseAdapter.js'

export class CreditAdapter extends BaseAdapter {
  /**
   * Transform credit record from API to frontend format
   * @param {Object} credit - Credit data from API
   * @param {string} type - Transformation type
   * @returns {Object} Transformed credit data
   */
  static transformItem(credit, type = 'default') {
    if (!credit) return null
    
    const base = super.transformItem(credit, type)
    
    switch (type) {
      case 'list':
        return {
          id: credit.id,
          userId: credit.user_id,
          userName: credit.user_name || 'Unknown User',
          competitionId: credit.competition_id,
          competitionName: credit.competition_name || 'Unknown Competition',
          qualificationId: credit.qual_id,
          qualificationName: credit.qualification_name || 'Unknown Qualification',
          credit: credit.credit || 0,
          remark: credit.remark || '',
          batchId: credit.batch_id,
          status: credit.status || 'active',
          // Display fields
          displayCredit: `${credit.credit || 0} credits`,
          displayStatus: this.formatStatus(credit.status),
          displayDate: this.formatDate(credit.created_at),
          // Dates
          awardedAt: credit.created_at,
          updatedAt: credit.updated_at,
          ...base
        }
      
      case 'detail':
        return {
          id: credit.id,
          userId: credit.user_id,
          userName: credit.user_name || 'Unknown User',
          userEmail: credit.user_email,
          competitionId: credit.competition_id,
          competitionName: credit.competition_name || 'Unknown Competition',
          qualificationId: credit.qual_id,
          qualificationName: credit.qualification_name || 'Unknown Qualification',
          credit: credit.credit || 0,
          remark: credit.remark || '',
          batchId: credit.batch_id,
          status: credit.status || 'active',
          // Enhanced display fields
          displayCredit: `${credit.credit || 0} credits`,
          displayStatus: this.formatStatus(credit.status),
          displayDate: this.formatDate(credit.created_at),
          displayDateTime: this.formatDateTime(credit.created_at),
          // Dates
          awardedAt: credit.created_at,
          updatedAt: credit.updated_at,
          // Additional info
          adminId: credit.admin_id,
          adminName: credit.admin_name,
          isRevokable: this.isRevokable(credit),
          ...base
        }
      
      case 'summary':
        return {
          id: credit.id,
          credit: credit.credit || 0,
          competitionName: credit.competition_name || 'Unknown Competition',
          qualificationName: credit.qualification_name || 'Unknown Qualification',
          awardedAt: credit.created_at,
          displayCredit: `${credit.credit || 0} credits`,
          displayDate: this.formatDate(credit.created_at)
        }
      
      default:
        return {
          id: credit.id,
          userId: credit.user_id,
          competitionId: credit.competition_id,
          qualificationId: credit.qual_id,
          credit: credit.credit || 0,
          remark: credit.remark || '',
          status: credit.status || 'active',
          awardedAt: credit.created_at,
          ...base
        }
    }
  }
  
  /**
   * Transform credit award request
   * @param {Object} data - Award request data
   * @returns {Object} Transformed request data
   */
  static transformAwardRequest(data) {
    return {
      user_ids: Array.isArray(data.userIds) ? data.userIds : [data.userIds],
      competition_id: data.competitionId,
      qual_id: data.qualificationId,
      credit: parseInt(data.credit) || 0,
      remark: data.remark || ''
    }
  }
  
  /**
   * Transform credit revoke request
   * @param {Object} data - Revoke request data
   * @returns {Object} Transformed request data
   */
  static transformRevokeRequest(data) {
    return {
      record_id: data.recordId || data.id
    }
  }
  
  /**
   * Transform credit statistics
   * @param {Object} stats - Credit statistics
   * @returns {Object} Transformed statistics
   */
  static transformStats(stats) {
    if (!stats) return null
    
    return {
      totalCredits: stats.total_credits || 0,
      totalRecords: stats.total_records || 0,
      totalUsers: stats.total_users || 0,
      totalCompetitions: stats.total_competitions || 0,
      averageCreditsPerUser: stats.average_credits_per_user || 0,
      averageCreditsPerCompetition: stats.average_credits_per_competition || 0,
      topUsers: stats.top_users || [],
      topCompetitions: stats.top_competitions || [],
      recentAwards: stats.recent_awards || [],
      lastUpdated: stats.last_updated || new Date().toISOString()
    }
  }
  
  /**
   * Format credit status for display
   * @param {string} status - Credit status
   * @returns {string} Formatted status
   */
  static formatStatus(status) {
    const statusMap = {
      'active': 'Active',
      'revoked': 'Revoked',
      'pending': 'Pending',
      'expired': 'Expired'
    }
    
    return statusMap[status] || 'Unknown'
  }
  
  /**
   * Format date for display
   * @param {string} dateString - ISO date string
   * @returns {string} Formatted date
   */
  static formatDate(dateString) {
    if (!dateString) return 'Unknown'
    
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString()
    } catch (error) {
      return 'Invalid Date'
    }
  }
  
  /**
   * Format date and time for display
   * @param {string} dateString - ISO date string
   * @returns {string} Formatted date and time
   */
  static formatDateTime(dateString) {
    if (!dateString) return 'Unknown'
    
    try {
      const date = new Date(dateString)
      return date.toLocaleString()
    } catch (error) {
      return 'Invalid Date'
    }
  }
  
  /**
   * Check if credit record can be revoked
   * @param {Object} credit - Credit record
   * @returns {boolean} True if revokable
   */
  static isRevokable(credit) {
    if (credit.status === 'revoked') return false
    
    // Check if within revocation period (e.g., 30 days)
    const awardedAt = new Date(credit.created_at || credit.awardedAt)
    const now = new Date()
    const daysSinceAwarded = (now - awardedAt) / (1000 * 60 * 60 * 24)
    
    return daysSinceAwarded <= 30 // 30 days revocation period
  }
  
  /**
   * Transform credit history response
   * @param {Object} response - API response
   * @param {Object} options - Transformation options
   * @returns {Object} Transformed response
   */
  static transform(response, options = {}) {
    const { type = 'list', isPaginated = true } = options
    
    if (isPaginated) {
      return this.transformPaginated(response, type)
    }
    
    if (Array.isArray(response.data)) {
      return {
        ...response,
        data: response.data.map(item => this.transformItem(item, type))
      }
    }
    
    return {
      ...response,
      data: this.transformItem(response.data, type)
    }
  }
  
  /**
   * Transform award response
   * @param {Object} response - Award response
   * @returns {Object} Transformed response
   */
  static transformAwardResponse(response) {
    return {
      success: response.success,
      data: {
        awarded: response.data?.awarded || 0,
        failed: response.data?.failed || 0,
        batchId: response.data?.batch_id,
        records: response.data?.records || []
      },
      message: response.message || 'Credits awarded successfully'
    }
  }
  
  /**
   * Transform revoke response
   * @param {Object} response - Revoke response
   * @returns {Object} Transformed response
   */
  static transformRevokeResponse(response) {
    return {
      success: response.success,
      data: {
        recordId: response.data?.record_id,
        revokedAt: response.data?.revoked_at || new Date().toISOString()
      },
      message: response.message || 'Credits revoked successfully'
    }
  }
}

// Register the adapter
AdapterRegistry.register('credit', CreditAdapter)

export { CreditAdapter as creditAdapter }
