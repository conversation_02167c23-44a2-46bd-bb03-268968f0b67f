from enum import Enum
from tortoise import fields
from tortoise.models import Model
from libs.models.base import TimestampMixin, VisibilityMixin, UserBasedMixin, SchoolBasedMixin, QualificationMixin, StatisticsMixin
from libs.models.base import NaiveDatetimeField
from bson import ObjectId

def gen_oid() -> str:
    return str(ObjectId())


class StatisticsCategory(str,Enum):
    """
    Statistics category model for tracking system-wide statistics.
    
    Contains aggregated data from various sources,
    including competitions, users, and system performance.
    """
    COMPETITION = "competition"
    USER = "user"
    SCHOOL = "school"
    ROUTE = "route"
    CREDIT = "credit"
    QUALIFICATION = "qualification"
    OTHER = "other" 

# 赛道
class Route(TimestampMixin,VisibilityMixin):
    """
    Route model for competition categorization.
    
    Defines different competition routes or categories
    for organizing competitions by type or domain.
    """
    
    route_id = fields.CharField(
        max_length=255, pk=True, description="赛道的 id(object_id)"
        ,default=gen_oid
    )
    route_name = fields.CharField(
        max_length=255, description="赛道的名称"
    )
    route_info = fields.CharField(
        max_length=255, null=True, description="赛道的信息"
    )

    class Meta:
        table = "routes"
        table_description = "Competition routes/categories"

# 活动
class Competition(TimestampMixin,VisibilityMixin):
    """
    Competition model for managing contests and challenges.
    
    Contains competition metadata, configuration, and
    relationships to routes and participants.
    
    Fields:
    competition_id: 活动 ID
    competition_name: 活动名称  
    start_date: 活动开始时间
    end_date: 活动结束时间
    route_id: 赛道 ID
    route_name: 赛道名称
    """
    
    record_id = fields.CharField(
        max_length=255, pk=True, description="Primary record identifier",default=gen_oid
    )
    competition_id = fields.CharField(
        max_length=255, db_index=True, description="活动 ID"
    )
    competition_name = fields.CharField(
        max_length=255, description="活动名称"
    )

    start_date = NaiveDatetimeField(description="活动开始时间",null=True)
    
    end_date = NaiveDatetimeField(description="活动结束时间",null=True)
    

    route_id = fields.CharField(
        max_length=255, description="赛道 ID"
    )
    route_name = fields.CharField(
        max_length=255, description="赛道名称"
    )
    class Meta:
        table = "competitions"
        table_description = "Competitions and challenges"
        
# 用户报名表
class UserRegistration(TimestampMixin,VisibilityMixin,UserBasedMixin,SchoolBasedMixin):
    """
    User model representing platform users.
    
    Contains user profile information, contact details,
    and associated competition/university data.
    """
    
    register_id = fields.CharField(
        max_length=255, pk=True, description="报名 ID"
    )
    register_ts = NaiveDatetimeField(
        description="报名时间",null=True
    )
    phone = fields.CharField(
        max_length=255, description="手机号",db_index=True,null=True
    )
    email = fields.CharField(
        max_length=255, description="邮箱",db_index=True,null=True
    )
    identity = fields.CharField(
        max_length=255, description="身份信息",db_index=True,null=True
    )
    competition_id = fields.CharField(
        max_length=255, null=True, description="活动 ID",db_index=True
    )
    competition_name = fields.CharField(
        max_length=255, null=True, description="活动名称"
    )


    class Meta:
        table = "user_registrations"
        table_description = "用户报名表"
    
# # 团队 - 队伍信息
class Teams(TimestampMixin,VisibilityMixin,UserBasedMixin):
    """
    Teams model for competition team management.
    
    Manages team formation, membership, and competition participation.
    """
    record_id = fields.CharField(
        max_length=255, pk=True, description="队伍 ID",default=gen_oid
    )   
    team_id = fields.CharField(
        max_length=255, description="队伍 ID",db_index=True
    )
    team_name = fields.CharField(
        max_length=255, null=True, description="队伍名称"
    )
    competition_id = fields.CharField(
        max_length=255, description="活动 ID",db_index=True
    )
    competition_name = fields.CharField(
        max_length=255, description="活动名称",db_index=True
    )


    class Meta:
        table = "teams"
        table_description = "Competition teams"


# 学分规则
class Qualification(TimestampMixin,VisibilityMixin):
    """
    Qualification model for defining achievement criteria.
    
    Specifies requirements and thresholds for earning
    qualifications in competitions.
    """
    
    qualification_id = fields.CharField(
        max_length=255, pk=True, description="学分规则 ID",default=gen_oid
    )
    qualification_name = fields.CharField(
        max_length=255, description="学分规则名称"
    )
    qualification_type = fields.IntField(
        description="学分规则类型",
        db_index=True,
        default=0
    )
    qualification_logic = fields.IntField(
        description="学分规则逻辑",
        db_index=True,
        default=0
    )
    score_threshold = fields.FloatField(
        null=True, description="学分规则分数阈值"
    )
    credit = fields.IntField(
        default=0, description="学分数量"
    )
    related_task_id = fields.CharField(
        max_length=255, null=True, description="相关任务 ID"
    )
    competition_id = fields.CharField(
        max_length=255, description="活动 ID",db_index=True
    )
    competition_name = fields.CharField(
        max_length=255, description="活动名称",db_index=True
    )

    class Meta:
        table = "qualifications"
        table_description = "Qualification criteria and requirements"

# 团队提交
class Submission(TimestampMixin,VisibilityMixin):
    """
    Submission model for tracking user submissions.
    
    Records all submissions made by users for competitions,
    including scores and evaluation results.
    """
    
    record_id = fields.CharField(
        max_length=255, pk=True, description="提交 ID"
    )
    work_id = fields.CharField(
        max_length=255, null=True, description="作品 ID",db_index=True
    )
    task_id = fields.CharField(
        max_length=255, description="任务 ID",db_index=True
    )
    score = fields.FloatField(
        null=True, description="提交分数"
    )
    submission_ts = NaiveDatetimeField(
        description="提交时间"
    )
    team_id = fields.CharField(
        max_length=255, description="队伍 ID",db_index=True
    )
    user_id = fields.CharField(
        max_length=255, description="用户 ID",db_index=True,null=True
    )
    competition_id = fields.CharField(
        max_length=255, description="活动 ID",db_index=True
    )

    class Meta:
        table = "submissions"
        table_description = "User submissions and results"

# 获得学分的用户
class QualifiedUser(UserBasedMixin,SchoolBasedMixin,QualificationMixin):
    """
    Model representing users who have met qualification criteria.
    
    This model tracks qualified users for specific competitions and
    maintains qualification history and metadata.
    """
    record_id = fields.CharField(
        max_length=255, pk=True, description="Primary record identifier",default=gen_oid
    )
    qualification_ts = NaiveDatetimeField(
        description="获得学分时间"
    )
    credit = fields.IntField(default=0, description="学分数量")
    class Meta:
        table = "users_qualified"
        table_description = "View of users who met qualification criteria"
        unique_together = (("user_id", "qualification_id"),)


# 管理员    
class Administrators(TimestampMixin,VisibilityMixin):
    """
    Administrator model for system administration.
    
    Manages administrative user accounts with enhanced
    privileges for system management.
    """
    
    email_address = fields.CharField(
        max_length=255, pk=True, description="Administrator email"
    )
    password = fields.CharField(
        max_length=255, description="Encrypted password"
    )
    user_name = fields.CharField(
        max_length=255, null=True, description="Administrator name"
    )
    last_login_ts = NaiveDatetimeField(
        null=True, description="Last login timestamp"
    )

    class Meta:
        table = "administrators"
        table_description = "System administrators"

# 管理员日志
class AdminLog(TimestampMixin,VisibilityMixin):
    """
    Admin log model for tracking system-wide statistics.
    Contains aggregated data from various sources,
    """     
    record_id = fields.CharField(
        max_length=255, pk=True, description="管理员日志 ID",default=gen_oid
    )
    admin_email = fields.CharField(
        max_length=255, description="管理员邮箱",db_index=True
    )
    action_type = fields.CharField(
        max_length=255, description="操作类型"
    )
    action_detail = fields.TextField(
        null=True, description="操作详情"
    )
    related_id = fields.CharField(
        max_length=255, description="相关 ID",db_index=True,null=True
    )
    class Meta:
        table = "admin_logs"

# 学分发放历史
class CreditHistory(UserBasedMixin,SchoolBasedMixin,QualificationMixin):
    """
    Credit history model for tracking credit transactions.
    
    Maintains a record of all credit-related transactions
    and changes for users across competitions.
    """
    
    record_id = fields.CharField(
        max_length=255, pk=True, description="学分记录 ID",default=gen_oid
    )
    credit = fields.IntField(
        description="学分数量"
    )
    batch_id = fields.CharField(
        max_length=255, description="批次 ID",db_index=True,null=True
    )
    remark = fields.TextField(
        null=True, description="备注"
    )
    class Meta:
        table = "credit_history"
        table_description = "Credit transaction history"


# 统计总表
class Statistics(StatisticsMixin):
    """
    Statistics model for tracking system-wide statistics.
    
    Contains aggregated data from various sources,
    including competitions, users, and system performance.
    """
    record_id = fields.CharField(
        max_length=255, pk=True, description="统计 ID",default=gen_oid
    )
    related_id = fields.CharField(  
        max_length=255, description="相关 ID",db_index=True,null=True
    )
    statistics_category: StatisticsCategory = fields.CharField(
        max_length=255, description="统计分类",db_index=True,null=True
    )
    class Meta:
        table = "statistics"
        table_description = "System-wide statistics"
