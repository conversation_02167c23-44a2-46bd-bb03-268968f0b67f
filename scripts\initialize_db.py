import asyncio
import sys
import os
import logging
import requests

# Ensure the project root is in the Python path for direct imports
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)],
)
logger = logging.getLogger(__name__)

# Import after sys.path is updated
from core.database.database import init_database, close_database
from core.database.mongo_db import connect_to_mongo, close_mongo_connection
from core.config.tunnel_manager import SSHTunnelManager
from core.config import settings

# Override settings for initialization script
settings.database_url = (
    "mysql://developer:VhUdQSBX8H3NmTA@**************:14306/heywhale"
)
settings.ssh_tunnel_enabled = True

# Instantiate the tunnel manager
tunnel_manager = SSHTunnelManager()


async def init_database_connections():
    """Initialize both Tortoise ORM and MongoDB connections"""
    # NOTE: SSH tunnel is started automatically by connect_to_mongo if enabled
    # and init_database will use the tunnel if it's for a remote DB.
    # The new structure assumes tunnel is for Mongo, so we connect to Mongo first.
    try:
        logger.info("Initializing MongoDB connection...")
        await connect_to_mongo()
        logger.info("MongoDB connection initialized successfully.")
    except Exception as e:
        logger.error(f"Failed to initialize MongoDB: {e}", exc_info=True)
        logger.warning("Continuing without MongoDB connection")

    # Initialize Tortoise ORM
    # The new init_database function reads from settings directly
    await init_database()


async def close_database_connections():
    """Close all database connections"""
    # Close Tortoise ORM connections
    await close_database()
    logger.info("Tortoise ORM connections closed")

    # Close MongoDB connection
    try:
        await close_mongo_connection()
        logger.info("MongoDB connection closed")
    except Exception as e:
        logger.error(f"Error closing MongoDB connection: {e}")

    # Close SSH tunnel if it was opened
    if settings.ssh_tunnel.enabled and tunnel_manager.is_active:
        await tunnel_manager.stop()
        logger.info("SSH tunnel closed")


async def initialize_database():
    """Initialize the database schema"""
    try:
        await init_database_connections()
        logger.info("Database initialization complete.")
        return {"status": "success", "message": "Database initialized successfully"}
    except Exception as e:
        logger.error(f"Database initialization failed: {e}", exc_info=True)
        return {"status": "error", "message": str(e)}
    finally:
        await close_database_connections()


# API endpoint for database initialization - keeping for reference
url = "http://localhost:8000/api/v1/qualification/database/initialize"


# Compatibility function to call REST API method instead of direct initialization (legacy)
def initialize_database_via_api():
    is_clean = True
    response = requests.post(url, json={"is_clean": is_clean})
    print(f"Calling {url} with data: {response.json()}")
    return response.json()


# Main execution block
if __name__ == "__main__":
    print("Starting database initialization...")
    try:
        # Run the async function
        result = asyncio.run(initialize_database())
        print(f"Database initialization result: {result}")
    except KeyboardInterrupt:
        print("Process interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"Error during database initialization: {e}")
        sys.exit(1)
