# Community Services Frontend

Vue 3 + Vite frontend application for the Community Services Administrative System.

## Features

- **Vue 3** with Composition API
- **Vite** for fast development and optimized builds
- **Element Plus** for comprehensive UI components
- **Pinia** for state management
- **Vue Router** for navigation
- **Axios** for API communication
- **Vitest** for testing
- **SCSS** for styling

## Project Structure

```
src/
├── components/          # Reusable components
│   └── layout/         # Layout components
├── views/              # Page components
│   ├── auth/           # Authentication pages
│   ├── dashboard/      # Dashboard
│   ├── competitions/   # Competition management
│   ├── credits/        # Credit management
│   ├── schools/        # School statistics
│   ├── admin/          # Admin tools
│   └── error/          # Error pages
├── stores/             # Pinia stores
├── services/           # API services
├── router/             # Vue Router configuration
├── utils/              # Utility functions
└── assets/             # Static assets
```

## Development

### Prerequisites

- Node.js >= 18.0.0
- npm >= 8.0.0

### Installation

```bash
cd frontend
npm install
```

### Development Server

```bash
npm run dev
```

The application will be available at `http://localhost:3000`

### Building for Production

```bash
npm run build
```

### Testing

```bash
# Run tests
npm run test

# Run tests with UI
npm run test:ui

# Run tests with coverage
npm run test:coverage
```

### Linting

```bash
npm run lint
```

## Environment Variables

Create `.env.local` file for local development:

```env
VITE_API_BASE_URL=http://localhost:5000
```

## API Integration

The frontend communicates with the backend API service. The API base URL is configured via environment variables.

### Authentication

- JWT token-based authentication
- Tokens stored in HTTP-only cookies
- Automatic token refresh
- Route guards for protected pages

### Error Handling

- Global error interceptors
- User-friendly error messages
- Automatic logout on authentication errors

## Phase 1 Implementation Status

### ✅ Completed
- [x] Project setup with Vite + Vue 3
- [x] Element Plus UI library integration
- [x] Pinia state management setup
- [x] Vue Router configuration
- [x] Axios API service setup
- [x] Authentication system (login/register)
- [x] Main layout with navigation
- [x] Dashboard with basic stats
- [x] Route guards for authentication
- [x] Basic test setup

### 🚧 In Progress
- [ ] Complete test coverage
- [ ] Error boundary implementation
- [ ] Loading states optimization

### 📋 Next Phase
- [ ] Competition management features
- [ ] Credit distribution system
- [ ] School statistics dashboard
- [ ] Data export functionality

## Contributing

1. Follow Vue 3 Composition API patterns
2. Use TypeScript for better development experience
3. Write tests for new components and features
4. Follow the established project structure
5. Use Element Plus components consistently

## Deployment

The application can be deployed as static files after building:

```bash
npm run build
```

The `dist/` directory contains the production-ready files.
