/**
 * Analytics API Service
 * Handles rankings, statistics, event tracking, and ShenCe analytics integration
 */

import { api } from '../api.js'
import { handleApiResponse, handleApiError, buildQueryString, logApiCall } from '../../utils/apiHelpers.js'

const BASE_PATH = '/analytics'

/**
 * Check analytics health status
 * @returns {Promise<Object>} Health status response
 */
export const getHealthStatus = async () => {
  try {
    const url = `${BASE_PATH}/health`
    
    logApiCall('GET', url, null, null)
    
    const response = await api.get(url)
    const result = handleApiResponse(response)
    
    logApiCall('GET', url, null, result)
    
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('GET', `${BASE_PATH}/health`, null, normalizedError)
    throw normalizedError
  }
}

/**
 * Get summary statistics
 * @returns {Promise<Object>} Summary statistics response
 */
export const getSummaryStatistics = async () => {
  try {
    const url = `${BASE_PATH}/camp-only/statistics/summary`
    
    logApiCall('POST', url, null, null)
    
    const response = await api.post(url)
    const result = handleApiResponse(response)
    
    logApiCall('POST', url, null, result)
    
    return {
      success: true,
      data: result.data.data || []
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('POST', `${BASE_PATH}/camp-only/statistics/summary`, null, normalizedError)
    throw normalizedError
  }
}

/**
 * Get school rankings
 * @param {Object} data - Pagination data
 * @param {number} data.page_size - Number of schools per page (default: 20)
 * @param {number} data.current_page - Current page number (default: 1)
 * @returns {Promise<Object>} School rankings response
 */
export const getSchoolRankings = async (data = {}) => {
  try {
    const requestData = {
      page_size: data.page_size || 20,
      current_page: data.current_page || 1
    }
    
    const url = `${BASE_PATH}/camp-only/rankings/schools`
    
    logApiCall('POST', url, requestData, null)
    
    const response = await api.post(url, requestData)
    const result = handleApiResponse(response)
    
    logApiCall('POST', url, requestData, result)
    
    return {
      success: true,
      data: result.data.data || {},
      total: result.data.data?.total || 0,
      pageSize: result.data.data?.page_size || 20,
      currentPage: result.data.data?.current_page || 1,
      schools: result.data.data?.res || []
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('POST', `${BASE_PATH}/camp-only/rankings/schools`, data, normalizedError)
    throw normalizedError
  }
}

/**
 * Get user rankings by route
 * @param {Object} data - Request data
 * @param {number} data.top_num - Number of top users to return (default: 30)
 * @returns {Promise<Object>} User rankings by route response
 */
export const getUserRankingsByRoute = async (data = {}) => {
  try {
    const requestData = {
      top_num: data.top_num || 30
    }
    
    const url = `${BASE_PATH}/camp-only/rankings/users/by_route`
    
    logApiCall('POST', url, requestData, null)
    
    const response = await api.post(url, requestData)
    const result = handleApiResponse(response)
    
    logApiCall('POST', url, requestData, result)
    
    return {
      success: true,
      data: result.data.data || []
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('POST', `${BASE_PATH}/camp-only/rankings/users/by_route`, data, normalizedError)
    throw normalizedError
  }
}

/**
 * Get total user rankings
 * @param {Object} data - Pagination data
 * @param {number} data.page_size - Number of users per page (default: 20)
 * @param {number} data.current_page - Current page number (default: 1)
 * @returns {Promise<Object>} Total user rankings response
 */
export const getTotalUserRankings = async (data = {}) => {
  try {
    const requestData = {
      page_size: data.page_size || 20,
      current_page: data.current_page || 1
    }
    
    const url = `${BASE_PATH}/camp-only/rankings/users/total`
    
    logApiCall('POST', url, requestData, null)
    
    const response = await api.post(url, requestData)
    const result = handleApiResponse(response)
    
    logApiCall('POST', url, requestData, result)
    
    return {
      success: true,
      data: result.data.data || {},
      total: result.data.data?.total || 0,
      pageSize: result.data.data?.page_size || 20,
      currentPage: result.data.data?.current_page || 1,
      users: result.data.data?.res || []
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('POST', `${BASE_PATH}/camp-only/rankings/users/total`, data, normalizedError)
    throw normalizedError
  }
}

/**
 * Get top users by route
 * @param {Object} data - Request data
 * @param {number} data.top_num - Number of top users to return (default: 10)
 * @returns {Promise<Object>} Top users by route response
 */
export const getTopUsersByRoute = async (data = {}) => {
  try {
    const requestData = {
      top_num: data.top_num || 10
    }
    
    const url = `${BASE_PATH}/camp-only/rankings/top_by_route`
    
    logApiCall('POST', url, requestData, null)
    
    const response = await api.post(url, requestData)
    const result = handleApiResponse(response)
    
    logApiCall('POST', url, requestData, result)
    
    return {
      success: true,
      data: result.data.data || []
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('POST', `${BASE_PATH}/camp-only/rankings/top_by_route`, data, normalizedError)
    throw normalizedError
  }
}

/**
 * Get event tracks
 * @returns {Promise<Object>} Event tracks response
 */
export const getEventTracks = async () => {
  try {
    const url = `${BASE_PATH}/events/tracks`
    
    logApiCall('GET', url, null, null)
    
    const response = await api.get(url)
    const result = handleApiResponse(response)
    
    logApiCall('GET', url, null, result)
    
    return {
      success: true,
      data: result.data.data || {},
      events: result.data.data?.events || [],
      total: result.data.data?.total || 0
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('GET', `${BASE_PATH}/events/tracks`, null, normalizedError)
    throw normalizedError
  }
}

/**
 * Get ShenCe directories
 * @returns {Promise<Object>} ShenCe directories response
 */
export const getShenceDirectories = async () => {
  try {
    const url = `${BASE_PATH}/integrations/shence/directories`
    
    logApiCall('GET', url, null, null)
    
    const response = await api.get(url)
    const result = handleApiResponse(response)
    
    logApiCall('GET', url, null, result)
    
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('GET', `${BASE_PATH}/integrations/shence/directories`, null, normalizedError)
    throw normalizedError
  }
}

/**
 * Get ShenCe tag metadata
 * @param {string} tagId - ShenCe tag ID
 * @returns {Promise<Object>} ShenCe tag metadata response
 */
export const getShenceTagMeta = async (tagId) => {
  try {
    const url = `${BASE_PATH}/integrations/shence/tags/${tagId}/meta`
    
    logApiCall('GET', url, null, null)
    
    const response = await api.get(url)
    const result = handleApiResponse(response)
    
    logApiCall('GET', url, null, result)
    
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('GET', `${BASE_PATH}/integrations/shence/tags/${tagId}/meta`, null, normalizedError)
    throw normalizedError
  }
}

// Export all functions as a service object
export const analyticsApi = {
  getHealthStatus,
  getSummaryStatistics,
  getSchoolRankings,
  getUserRankingsByRoute,
  getTotalUserRankings,
  getTopUsersByRoute,
  getEventTracks,
  getShenceDirectories,
  getShenceTagMeta
}
