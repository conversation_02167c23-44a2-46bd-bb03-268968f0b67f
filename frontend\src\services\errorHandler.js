/**
 * Global Error Handler
 * Centralized error handling for API calls and application errors
 */

import { ElMessage, ElNotification } from 'element-plus'
import { useRouter } from 'vue-router'

// Error types
export const ERROR_TYPES = {
  NETWORK: 'NETWORK_ERROR',
  AUTH: 'AUTH_ERROR',
  VALIDATION: 'VALIDATION_ERROR',
  SERVER: 'SERVER_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  FORBIDDEN: 'FORBIDDEN',
  RATE_LIMIT: 'RATE_LIMIT',
  TIMEOUT: 'TIMEOUT_ERROR',
  UNKNOWN: 'UNKNOWN_ERROR'
}

// Error severity levels
export const ERROR_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
}

/**
 * Classify error based on status code and type
 * @param {Object} error - Error object
 * @returns {Object} Error classification
 */
export const classifyError = (error) => {
  const status = error.response?.status || error.status || 0
  
  let type = ERROR_TYPES.UNKNOWN
  let severity = ERROR_SEVERITY.MEDIUM
  let shouldRetry = false
  let shouldRedirect = false
  let redirectPath = null

  // Classify by status code
  switch (status) {
    case 0:
      type = ERROR_TYPES.NETWORK
      severity = ERROR_SEVERITY.HIGH
      shouldRetry = true
      break
    
    case 400:
      type = ERROR_TYPES.VALIDATION
      severity = ERROR_SEVERITY.LOW
      break
    
    case 401:
      type = ERROR_TYPES.AUTH
      severity = ERROR_SEVERITY.HIGH
      shouldRedirect = true
      redirectPath = '/login'
      break
    
    case 403:
      type = ERROR_TYPES.FORBIDDEN
      severity = ERROR_SEVERITY.MEDIUM
      break
    
    case 404:
      type = ERROR_TYPES.NOT_FOUND
      severity = ERROR_SEVERITY.LOW
      break
    
    case 408:
      type = ERROR_TYPES.TIMEOUT
      severity = ERROR_SEVERITY.MEDIUM
      shouldRetry = true
      break
    
    case 429:
      type = ERROR_TYPES.RATE_LIMIT
      severity = ERROR_SEVERITY.MEDIUM
      shouldRetry = true
      break
    
    case 500:
    case 502:
    case 503:
    case 504:
      type = ERROR_TYPES.SERVER
      severity = ERROR_SEVERITY.HIGH
      shouldRetry = true
      break
    
    default:
      if (status >= 400 && status < 500) {
        type = ERROR_TYPES.VALIDATION
        severity = ERROR_SEVERITY.LOW
      } else if (status >= 500) {
        type = ERROR_TYPES.SERVER
        severity = ERROR_SEVERITY.HIGH
        shouldRetry = true
      }
  }

  return {
    type,
    severity,
    shouldRetry,
    shouldRedirect,
    redirectPath,
    status
  }
}

/**
 * Get user-friendly error message
 * @param {Object} error - Error object
 * @param {Object} classification - Error classification
 * @returns {string} User-friendly message
 */
export const getUserFriendlyMessage = (error, classification) => {
  const defaultMessages = {
    [ERROR_TYPES.NETWORK]: 'Network connection error. Please check your internet connection.',
    [ERROR_TYPES.AUTH]: 'Authentication failed. Please log in again.',
    [ERROR_TYPES.VALIDATION]: 'Invalid data provided. Please check your input.',
    [ERROR_TYPES.SERVER]: 'Server error occurred. Please try again later.',
    [ERROR_TYPES.NOT_FOUND]: 'The requested resource was not found.',
    [ERROR_TYPES.FORBIDDEN]: 'You do not have permission to perform this action.',
    [ERROR_TYPES.RATE_LIMIT]: 'Too many requests. Please wait a moment and try again.',
    [ERROR_TYPES.TIMEOUT]: 'Request timed out. Please try again.',
    [ERROR_TYPES.UNKNOWN]: 'An unexpected error occurred. Please try again.'
  }

  // Use custom error message if available
  const customMessage = error.error?.message || error.message
  if (customMessage && customMessage !== 'Network Error') {
    return customMessage
  }

  return defaultMessages[classification.type] || defaultMessages[ERROR_TYPES.UNKNOWN]
}

/**
 * Display error notification to user
 * @param {Object} error - Error object
 * @param {Object} options - Display options
 */
export const displayError = (error, options = {}) => {
  const classification = classifyError(error)
  const message = getUserFriendlyMessage(error, classification)
  
  const {
    showNotification = true,
    showMessage = false,
    duration = 4000,
    title = 'Error'
  } = options

  if (showNotification) {
    ElNotification({
      title,
      message,
      type: 'error',
      duration: classification.severity === ERROR_SEVERITY.CRITICAL ? 0 : duration,
      position: 'top-right'
    })
  }

  if (showMessage) {
    ElMessage({
      message,
      type: 'error',
      duration,
      showClose: true
    })
  }

  // Log error in development
  if (import.meta.env.DEV) {
    console.error('🚨 Error Handler:', {
      error,
      classification,
      message
    })
  }
}

/**
 * Handle authentication errors
 * @param {Object} error - Error object
 */
export const handleAuthError = (error) => {
  const router = useRouter()
  
  displayError(error, {
    title: 'Authentication Error',
    showNotification: true
  })

  // Clear auth data
  localStorage.removeItem('auth_token')
  localStorage.removeItem('user_data')
  
  // Redirect to login
  router.push('/login')
}

/**
 * Global error handler for unhandled errors
 * @param {Error} error - Error object
 * @param {Object} errorInfo - Additional error information
 */
export const globalErrorHandler = (error, errorInfo = {}) => {
  const classification = classifyError(error)
  
  // Handle critical errors
  if (classification.severity === ERROR_SEVERITY.CRITICAL) {
    displayError(error, {
      title: 'Critical Error',
      showNotification: true,
      duration: 0
    })
    
    // Log to external service in production
    if (import.meta.env.PROD) {
      // TODO: Integrate with error tracking service (Sentry, etc.)
      console.error('Critical error:', error, errorInfo)
    }
  } else {
    displayError(error)
  }

  // Handle redirects
  if (classification.shouldRedirect && classification.redirectPath) {
    const router = useRouter()
    router.push(classification.redirectPath)
  }
}

/**
 * Create error handler for specific contexts
 * @param {string} context - Error context (e.g., 'login', 'dashboard')
 * @param {Object} options - Handler options
 * @returns {Function} Error handler function
 */
export const createErrorHandler = (context, options = {}) => {
  return (error) => {
    const classification = classifyError(error)
    
    // Context-specific handling
    if (context === 'auth' && classification.type === ERROR_TYPES.AUTH) {
      handleAuthError(error)
      return
    }

    // Default handling
    displayError(error, {
      title: `${context} Error`,
      ...options
    })
  }
}

/**
 * Wrap async function with error handling
 * @param {Function} asyncFn - Async function to wrap
 * @param {Object} options - Error handling options
 * @returns {Function} Wrapped function
 */
export const withErrorHandling = (asyncFn, options = {}) => {
  return async (...args) => {
    try {
      return await asyncFn(...args)
    } catch (error) {
      const handler = options.customHandler || globalErrorHandler
      handler(error, { context: options.context })
      
      if (options.rethrow !== false) {
        throw error
      }
    }
  }
}
