import pytest
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.jobstores.memory import MemoryJobStore
from apscheduler.executors.asyncio import AsyncIOExecutor
from pytz import utc


@pytest.fixture
def test_scheduler():
    """Fixture to provide a clean APScheduler instance for testing."""
    jobstores = {"default": MemoryJobStore()}
    executors = {"default": AsyncIOExecutor()}
    job_defaults = {"coalesce": True, "max_instances": 1}
    scheduler = AsyncIOScheduler(
        jobstores=jobstores,
        executors=executors,
        job_defaults=job_defaults,
        timezone=utc,
    )
    yield scheduler  # Provide the scheduler to the test
    # Teardown: Ensure scheduler is shut down if it was started
    if scheduler.running:
        scheduler.shutdown(wait=False)  # wait=False for faster test teardown
