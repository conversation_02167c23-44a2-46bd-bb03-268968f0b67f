import streamlit as st
from libs.api import get_competitions, add_qualifications, get_routes, add_admin_log
from libs.constants import AdminActionType
from copy import deepcopy

st.header("添加学分发放规则", divider="rainbow")


qualification_name = st.text_input(
    "学分发放的理由", placeholder="如：完成任务, 发帖等等"
)
credits = st.number_input("添加的学分", min_value=1, step=1, value=1)

new_competition = st.checkbox("是否为新比赛", help="新比赛指 7.1 后上线的比赛")


if not new_competition:
    # Step 1: Select competition
    competitions = get_competitions()["data"]
    # competitions = [{"id": 1, "name": "2020-2021学年"}, {"id": 2, "name": "2021-2022学年"}, {"id": 3, "name": "2022-2023学年"}]
    competition_options = {
        comp["competition_id"]: comp["competition_name"] for comp in competitions
    }
    competition_id = st.selectbox(
        "选择比赛",
        options=list(competition_options.keys()),
        format_func=lambda x: competition_options[x],
    )
    competition_name = competition_options[competition_id]
    route_options = [
        comp["route_name"]
        for comp in competitions
        if comp["competition_id"] == competition_id
    ]
    selected_routes = st.multiselect(
        "选择赛道",
        options=route_options,
    )

else:
    competition_id = st.text_input("比赛id", placeholder="比赛id")
    competition_name = st.text_input("比赛名称", placeholder="比赛名称")
    routes = [r["route_name"] for r in get_routes()["data"]]
    selected_routes = st.multiselect("赛道", options=routes)

related_task_id = st.text_input(
    "关联的任务",
    placeholder="关联的任务id，如无则留空",
)
is_objective_submission = st.checkbox(
    "是否和客观提交相关", value=False, help="手动发放的学分无需勾选"
)
submission_config = {}
if is_objective_submission:
    score_threshold = st.text_input(
        "通关分数", placeholder="如果该任务仅需提交即可得学分，则留空"
    )
    evaluation_logic = st.selectbox(
        "通关得分和通关分数的关系",
        options=["大于等于", "小于等于", "大于", "小于", "等于"],
        index=0,
    )
    submission_config = {
        "related_task_id": related_task_id,
        "score_threshold": score_threshold,
    }

submission_checked = True
if is_objective_submission:
    submission_checked = related_task_id and score_threshold and evaluation_logic


def submit_new_qualification(data):
    for idx, r in enumerate(data["route_name"]):
        new_data = deepcopy(data)
        new_data["route_name"] = data["route_name"][idx]
        response = add_qualifications(new_data)
        if response.status_code == 200:
            st.success("提交成功")
            add_admin_log(
                {
                    "action_type": AdminActionType.ADD_COMPETITION,
                    "admin": st.session_state.username,
                    "action_relate_id": response.json()["data"],
                    "remark": qualification_name,
                }
            )
        else:
            st.error(f"Error: Cannot add new qualification: {response.json()['error']}")


# Step 4: submit
if st.button("提交"):
    check_requirements = competition_id and qualification_name
    if check_requirements:
        data = {
            **{
                "competition_id": competition_id,
                "competition_name": competition_name,
                "qualification_name": qualification_name,
                "credit": credits,
                "route_name": selected_routes,
            },
            **submission_config,
        }
        submit_new_qualification(data)

    else:
        st.error("请填写完整信息")
