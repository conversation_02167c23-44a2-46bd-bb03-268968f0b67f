import pytest
from unittest.mock import AsyncMock, patch

# Function to test
from app.background_tasks.qualification_scheduler import daily_update


# To mock Tortoise model for competition object
class MockCompetition:
    def __init__(self, competition_id, competition_name="Test Comp"):
        self.competition_id = competition_id
        self.competition_name = competition_name
        self.record_id = f"record_{competition_id}"  # Example, if used by logger


@pytest.mark.asyncio
@patch(
    "app.background_tasks.qualification_scheduler.run_daily_statistics",
    new_callable=AsyncMock,
)
@patch(
    "app.background_tasks.qualification_scheduler.run_submission_job",
    new_callable=AsyncMock,
)
@patch(
    "app.background_tasks.qualification_scheduler.run_teaminfo_job",
    new_callable=AsyncMock,
)
@patch(
    "app.background_tasks.qualification_scheduler.run_registerinfo_job",
    new_callable=AsyncMock,
)
@patch(
    "app.background_tasks.qualification_scheduler.get_competitions",
    new_callable=AsyncMock,
)
async def test_daily_update_no_competitions(
    mock_get_competitions: AsyncMock,
    mock_run_registerinfo: AsyncMock,
    mock_run_teaminfo: AsyncMock,
    mock_run_submission: AsyncMock,
    mock_run_statistics: AsyncMock,
):
    """Test daily_update when get_competitions returns an empty list."""
    mock_get_competitions.return_value = []  # Simulate no competitions

    await daily_update()

    mock_get_competitions.assert_awaited_once()
    mock_run_registerinfo.assert_not_awaited()
    mock_run_teaminfo.assert_not_awaited()
    mock_run_submission.assert_not_awaited()
    mock_run_statistics.assert_not_awaited()


@pytest.mark.asyncio
@patch(
    "app.background_tasks.qualification_scheduler.run_daily_statistics",
    new_callable=AsyncMock,
)
@patch(
    "app.background_tasks.qualification_scheduler.run_submission_job",
    new_callable=AsyncMock,
)
@patch(
    "app.background_tasks.qualification_scheduler.run_teaminfo_job",
    new_callable=AsyncMock,
)
@patch(
    "app.background_tasks.qualification_scheduler.run_registerinfo_job",
    new_callable=AsyncMock,
)
@patch(
    "app.background_tasks.qualification_scheduler.get_competitions",
    new_callable=AsyncMock,
)
async def test_daily_update_with_competitions(
    mock_get_competitions: AsyncMock,
    mock_run_registerinfo: AsyncMock,
    mock_run_teaminfo: AsyncMock,
    mock_run_submission: AsyncMock,
    mock_run_statistics: AsyncMock,
):
    """Test daily_update with a list of competitions."""
    comp1_id = "comp_id_1"
    comp2_id = "comp_id_2"
    mock_competitions = [
        MockCompetition(competition_id=comp1_id, competition_name="Competition 1"),
        MockCompetition(competition_id=comp2_id, competition_name="Competition 2"),
    ]
    mock_get_competitions.return_value = mock_competitions

    await daily_update()

    mock_get_competitions.assert_awaited_once()

    # Check calls for each competition
    assert mock_run_registerinfo.await_count == 2
    mock_run_registerinfo.assert_any_await(comp1_id)
    mock_run_registerinfo.assert_any_await(comp2_id)

    assert mock_run_teaminfo.await_count == 2
    mock_run_teaminfo.assert_any_await(comp1_id)
    mock_run_teaminfo.assert_any_await(comp2_id)

    assert mock_run_submission.await_count == 2
    mock_run_submission.assert_any_await(comp1_id)
    mock_run_submission.assert_any_await(comp2_id)

    assert mock_run_statistics.await_count == 2
    mock_run_statistics.assert_any_await(comp1_id)
    mock_run_statistics.assert_any_await(comp2_id)


@pytest.mark.asyncio
@patch(
    "app.background_tasks.qualification_scheduler.run_daily_statistics",
    new_callable=AsyncMock,
)
@patch(
    "app.background_tasks.qualification_scheduler.run_submission_job",
    new_callable=AsyncMock,
)
@patch(
    "app.background_tasks.qualification_scheduler.run_teaminfo_job",
    new_callable=AsyncMock,
)
@patch(
    "app.background_tasks.qualification_scheduler.run_registerinfo_job",
    new_callable=AsyncMock,
)
@patch(
    "app.background_tasks.qualification_scheduler.get_competitions",
    new_callable=AsyncMock,
)
async def test_daily_update_skips_competition_with_no_id(
    mock_get_competitions: AsyncMock,
    mock_run_registerinfo: AsyncMock,
    mock_run_teaminfo: AsyncMock,
    mock_run_submission: AsyncMock,
    mock_run_statistics: AsyncMock,
):
    """Test daily_update skips processing for a competition if competition_id is missing/None."""
    valid_comp_id = "valid_comp_123"
    mock_competitions = [
        MockCompetition(competition_id=None, competition_name="Invalid Comp No ID"),
        MockCompetition(competition_id="", competition_name="Invalid Comp Empty ID"),
        MockCompetition(competition_id=valid_comp_id, competition_name="Valid Comp"),
    ]
    mock_get_competitions.return_value = mock_competitions

    await daily_update()

    mock_get_competitions.assert_awaited_once()

    # Jobs should only be called for the valid competition_id
    mock_run_registerinfo.assert_awaited_once_with(valid_comp_id)
    mock_run_teaminfo.assert_awaited_once_with(valid_comp_id)
    mock_run_submission.assert_awaited_once_with(valid_comp_id)
    mock_run_statistics.assert_awaited_once_with(valid_comp_id)
