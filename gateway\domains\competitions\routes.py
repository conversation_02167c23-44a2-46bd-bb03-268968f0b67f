"""
Competition Management API Router.

Handles all competition-related endpoints organized by business function:
- Routes (high-level categories)
- Competitions (events within routes)
- Qualifications (standards for earning credits)
- Credits (points earned through qualifications)
- Badges (rewards for accumulated credits)
"""

from fastapi import APIRouter, HTTPException, Query, Body, Depends
import logging
from typing import Dict, Any, Optional

from .schemas import (
    CompetitionUserRegisterRequest,
    CompetitionUserIdentityRegisterRequest,
    CompetitionUserProfileResponse,
    CompetitionUserProfileType,
)
from libs.auth.authentication import (
    get_current_user_id,
)
from core.database.dependencies import get_mongo_kesci_db
from libs.schemas.api.responses import (
    success,
    SuccessResponse,
    ErrorResponse,
)
from .services import competition_service
from libs.schemas.api.base import PaginationConfig

logger = logging.getLogger(__name__)

# Create router with prefix for competition endpoints
router = APIRouter(
    prefix="/competitions",
    tags=["competitions"],
    responses={404: {"description": "Not found"}},
)


# Competition User Registration Endpoints
@router.post(
    "/register",
    response_model=SuccessResponse | ErrorResponse,
    summary="Get Competition Users by Type",
    description="Fetch competition users based on the competition type and an optional registration start date.",
)
async def get_competition_users_by_type(
    comp_type: str = Query(
        "DATA_ANALYSIS", description="Type of competition detail, e.g., DATA_ANALYSIS"
    ),
    request_data: CompetitionUserRegisterRequest = Body(...),
    user_id: str = Depends(get_current_user_id),
    database = Depends(get_mongo_kesci_db),   
    profile_type: CompetitionUserProfileType = Query(
        CompetitionUserProfileType.BASIC, description="Type of competition user profile"
    ),
    limit: Optional[int] = Query(PaginationConfig.DEFAULT_LIMIT, ge=1, le=1000, description="Number of users to return"),
    offset: Optional[int] = Query(PaginationConfig.DEFAULT_OFFSET, ge=0, description="Number of users to skip"),
):
    try:
        # Use CompetitionServices for data access
        result_data:CompetitionUserProfileResponse = await competition_service.get_competition_users_by_type(
            competition_type=comp_type,
            register_start_date=request_data.register_start_date,
            database=database,
            profile_type=profile_type,
            limit=limit,
            offset=offset,
        )
        return success(data=result_data.data)
    except Exception as e:
        logger.error(f"Error fetching competition users by type: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post(
    "/register/identity",
    response_model=SuccessResponse | ErrorResponse,
    summary="Get Competition Users by Identity",
    description="Fetch competition users based on identity and an optional registration start date.",
)
async def get_competition_users_by_identity(
    request_data: CompetitionUserIdentityRegisterRequest = Body(...),
    user_id: str = Depends(get_current_user_id),
    database = Depends(get_mongo_kesci_db),
    profile_type: CompetitionUserProfileType = Query(
        CompetitionUserProfileType.BASIC, description="Type of competition user profile"
    ),
    limit: Optional[int] = Query(PaginationConfig.NO_LIMIT, le=1000, description="Number of users to return"),
    offset: Optional[int] = Query(PaginationConfig.NO_LIMIT, description="Number of users to skip"),
):
    try:
        # Use CompetitionServices for data access
        result_data:CompetitionUserProfileResponse = await competition_service.get_competition_users_by_identity(
            identity=request_data.identity,
            register_start_date=request_data.register_start_date,
            database=database,
            profile_type=profile_type,
            limit=limit,
            offset=offset,
        )
        return success(data=result_data.data)
    except Exception as e:
        logger.error(
            f"Error fetching competition users by identity: {e}", exc_info=True
        )
        raise HTTPException(status_code=500, detail=str(e))
