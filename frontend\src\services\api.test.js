import { describe, it, expect, beforeEach, vi } from 'vitest'

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElMessage: {
    error: vi.fn(),
    success: vi.fn()
  }
}))

// Mock js-cookie
vi.mock('js-cookie', () => ({
  default: {
    get: vi.fn(),
    set: vi.fn(),
    remove: vi.fn()
  }
}))

// Mock axios
vi.mock('axios', () => ({
  default: {
    create: vi.fn(() => ({
      interceptors: {
        request: { use: vi.fn() },
        response: { use: vi.fn() }
      }
    }))
  }
}))

describe('API Service', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('creates axios instance with correct configuration', async () => {
    const { default: axios } = await import('axios')

    expect(axios.create).toHaveBeenCalledWith({
      baseURL: expect.any(String),
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    })
  })

  it('sets up interceptors', async () => {
    // This test just verifies the module loads without errors
    const api = await import('./api')
    expect(api.default).toBeDefined()
  })
})
