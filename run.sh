#!/bin/bash

# ——— Docker Build and Push Script ———
# Builds and pushes the Community Services Docker image to Aliyun Container Registry

# Exit immediately if a command exits with a non-zero status
set -e

# ——— Configuration ———
# Update these variables as needed for new releases
TAG_VERSION="${TAG_VERSION:-community_ds_v4.1.1}"  # Can be overridden via environment variable
DOCKER_IMAGE="community_ds"
DOCKER_REPO="registry.cn-shanghai.aliyuncs.com/kcr-3rd"
FULL_IMAGE_NAME="${DOCKER_REPO}/${DOCKER_IMAGE}:${TAG_VERSION}"

# ——— Build Process ———
echo "🏗️  Building Docker image..."
echo "📦 Image: ${FULL_IMAGE_NAME}"
echo "📁 Context: $(pwd)"
echo ""

# Build the Docker image with the specified tag
docker build -t "${FULL_IMAGE_NAME}" .

echo "✅ Docker image built successfully!"
echo ""

# ——— Push Process ———
echo "🚀 Pushing Docker image to registry..."
echo "📤 Target: ${DOCKER_REPO}"

# Push the Docker image to the specified repository
docker push "${FULL_IMAGE_NAME}"

echo ""
echo "🎉 Success! Docker image pushed successfully."
echo "📋 Image: ${FULL_IMAGE_NAME}"
echo ""
echo "💡 To use a different version, run:"
echo "   TAG_VERSION=your_version ./run.sh"
