#!/bin/bash

# Exit immediately if a command exits with a non-zero status
set -e

# Define the tag version specific to this repository
# 需要手动更新
TAG_VERSION="community_st_v5.1.1"

# Define the Docker image and repository
DOCKER_IMAGE="community_streamlit"
DOCKER_REPO="registry.cn-shanghai.aliyuncs.com/kcr-3rd"

# Build the Docker image
echo "Building Docker image with tag: $TAG_VERSION..."
docker build -t "${DOCKER_REPO}/${DOCKER_IMAGE}:${TAG_VERSION}" .

# Push the Docker image to the specified repository
echo "Pushing Docker image to ${DOCKER_REPO}..."
docker push "${DOCKER_REPO}/${DOCKER_IMAGE}:${TAG_VERSION}"

# Success message
echo "Docker image ${DOCKER_REPO}/${DOCKER_IMAGE}:${TAG_VERSION} built and pushed successfully."
