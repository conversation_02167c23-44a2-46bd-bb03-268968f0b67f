from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS `access_log` (
    `id` INT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `user_id` VARCHAR(255) NOT NULL  COMMENT '用户ID',
    `target_url` VARCHAR(255)   COMMENT '访问的url',
    `user_agent` VARCHAR(255)   COMMENT '访问UA',
    `request_params` JSON   COMMENT '请求参数get|post',
    `ip` VARCHAR(32)   COMMENT '访问IP',
    `note` VARCHAR(255)   COMMENT '备注'
) CHARACTER SET utf8mb4 COMMENT='用户操作记录表';
CREATE TABLE IF NOT EXISTS `administrators` (
    `email_address` VARCHAR(255) NOT NULL  PRIMARY KEY,
    `password` VARCHAR(255) NOT NULL,
    `user_name` VARCHAR(255),
    `last_login_ts` DATETIME(6)
) CHARACTER SET utf8mb4 COMMENT='Site administrators';
CREATE TABLE IF NOT EXISTS `admin_logs` (
    `created_at` DATETIME(6) NOT NULL  COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `record_id` CHAR(36) NOT NULL  PRIMARY KEY,
    `action_type` INT NOT NULL,
    `action_related_id` VARCHAR(255),
    `remark` VARCHAR(255),
    `admin_email_id` VARCHAR(255) NOT NULL,
    CONSTRAINT `fk_admin_lo_administ_1ea23d10` FOREIGN KEY (`admin_email_id`) REFERENCES `administrators` (`email_address`) ON DELETE CASCADE
) CHARACTER SET utf8mb4 COMMENT='Logs of administrator actions';
CREATE TABLE IF NOT EXISTS `majors` (
    `created_at` DATETIME(6) NOT NULL  COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `id` INT NOT NULL PRIMARY KEY AUTO_INCREMENT COMMENT '专业ID',
    `name` VARCHAR(255) NOT NULL UNIQUE COMMENT '专业名称'
) CHARACTER SET utf8mb4;
CREATE TABLE IF NOT EXISTS `qualified_users_triggered` (
    `id` INT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `user_id` VARCHAR(255) NOT NULL,
    `qualification_id` VARCHAR(255) NOT NULL,
    `qualification_name` VARCHAR(255),
    `credit` INT NOT NULL  DEFAULT 0,
    `task_id` VARCHAR(255) NOT NULL,
    `competition_id` VARCHAR(255) NOT NULL,
    `team_id` VARCHAR(255) NOT NULL,
    `qualified_date` DATETIME(6) NOT NULL,
    `user_name` VARCHAR(255) NOT NULL,
    `university` VARCHAR(255)   DEFAULT '',
    `update_ts` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    UNIQUE KEY `uid_qualified_u_user_id_4d8b7f` (`user_id`, `qualification_id`)
) CHARACTER SET utf8mb4 COMMENT='View of users who met qualification criteria';
CREATE TABLE IF NOT EXISTS `routes` (
    `route_id` VARCHAR(255) NOT NULL  PRIMARY KEY,
    `route_name` VARCHAR(255),
    `info` VARCHAR(255)
) CHARACTER SET utf8mb4 COMMENT='Competition routes or tracks';
CREATE TABLE IF NOT EXISTS `competitions` (
    `record_id` VARCHAR(255) NOT NULL  PRIMARY KEY,
    `competition_id` VARCHAR(255) NOT NULL UNIQUE,
    `route_name` VARCHAR(255) NOT NULL,
    `competition_name` VARCHAR(255),
    `route_id` VARCHAR(255) NOT NULL,
    CONSTRAINT `fk_competit_routes_1f3c2e33` FOREIGN KEY (`route_id`) REFERENCES `routes` (`route_id`) ON DELETE RESTRICT
) CHARACTER SET utf8mb4 COMMENT='Competition details';
CREATE TABLE IF NOT EXISTS `qualifications` (
    `qualification_id` VARCHAR(255) NOT NULL  PRIMARY KEY,
    `qualification_name` VARCHAR(255),
    `qualification_type` INT   DEFAULT 1,
    `qualification_logic` INT   DEFAULT 1,
    `related_task_id` VARCHAR(255),
    `score_threshold` DOUBLE,
    `deleted` BOOL   DEFAULT 0,
    `credit` INT,
    `competition_id` VARCHAR(255) NOT NULL COMMENT 'The competition this qualification belongs to',
    CONSTRAINT `fk_qualific_competit_4566bfae` FOREIGN KEY (`competition_id`) REFERENCES `competitions` (`competition_id`) ON DELETE CASCADE
) CHARACTER SET utf8mb4 COMMENT='Qualification criteria for competitions';
CREATE TABLE IF NOT EXISTS `statistics` (
    `created_at` DATETIME(6) NOT NULL  COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `record_id` CHAR(36) NOT NULL  PRIMARY KEY,
    `statistics_name` VARCHAR(255) NOT NULL,
    `statistics_value` INT   DEFAULT 0,
    `statistics_related_id` VARCHAR(255),
    `remark` VARCHAR(255)
) CHARACTER SET utf8mb4 COMMENT='General statistics records';
CREATE TABLE IF NOT EXISTS `submissions` (
    `record_id` VARCHAR(255) NOT NULL  PRIMARY KEY,
    `submission_ts` DATETIME(6) NOT NULL,
    `score` INT,
    `team` VARCHAR(255) NOT NULL,
    `task_id` VARCHAR(255) NOT NULL,
    `competition_id` VARCHAR(255) NOT NULL,
    CONSTRAINT `fk_submissi_competit_4b49c60f` FOREIGN KEY (`competition_id`) REFERENCES `competitions` (`competition_id`) ON DELETE CASCADE
) CHARACTER SET utf8mb4 COMMENT='User submissions for tasks/competitions';
CREATE TABLE IF NOT EXISTS `universities` (
    `created_at` DATETIME(6) NOT NULL  COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `id` INT NOT NULL PRIMARY KEY AUTO_INCREMENT COMMENT '学校ID',
    `name` VARCHAR(255) NOT NULL UNIQUE COMMENT '学校名称'
) CHARACTER SET utf8mb4;
CREATE TABLE IF NOT EXISTS `users` (
    `user_id` VARCHAR(255) NOT NULL  PRIMARY KEY,
    `register_id` VARCHAR(255) NOT NULL UNIQUE,
    `user_name` VARCHAR(255),
    `university` VARCHAR(255)   DEFAULT '',
    `competition_id` VARCHAR(255),
    `phone` VARCHAR(255),
    `email` VARCHAR(255)  UNIQUE
) CHARACTER SET utf8mb4 COMMENT='User accounts';
CREATE TABLE IF NOT EXISTS `credit_history` (
    `created_at` DATETIME(6) NOT NULL  COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `record_id` CHAR(36) NOT NULL  PRIMARY KEY,
    `credit` INT NOT NULL  DEFAULT 0,
    `competition_id` VARCHAR(255) NOT NULL,
    `qualification_id` VARCHAR(255),
    `remark` VARCHAR(255),
    `batch_id` VARCHAR(255),
    `deleted` BOOL NOT NULL  DEFAULT 0,
    `user_id` VARCHAR(255) NOT NULL,
    CONSTRAINT `fk_credit_h_users_5d613420` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) CHARACTER SET utf8mb4 COMMENT='Record of user credit changes';
CREATE TABLE IF NOT EXISTS `teams` (
    `membership_id` VARCHAR(255) NOT NULL  PRIMARY KEY,
    `team_id` VARCHAR(255) NOT NULL,
    `update_ts` DATETIME(6),
    `competition_id` VARCHAR(255) NOT NULL,
    `user_id` VARCHAR(255) NOT NULL,
    CONSTRAINT `fk_teams_competit_4a551b16` FOREIGN KEY (`competition_id`) REFERENCES `competitions` (`competition_id`) ON DELETE CASCADE,
    CONSTRAINT `fk_teams_users_b963578a` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) CHARACTER SET utf8mb4 COMMENT='Team memberships';
CREATE TABLE IF NOT EXISTS `aerich` (
    `id` INT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `version` VARCHAR(255) NOT NULL,
    `app` VARCHAR(100) NOT NULL,
    `content` JSON NOT NULL
) CHARACTER SET utf8mb4;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        """
