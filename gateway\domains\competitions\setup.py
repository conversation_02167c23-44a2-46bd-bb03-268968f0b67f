"""
Competition database setup and initialization utilities.

Contains functions for creating tables, cleaning data, and initializing
default data for the competitions domain.
"""

from tortoise import Tortoise
from libs.models.base_models import (
    Route as TortoiseRoute,
    Competition as TortoiseCompetition,
    Qualification as TortoiseQualification,
    CreditHistory as TortoiseCreditHistory,
    Ranking as TortoiseRanking,
    Statistics as TortoiseStatistic,
)
import logging

logger = logging.getLogger(__name__)


async def create_tables():
    """
    Creates database tables for qualification models.
    This is mainly used during initial setup and migrations.

    Note: In production, you may want to use Aerich or other migration tools
    instead of directly creating tables.
    """
    logger.info("Creating qualification database tables")

    try:
        # Generate schemas for all models using Tortoise ORM
        await Tortoise.generate_schemas()
        logger.info("Tables created successfully")
        return True
    except Exception as e:
        logger.error(f"Error creating tables: {e}")
        raise


async def clean_tables():
    """
    Cleans data from qualification tables without dropping the tables.
    Useful for development and testing environments.
    """
    logger.info("Cleaning qualification tables")
    try:
        # Delete records from tables in appropriate order to respect foreign key constraints
        await TortoiseRanking.all().delete()
        await TortoiseCreditHistory.all().delete()
        await TortoiseQualification.all().delete()
        await TortoiseCompetition.all().delete()
        await TortoiseRoute.all().delete()
        await TortoiseStatistic.all().delete()

        logger.info("All qualification tables cleaned successfully")
        return True
    except Exception as e:
        logger.error(f"Error cleaning tables: {e}")
        raise


async def initialize_tables():
    """
    Initializes qualification tables with default data.
    This is called after table creation to populate with initial required data.
    """
    logger.info("Initializing qualification tables with default data")
    try:
        # Create default routes if they don't exist
        default_routes = [
            {"route_id": "route_1", "route_name": "数据科学"},
            {"route_id": "route_2", "route_name": "人工智能"},
            {"route_id": "route_3", "route_name": "云计算"},
        ]

        for route_data in default_routes:
            print(route_data["route_id"])
            # Create a copy of route_data without route_id to avoid parameter duplication
            defaults = route_data.copy()
            defaults.pop("route_id")
            await TortoiseRoute.get_or_create(
                route_id=route_data["route_id"], defaults=defaults
            )

        # Create default competitions if needed
        # Similar pattern for other default data

        logger.info("Default data initialized successfully")
        return True
    except Exception as e:
        logger.error(f"Error initializing tables with default data: {e}")
        raise
