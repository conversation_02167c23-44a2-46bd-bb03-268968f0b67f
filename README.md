# Community Services API

本项目为社区（及夏令营）提供相关的数据服务和 API 接口。项目基于 FastAPI 构建，采用现代化的域驱动架构。

## 🚀 最新更新 (2025-01-17)

### ✅ 域驱动架构重构完成
- **新架构**: 从分层架构重构为域驱动架构，每个业务域自包含
- **按业务组织**: 竞赛域 (Competitions) / 用户域 (Users) / 分析域 (Analytics) / 社区域 (Community)
- **简化架构**: 移除过度工程化的处理层，直接服务实例化
- **真正解耦**: 每个域管理自己的服务，无外部依赖

### ✅ 文件组织优化
- **脚本目录**: 数据库脚本移至 `scripts/` 目录并添加文档
- **文档整理**: 项目文档移至 `docs/` 目录统一管理
- **安全改进**: 移除 SSH 密钥文件，更新忽略文件配置
- **构建优化**: 改进 Docker 构建脚本，支持版本配置

## 主要技术栈

*   **后端框架**: Python 3.11+, FastAPI (异步 Web 框架)
*   **服务架构**: 域驱动架构 (Domain-Focused Architecture)
*   **数据校验**: Pydantic (数据模型和验证)
*   **数据库**:
    *   关系型: PostgreSQL (使用 Tortoise-ORM)
    *   非关系型: MongoDB (使用 Motor)
*   **缓存**: Redis (查询层缓存)
*   **异步任务**: APScheduler (后台任务调度)
*   **容器化**: Docker + Docker Compose
*   **代码规范**: Ruff, Black (代码格式化和检查)

## 🏗️ 项目架构

### 新域驱动架构
```
gateway/
├── domains/              # 业务域 - 每个域自包含所有逻辑
│   ├── competitions/     # 竞赛域
│   │   ├── routes.py     # FastAPI 路由
│   │   ├── services.py   # 业务逻辑
│   │   ├── models.py     # 域模型
│   │   └── schemas.py    # 请求/响应模式
│   ├── users/            # 用户域
│   │   ├── routes.py
│   │   ├── services.py
│   │   ├── models.py
│   │   └── schemas.py
│   ├── analytics/        # 分析域
│   │   ├── routes.py
│   │   ├── services.py
│   │   ├── models.py
│   │   └── schemas.py
│   └── community/        # 社区域
│       ├── routes.py
│       ├── services.py
│       ├── models.py
│       └── schemas.py
├── admin/                # 管理层服务 (保留)
├── integrations/         # 集成层服务 (保留)
└── shared/               # 共享工具和配置
    ├── auth.py
    ├── errors.py
    ├── config.py
    └── container.py      # 最小化容器 (仅非域服务)
```

### 完整项目结构
```
community-services/
├── scripts/                # 🆕 数据库和工具脚本
│   ├── README.md          # 脚本使用文档
│   ├── initialize_db.py   # 数据库初始化
│   └── fill_qualification_data.py  # 数据填充
├── docs/                   # 📚 项目文档
│   ├── PROJECT.md         # 项目概述
│   ├── architecture/      # 架构文档
│   ├── api/              # API 文档
│   └── planning/         # 规划文档
├── gateway/               # 🎯 主要服务层 (域驱动架构)
│   ├── domains/          # 业务域 (新架构核心)
│   │   ├── competitions/ # 竞赛域 - 自包含
│   │   ├── users/        # 用户域 - 自包含
│   │   ├── analytics/    # 分析域 - 自包含
│   │   └── community/    # 社区域 - 自包含
│   ├── api/v1/           # API 路由 (主路由器)
│   ├── admin/            # 管理层服务 (保留)
│   ├── integrations/     # 集成层服务 (保留)
│   └── shared/           # 共享工具和配置
├── core/                  # 🔧 基础设施组件
│   ├── config/           # 配置管理
│   ├── database/         # 数据库连接
│   └── monitoring/       # 监控和日志
├── data_pipeline/         # 📊 数据处理管道
├── shared/               # 🤝 跨服务共享模块
├── tests/                # 🧪 自动化测试
├── migrations/           # 📋 数据库迁移
├── main.py              # FastAPI 应用入口
├── run_gateway_server.py # 🚀 网关服务启动脚本
├── run.sh               # 🐳 Docker 构建脚本 (已优化)
└── requirements.txt     # Python 依赖
```

## 环境准备

*   **Python**: 3.11+ (推荐使用最新版本)
*   **数据库**: PostgreSQL 和 MongoDB 实例
*   **缓存**: Redis (用于查询层缓存)
*   **容器**: Docker 和 Docker Compose
*   **开发工具**: Git, VS Code (推荐)

## 🚀 快速开始

### 1. 克隆和环境设置
```bash
# 克隆代码仓库
git clone <your-repository-url>
cd community-services

# 创建并激活虚拟环境
python -m venv .venv
# Windows
.venv\Scripts\activate
# macOS/Linux
source .venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量
```bash
# 复制环境变量示例文件
cp .env.example .env

# 编辑 .env 文件，配置数据库连接等信息
# 包括: DATABASE_URL, MONGO_*, REDIS_*, SSH_TUNNEL_* 等
```

### 3. 初始化数据库
```bash
# 初始化数据库架构
python scripts/initialize_db.py

# 填充基础数据 (可选)
python scripts/fill_qualification_data.py
```

### 4. 启动服务
```bash
# 启动网关服务 (开发模式)
python run_gateway_server.py

# 或使用 Docker Compose
docker-compose up -d
```

### 5. 访问服务
- **API 文档**: [http://localhost:8000/docs](http://localhost:8000/docs)
- **ReDoc**: [http://localhost:8000/redoc](http://localhost:8000/redoc)
- **健康检查**: [http://localhost:8000/health](http://localhost:8000/health)

## 🔧 开发指南

### 域服务使用示例
```python
# 竞赛域 - 直接服务实例化
from gateway.domains.competitions.services import CompetitionServices

competition_service = CompetitionServices()
routes = await competition_service.get_routes(limit=10)

# 用户域 - 直接服务实例化
from gateway.domains.users.services import UserServices

user_service = UserServices()
profile = await user_service.get_user_profile(user_id="user123")

# 分析域 - 直接服务实例化
from gateway.domains.analytics.services import AnalyticsServices

analytics_service = AnalyticsServices()
rankings = await analytics_service.get_user_rankings_by_route()

# 社区域 - 直接服务实例化
from gateway.domains.community.services import CommunityServices

community_service = CommunityServices()
universities = await community_service.get_universities()
```

### API 端点组织
- **竞赛管理**: `/api/v1/competitions/` - 路线、竞赛、资格、积分
- **社区功能**: `/api/v1/community/` - 用户发现、大学、专业
- **用户管理**: `/api/v1/users/` - 用户资料、标签、搜索
- **数据分析**: `/api/v1/analytics/` - 排名、统计、事件追踪
- **系统管理**: `/api/v1/admin/` - 管理员、系统监控、日志
- **第三方集成**: `/api/v1/integrations/` - ShenCe、HeyWhale、Webhook

## 🐳 Docker 部署

### 构建和推送镜像
```bash
# 使用默认版本
./run.sh

# 使用自定义版本
TAG_VERSION=v4.2.0 ./run.sh

# 手动构建
docker build -t community-services:latest .
```

### Docker Compose 部署
```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 📊 架构特性

### 域驱动架构 (Domain-Focused Architecture)
- **真正自包含**: 每个域管理自己的路由、服务、模型和模式
- **直接服务实例化**: `CompetitionServices()` 而非容器查找
- **无外部依赖**: 域之间无耦合，真正的边界隔离
- **简化架构**: 移除不必要的间接层和容器复杂性

### 保留的分层服务 (Admin & Integrations)
- **管理层 (Admin)**: 事务支持，审计日志，权限控制
- **集成层 (Integrations)**: 优雅降级，重试机制，错误处理
- **最小化容器**: 仅注册非域服务 (credit, ranking queries)

## 🧪 测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_domain_architecture.py

# 生成覆盖率报告
pytest --cov=gateway --cov-report=html
```

## 📚 文档

- **架构文档**: `docs/architecture/service-layer-architecture.md`
- **API 使用指南**: `docs/api/service-layer-usage-guide.md`
- **脚本文档**: `scripts/README.md`
- **项目概述**: `docs/PROJECT.md`

## 🤝 贡献指南

1. **代码规范**: 使用 Intent-First 注释风格
2. **测试**: 为新功能编写测试
3. **文档**: 更新相关文档
4. **架构**: 遵循域驱动架构模式

## 📄 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。
