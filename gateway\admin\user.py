"""
User Admin - CRUD operations for user management.

Handles user creation, updates, and deletion with audit logging and transaction support.
"""

from typing import Dict, Any, List
import logging
from datetime import datetime

from gateway.admin.base import BaseAdmin
from libs.auth.context import AuthContext, require_auth
from libs.errors import AdminResult, AdminError

logger = logging.getLogger(__name__)


class UserAdmin(BaseAdmin):
    """Admin service for user management operations."""

    def __init__(self):
        """Initialize user admin service."""
        super().__init__()

    @require_auth
    async def create_user(
        self, auth_context: AuthContext, user_data: Dict[str, Any]
    ) -> AdminResult[Dict[str, Any]]:
        """
        Create a new user with audit logging.

        Args:
            auth_context: Authentication context
            user_data: User data including name, email, phone, etc.

        Returns:
            AdminResult containing the created user data
        """
        try:
            # Validate required fields
            required_fields = ["user_id", "user_name", "email"]
            missing_fields = [
                field
                for field in required_fields
                if field not in user_data or not user_data[field]
            ]

            if missing_fields:
                return AdminError.validation_failed(
                    "required_fields",
                    f"Missing required fields: {', '.join(missing_fields)}",
                )

            # Validate email format
            email = user_data["email"]
            if "@" not in email or "." not in email.split("@")[-1]:
                return AdminError.validation_failed("email", "Invalid email format")

            async with self.transaction_context() as session:
                # Check if user already exists
                existing_user = await self.db["users"].find_one(
                    {"user_id": user_data["user_id"]}, session=session
                )

                if existing_user:
                    return AdminError.validation_failed(
                        "user_id", "User already exists"
                    )

                # Prepare user document
                user_document = {
                    "user_id": user_data["user_id"],
                    "user_name": user_data["user_name"],
                    "email": user_data["email"],
                    "phone": user_data.get("phone"),
                    "university": user_data.get("university"),
                    "competition_id": user_data.get("competition_id"),
                    "register_id": user_data.get("register_id"),
                    "is_active": user_data.get("is_active", True),
                    "created_at": datetime.now(),
                    "created_by": auth_context.user_id,
                    "updated_at": datetime.now(),
                    "updated_by": auth_context.user_id,
                }

                # Insert user
                await self.db["users"].insert_one(user_document, session=session)

                # Log audit
                await self._log_audit(
                    auth_context,
                    "CREATE",
                    "users",
                    user_data["user_id"],
                    {"user_data": user_data},
                    session,
                )

                logger.info(
                    f"Created user {user_data['user_id']} by {auth_context.user_id}"
                )
                return AdminResult.success(user_document)

        except Exception as e:
            logger.error(f"Failed to create user: {e}")
            return AdminError.transaction_failed("create user")

    @require_auth
    async def update_user(
        self, auth_context: AuthContext, user_id: str, updates: Dict[str, Any]
    ) -> AdminResult[Dict[str, Any]]:
        """
        Update an existing user with audit logging.

        Args:
            auth_context: Authentication context
            user_id: ID of the user to update
            updates: Fields to update

        Returns:
            AdminResult containing the updated user data
        """
        try:
            if not user_id or not user_id.strip():
                return AdminError.validation_failed("user_id", "User ID is required")

            if not updates:
                return AdminError.validation_failed("updates", "No updates provided")

            # Validate email format if provided
            if "email" in updates:
                email = updates["email"]
                if email and ("@" not in email or "." not in email.split("@")[-1]):
                    return AdminError.validation_failed("email", "Invalid email format")

            async with self.transaction_context() as session:
                # Check if user exists
                existing_user = await self.db["users"].find_one(
                    {"user_id": user_id}, session=session
                )

                if not existing_user:
                    return AdminError.validation_failed("user_id", "User not found")

                # Prepare update data
                update_data = {}
                allowed_fields = [
                    "user_name",
                    "email",
                    "phone",
                    "university",
                    "competition_id",
                    "register_id",
                    "is_active",
                ]

                for field, value in updates.items():
                    if field in allowed_fields:
                        update_data[field] = value

                if not update_data:
                    return AdminError.validation_failed(
                        "updates", "No valid fields to update"
                    )

                # Add metadata
                update_data["updated_at"] = datetime.now()
                update_data["updated_by"] = auth_context.user_id

                # Update user
                result = await self.db["users"].update_one(
                    {"user_id": user_id}, {"$set": update_data}, session=session
                )

                if result.modified_count == 0:
                    return AdminError.transaction_failed(
                        "update user - no changes made"
                    )

                # Get updated user
                updated_user = await self.db["users"].find_one(
                    {"user_id": user_id}, session=session
                )

                # Log audit
                await self._log_audit(
                    auth_context,
                    "UPDATE",
                    "users",
                    user_id,
                    {"updates": updates, "applied_updates": update_data},
                    session,
                )

                logger.info(f"Updated user {user_id} by {auth_context.user_id}")
                return AdminResult.success(updated_user)

        except Exception as e:
            logger.error(f"Failed to update user {user_id}: {e}")
            return AdminError.transaction_failed("update user")

    @require_auth
    async def delete_user(
        self, auth_context: AuthContext, user_id: str, soft_delete: bool = True
    ) -> AdminResult[bool]:
        """
        Delete a user (soft delete by default) with audit logging.

        Args:
            auth_context: Authentication context
            user_id: ID of the user to delete
            soft_delete: Whether to soft delete (mark as inactive) or hard delete

        Returns:
            AdminResult indicating success or failure
        """
        try:
            if not user_id or not user_id.strip():
                return AdminError.validation_failed("user_id", "User ID is required")

            async with self.transaction_context() as session:
                # Check if user exists
                existing_user = await self.db["users"].find_one(
                    {"user_id": user_id}, session=session
                )

                if not existing_user:
                    return AdminError.validation_failed("user_id", "User not found")

                if soft_delete:
                    # Soft delete - mark as inactive
                    result = await self.db["users"].update_one(
                        {"user_id": user_id},
                        {
                            "$set": {
                                "is_active": False,
                                "deleted_at": datetime.now(),
                                "deleted_by": auth_context.user_id,
                                "updated_at": datetime.now(),
                                "updated_by": auth_context.user_id,
                            }
                        },
                        session=session,
                    )

                    operation = "SOFT_DELETE"
                    success = result.modified_count > 0
                else:
                    # Hard delete - remove from database
                    result = await self.db["users"].delete_one(
                        {"user_id": user_id}, session=session
                    )

                    operation = "DELETE"
                    success = result.deleted_count > 0

                if not success:
                    return AdminError.transaction_failed(f"{operation.lower()} user")

                # Log audit
                await self._log_audit(
                    auth_context,
                    operation,
                    "users",
                    user_id,
                    {"soft_delete": soft_delete},
                    session,
                )

                logger.info(f"{operation} user {user_id} by {auth_context.user_id}")
                return AdminResult.success(True)

        except Exception as e:
            logger.error(f"Failed to delete user {user_id}: {e}")
            return AdminError.transaction_failed("delete user")

    @require_auth
    async def activate_user(
        self, auth_context: AuthContext, user_id: str
    ) -> AdminResult[Dict[str, Any]]:
        """
        Activate a deactivated user.

        Args:
            auth_context: Authentication context
            user_id: ID of the user to activate

        Returns:
            AdminResult containing the activated user data
        """
        try:
            if not user_id or not user_id.strip():
                return AdminError.validation_failed("user_id", "User ID is required")

            async with self.transaction_context() as session:
                # Check if user exists
                existing_user = await self.db["users"].find_one(
                    {"user_id": user_id}, session=session
                )

                if not existing_user:
                    return AdminError.validation_failed("user_id", "User not found")

                if existing_user.get("is_active", True):
                    return AdminError.validation_failed(
                        "user_id", "User is already active"
                    )

                # Activate user
                result = await self.db["users"].update_one(
                    {"user_id": user_id},
                    {
                        "$set": {
                            "is_active": True,
                            "activated_at": datetime.now(),
                            "activated_by": auth_context.user_id,
                            "updated_at": datetime.now(),
                            "updated_by": auth_context.user_id,
                        },
                        "$unset": {"deleted_at": "", "deleted_by": ""},
                    },
                    session=session,
                )

                if result.modified_count == 0:
                    return AdminError.transaction_failed("activate user")

                # Get updated user
                updated_user = await self.db["users"].find_one(
                    {"user_id": user_id}, session=session
                )

                # Log audit
                await self._log_audit(
                    auth_context, "ACTIVATE", "users", user_id, {}, session
                )

                logger.info(f"Activated user {user_id} by {auth_context.user_id}")
                return AdminResult.success(updated_user)

        except Exception as e:
            logger.error(f"Failed to activate user {user_id}: {e}")
            return AdminError.transaction_failed("activate user")

    @require_auth
    async def bulk_update_users(
        self, auth_context: AuthContext, user_updates: List[Dict[str, Any]]
    ) -> AdminResult[Dict[str, Any]]:
        """
        Update multiple users in a single transaction.

        Args:
            auth_context: Authentication context
            user_updates: List of user updates, each containing user_id and updates

        Returns:
            AdminResult containing summary of bulk update operation
        """
        try:
            if not user_updates:
                return AdminError.validation_failed(
                    "user_updates", "No user updates provided"
                )

            # Validate each update entry
            for i, update_entry in enumerate(user_updates):
                if "user_id" not in update_entry or "updates" not in update_entry:
                    return AdminError.validation_failed(
                        "user_updates",
                        f"Entry {i} missing required fields: user_id, updates",
                    )

            async with self.transaction_context() as session:
                updated_count = 0
                failed_updates = []

                for update_entry in user_updates:
                    user_id = update_entry["user_id"]
                    updates = update_entry["updates"]

                    try:
                        # Check if user exists
                        existing_user = await self.db["users"].find_one(
                            {"user_id": user_id}, session=session
                        )

                        if not existing_user:
                            failed_updates.append(
                                {"user_id": user_id, "reason": "User not found"}
                            )
                            continue

                        # Prepare update data
                        update_data = {}
                        allowed_fields = [
                            "user_name",
                            "email",
                            "phone",
                            "university",
                            "competition_id",
                            "register_id",
                            "is_active",
                        ]

                        for field, value in updates.items():
                            if field in allowed_fields:
                                update_data[field] = value

                        if not update_data:
                            failed_updates.append(
                                {
                                    "user_id": user_id,
                                    "reason": "No valid fields to update",
                                }
                            )
                            continue

                        # Add metadata
                        update_data["updated_at"] = datetime.now()
                        update_data["updated_by"] = auth_context.user_id

                        # Update user
                        result = await self.db["users"].update_one(
                            {"user_id": user_id}, {"$set": update_data}, session=session
                        )

                        if result.modified_count > 0:
                            updated_count += 1

                            # Log audit for each successful update
                            await self._log_audit(
                                auth_context,
                                "BULK_UPDATE",
                                "users",
                                user_id,
                                {"updates": updates, "applied_updates": update_data},
                                session,
                            )
                        else:
                            failed_updates.append(
                                {"user_id": user_id, "reason": "No changes made"}
                            )

                    except Exception as e:
                        failed_updates.append({"user_id": user_id, "reason": str(e)})

                # Log bulk operation audit
                await self._log_audit(
                    auth_context,
                    "BULK_UPDATE_OPERATION",
                    "users",
                    "bulk",
                    {
                        "total_requested": len(user_updates),
                        "successful_updates": updated_count,
                        "failed_updates": len(failed_updates),
                    },
                    session,
                )

                result_data = {
                    "total_requested": len(user_updates),
                    "successful_updates": updated_count,
                    "failed_updates": failed_updates,
                }

                logger.info(
                    f"Bulk updated {updated_count}/{len(user_updates)} users by {auth_context.user_id}"
                )
                return AdminResult.success(result_data)

        except Exception as e:
            logger.error(f"Failed to bulk update users: {e}")
            return AdminError.transaction_failed("bulk update users")
