"""
Authentication services for Camp Admin domain.

Handles authentication business logic including:
- Admin authentication and authorization
- Token generation and validation
- Password management
- Session management
"""

import hashlib
import jwt
import secrets
import logging
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any
from tortoise.exceptions import Does<PERSON>otExist, IntegrityError

from libs.models.tables import Administrators
from .auth_schemas import (
    AdminAuthServiceResponse,
    AdminLoginResponse,
    AdminProfileResponse,
    AdminRegisterResponse,
    AdminTokenResponse,
    AdminTokenVerifyResponse,
    AdminTokenData,
    AdminPasswordValidation
)

logger = logging.getLogger(__name__)

# Configuration (should be moved to environment variables)
JWT_SECRET_KEY = "your-secret-key-here"  # TODO: Move to environment
JWT_ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
REFRESH_TOKEN_EXPIRE_DAYS = 7


class CampAdminAuthServices:
    """Authentication services for camp admin domain."""
    
    @staticmethod
    def hash_password(password: str) -> str:
        """Hash password using SHA-256."""
        return hashlib.sha256(password.encode()).hexdigest()
    
    @staticmethod
    def verify_password(password: str, hashed_password: str) -> bool:
        """Verify password against hash."""
        return hashlib.sha256(password.encode()).hexdigest() == hashed_password
    
    @staticmethod
    def generate_jwt_token(admin_data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """Generate JWT token for admin."""
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        
        payload = {
            "admin_id": admin_data["email"],
            "email": admin_data["email"],
            "username": admin_data.get("username", ""),
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "access"
        }
        
        return jwt.encode(payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    
    @staticmethod
    def generate_refresh_token(admin_data: Dict[str, Any]) -> str:
        """Generate refresh token for admin."""
        expire = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
        
        payload = {
            "admin_id": admin_data["email"],
            "email": admin_data["email"],
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "refresh"
        }
        
        return jwt.encode(payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    
    @staticmethod
    def verify_jwt_token(token: str) -> Optional[Dict[str, Any]]:
        """Verify and decode JWT token."""
        try:
            payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
            return payload
        except jwt.ExpiredSignatureError:
            logger.warning("Token has expired")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"Invalid token: {e}")
            return None
    
    async def authenticate_admin(
        self, 
        email: str, 
        password: str, 
        db_session
    ) -> AdminAuthServiceResponse:
        """Authenticate admin with email and password."""
        try:
            # Find admin by email
            admins = await Administrators.all()
            print(f'current admins:{admins}', flush=True)
            admin = await Administrators.get_or_none(email_address=email)
            print(f'current valid admin:{admin.email_address}', flush=True)
            if not admin:
                return AdminAuthServiceResponse(
                    success=False,
                    error_message="Invalid credentials",
                    error_code="INVALID_CREDENTIALS"
                )
            print(f'current valid admin:{admin.password}', flush=True)

            # Verify password
            if not self.verify_password(password, admin.password):
                return AdminAuthServiceResponse(
                    success=False,
                    error_message="Invalid credentials",
                    error_code="INVALID_CREDENTIALS"
                )
            
            # Update last login timestamp
            admin.last_login_ts = datetime.now()
            await admin.save()
            
            # Generate tokens
            admin_data = {
                "email": admin.email_address,
                "username": admin.user_name or admin.email_address
            }
            
            access_token = self.generate_jwt_token(admin_data)
            refresh_token = self.generate_refresh_token(admin_data)
            
            # Prepare response
            login_response = AdminLoginResponse(
                token=access_token,
                refresh_token=refresh_token,
                expires_in=ACCESS_TOKEN_EXPIRE_MINUTES * 60,
                user=AdminProfileResponse(
                    email=admin.email_address,
                    username=admin.user_name or admin.email_address,
                    last_login_at=admin.last_login_ts,
                    permissions=["camp_admin"],  # TODO: Implement proper permissions
                    roles=["admin"]
                )
            )
            
            return AdminAuthServiceResponse(
                success=True,
                data=login_response.dict()
            )
            
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return AdminAuthServiceResponse(
                success=False,
                error_message="Authentication failed",
                error_code="AUTH_ERROR"
            )
    
    async def register_admin(
        self,
        email: str,
        password: str,
        username: str,
        created_by: str,
        db_session
    ) -> AdminAuthServiceResponse:
        """Register a new admin account."""
        try:
            # Validate password
            print(f'current valid password:{password}', flush=True)
            print(f'username:{username}', flush=True)
            print(f'email:{email}', flush=True)
            print(f'created_by:{created_by}', flush=True)
            print(f'length of password:{len(password)}', flush=True)
            is_valid, errors = AdminPasswordValidation.validate_password(password)
            print(f'is_valid:{is_valid}', flush=True)
            print(f'errors:{errors}', flush=True)
            if not is_valid:
                return AdminAuthServiceResponse(
                    success=False,
                    error_message="; ".join(errors),
                    error_code="INVALID_PASSWORD"
                )
            
            # Check if admin already exists
            existing_admin = await Administrators.get_or_none(email_address=email)
            if existing_admin:
                return AdminAuthServiceResponse(
                    success=False,
                    error_message="Admin with this email already exists",
                    error_code="EMAIL_EXISTS"
                )
            
            # Hash password
            hashed_password = self.hash_password(password)
            print(f'hashed_password:{hashed_password}', flush=True)
            
            # Create new admin
            admin = await Administrators.create(
                email_address=email,
                password=hashed_password,
                user_name=username
            )
            
            register_response = AdminRegisterResponse(
                email=admin.email_address,
                username=admin.user_name,
                created_at=datetime.utcnow()
            )
            
            return AdminAuthServiceResponse(
                success=True,
                data=register_response.dict()
            )
            
        except IntegrityError:
            return AdminAuthServiceResponse(
                success=False,
                error_message="Admin with this email already exists",
                error_code="EMAIL_EXISTS"
            )
        except Exception as e:
            logger.error(f"Registration error: {e}")
            import traceback
            traceback.print_exc()
            return AdminAuthServiceResponse(
                success=False,
                error_message="Registration failed",
                error_code="REGISTRATION_ERROR"
            )
    
    async def get_admin_profile(
        self,
        admin_id: str,
        db_session
    ) -> AdminAuthServiceResponse:
        """Get admin profile information."""
        try:
            admin = await Administrators.get_or_none(email_address=admin_id)
            
            if not admin:
                return AdminAuthServiceResponse(
                    success=False,
                    error_message="Admin not found",
                    error_code="ADMIN_NOT_FOUND"
                )
            
            profile = AdminProfileResponse(
                email=admin.email_address,
                username=admin.user_name or admin.email_address,
                last_login_at=admin.last_login_ts,
                permissions=["camp_admin"],  # TODO: Implement proper permissions
                roles=["admin"]
            )
            
            return AdminAuthServiceResponse(
                success=True,
                data=profile.dict()
            )
            
        except Exception as e:
            logger.error(f"Get profile error: {e}")
            return AdminAuthServiceResponse(
                success=False,
                error_message="Failed to get profile",
                error_code="PROFILE_ERROR"
            )
    
    async def change_password(
        self,
        admin_id: str,
        current_password: str,
        new_password: str,
        db_session
    ) -> AdminAuthServiceResponse:
        """Change admin password."""
        try:
            admin = await Administrators.get_or_none(email_address=admin_id)
            
            if not admin:
                return AdminAuthServiceResponse(
                    success=False,
                    error_message="Admin not found",
                    error_code="ADMIN_NOT_FOUND"
                )
            
            # Verify current password
            if not self.verify_password(current_password, admin.password):
                return AdminAuthServiceResponse(
                    success=False,
                    error_message="Current password is incorrect",
                    error_code="INVALID_CURRENT_PASSWORD"
                )
            
            # Validate new password
            is_valid, errors = AdminPasswordValidation.validate_password(new_password)
            if not is_valid:
                return AdminAuthServiceResponse(
                    success=False,
                    error_message="; ".join(errors),
                    error_code="INVALID_NEW_PASSWORD"
                )
            
            # Update password
            admin.password = self.hash_password(new_password)
            await admin.save()
            
            return AdminAuthServiceResponse(
                success=True,
                data={"message": "Password changed successfully"}
            )
            
        except Exception as e:
            logger.error(f"Change password error: {e}")
            return AdminAuthServiceResponse(
                success=False,
                error_message="Failed to change password",
                error_code="PASSWORD_CHANGE_ERROR"
            )
    
    async def verify_token(self, token: str) -> AdminAuthServiceResponse:
        """Verify JWT token."""
        try:
            payload = self.verify_jwt_token(token)
            
            if not payload:
                return AdminAuthServiceResponse(
                    success=False,
                    error_message="Invalid or expired token",
                    error_code="INVALID_TOKEN"
                )
            
            verify_response = AdminTokenVerifyResponse(
                valid=True,
                admin_id=payload.get("admin_id"),
                email=payload.get("email"),
                expires_at=datetime.fromtimestamp(payload.get("exp", 0)),
                permissions=["camp_admin"]  # TODO: Get from token or database
            )
            
            return AdminAuthServiceResponse(
                success=True,
                data=verify_response.dict()
            )
            
        except Exception as e:
            logger.error(f"Token verification error: {e}")
            return AdminAuthServiceResponse(
                success=False,
                error_message="Token verification failed",
                error_code="TOKEN_VERIFICATION_ERROR"
            )
    
    async def refresh_token(self, refresh_token: str) -> AdminAuthServiceResponse:
        """Refresh access token using refresh token."""
        try:
            payload = self.verify_jwt_token(refresh_token)
            
            if not payload or payload.get("type") != "refresh":
                return AdminAuthServiceResponse(
                    success=False,
                    error_message="Invalid refresh token",
                    error_code="INVALID_REFRESH_TOKEN"
                )
            
            # Generate new access token
            admin_data = {
                "email": payload.get("email"),
                "username": payload.get("username", "")
            }
            
            new_access_token = self.generate_jwt_token(admin_data)
            
            token_response = AdminTokenResponse(
                token=new_access_token,
                refresh_token=refresh_token,  # Keep the same refresh token
                expires_in=ACCESS_TOKEN_EXPIRE_MINUTES * 60
            )
            
            return AdminAuthServiceResponse(
                success=True,
                data=token_response.dict()
            )
            
        except Exception as e:
            logger.error(f"Token refresh error: {e}")
            return AdminAuthServiceResponse(
                success=False,
                error_message="Token refresh failed",
                error_code="TOKEN_REFRESH_ERROR"
            )
    
    async def logout_admin(self, token: Optional[str] = None) -> AdminAuthServiceResponse:
        """Logout admin (invalidate token)."""
        try:
            # TODO: Implement token blacklisting if needed
            # For now, just return success as tokens will expire naturally
            
            return AdminAuthServiceResponse(
                success=True,
                data={"message": "Logged out successfully"}
            )
            
        except Exception as e:
            logger.error(f"Logout error: {e}")
            return AdminAuthServiceResponse(
                success=False,
                error_message="Logout failed",
                error_code="LOGOUT_ERROR"
            )
    
    async def forgot_password(self, email: str, db_session) -> AdminAuthServiceResponse:
        """Handle forgot password request."""
        try:
            admin = await Administrators.get_or_none(email_address=email)
            
            if admin:
                # TODO: Generate reset token and send email
                # For now, just log the request
                logger.info(f"Password reset requested for admin: {email}")
            
            # Always return success for security (don't reveal if email exists)
            return AdminAuthServiceResponse(
                success=True,
                data={"message": "If the email exists, a reset link has been sent"}
            )
            
        except Exception as e:
            logger.error(f"Forgot password error: {e}")
            return AdminAuthServiceResponse(
                success=True,  # Still return success for security
                data={"message": "If the email exists, a reset link has been sent"}
            )
    
    async def reset_password(
        self,
        reset_token: str,
        new_password: str,
        db_session
    ) -> AdminAuthServiceResponse:
        """Reset password using reset token."""
        try:
            # TODO: Implement reset token validation
            # For now, return error as reset tokens are not implemented
            
            return AdminAuthServiceResponse(
                success=False,
                error_message="Password reset not implemented",
                error_code="NOT_IMPLEMENTED"
            )
            
        except Exception as e:
            logger.error(f"Reset password error: {e}")
            return AdminAuthServiceResponse(
                success=False,
                error_message="Password reset failed",
                error_code="RESET_ERROR"
            )


# Create service instance
camp_admin_auth_services = CampAdminAuthServices()
