"""
Shared exception classes and handlers.

This module provides custom exceptions and FastAPI exception handlers
for consistent error handling across the application.
"""

from .handlers import (
    # Exception classes
    UnicornException,
    WrongContentErr,
    RunOutTimeErr,
    OathErr,
    # Security exceptions
    AuthenticationError,
    AuthorizationError,
    RateLimitError,
    # Infrastructure exceptions
    ResourceExhaustionError,
    ConfigurationError,
    NetworkError,
    # Service exceptions
    BusinessLogicError,
    ServiceException,   
    ExternalServiceError,
    # Database exceptions
    DatabaseConnectionError,
    MongoDBError,
    CacheError,
    PostgreSQLError,
    # Exception handlers
    pg_does_not_exist_handler,
    pg_operational_error_handler,
    http_error_handler,
    unicorn_exception_handler,
    wrong_content_handler,
    oath_error_handler,
    http422_error_handler,
    timeout_error_handler,
    general_handler,
)

__all__ = [
    # Exception classes
    "UnicornException",
    "WrongContentErr",
    "RunOutTimeErr",
    "OathErr",
    "BusinessLogicError",
    "ServiceException",
    "ExternalServiceError",
    # Security exceptions
    "AuthenticationError",
    "AuthorizationError",
    "RateLimitError",
    # Infrastructure exceptions
    "ResourceExhaustionError",
    "ConfigurationError",
    "NetworkError",
    # Database exceptions
    "DatabaseConnectionError",
    "MongoDBError",
    "CacheError",
    "PostgreSQLError",
    # Exception handlers
    "pg_does_not_exist_handler",
    "pg_operational_error_handler",
    "http_error_handler",
    "unicorn_exception_handler",
    "wrong_content_handler",
    "oath_error_handler",
    "http422_error_handler",
    "timeout_error_handler",
    "general_handler",
]
