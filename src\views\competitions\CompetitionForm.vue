<template>
  <div class="competition-form">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      @submit.prevent="handleSubmit"
    >
      <el-form-item label="Title" prop="title">
        <el-input v-model="form.title" placeholder="Enter competition title" />
      </el-form-item>

      <el-form-item label="Description" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="4"
          placeholder="Enter competition description"
        />
      </el-form-item>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="Type" prop="type">
            <el-select v-model="form.type" placeholder="Select type">
              <el-option label="Hackathon" value="hackathon" />
              <el-option label="Contest" value="contest" />
              <el-option label="Challenge" value="challenge" />
              <el-option label="Tournament" value="tournament" />
              <el-option label="Workshop" value="workshop" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="Category" prop="category">
            <el-select v-model="form.category" placeholder="Select category">
              <el-option label="Programming" value="programming" />
              <el-option label="Data Science" value="data_science" />
              <el-option label="AI/ML" value="ai_ml" />
              <el-option label="Web Development" value="web_development" />
              <el-option label="Mobile" value="mobile" />
              <el-option label="Design" value="design" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="Difficulty" prop="difficulty">
            <el-select v-model="form.difficulty" placeholder="Select difficulty">
              <el-option label="Beginner" value="beginner" />
              <el-option label="Intermediate" value="intermediate" />
              <el-option label="Advanced" value="advanced" />
              <el-option label="Expert" value="expert" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="Max Participants" prop="maxParticipants">
            <el-input-number
              v-model="form.maxParticipants"
              :min="1"
              :max="10000"
              placeholder="Max participants"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="Registration Start" prop="registrationStartDate">
            <el-date-picker
              v-model="form.registrationStartDate"
              type="datetime"
              placeholder="Registration start date"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="Registration End" prop="registrationEndDate">
            <el-date-picker
              v-model="form.registrationEndDate"
              type="datetime"
              placeholder="Registration end date"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="Start Date" prop="startDate">
            <el-date-picker
              v-model="form.startDate"
              type="datetime"
              placeholder="Competition start date"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="End Date" prop="endDate">
            <el-date-picker
              v-model="form.endDate"
              type="datetime"
              placeholder="Competition end date"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="Team Size Min" prop="teamSizeMin">
            <el-input-number
              v-model="form.teamSize.min"
              :min="1"
              :max="10"
              placeholder="Min team size"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="Team Size Max" prop="teamSizeMax">
            <el-input-number
              v-model="form.teamSize.max"
              :min="1"
              :max="10"
              placeholder="Max team size"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="Rules" prop="rules">
        <el-input
          v-model="form.rules"
          type="textarea"
          :rows="6"
          placeholder="Enter competition rules"
        />
      </el-form-item>

      <el-form-item label="Requirements">
        <el-input
          v-model="requirementInput"
          placeholder="Add requirement and press Enter"
          @keyup.enter="addRequirement"
        />
        <div class="requirements-list" v-if="form.requirements.length > 0">
          <el-tag
            v-for="(req, index) in form.requirements"
            :key="index"
            closable
            @close="removeRequirement(index)"
            style="margin: 4px"
          >
            {{ req }}
          </el-tag>
        </div>
      </el-form-item>

      <el-form-item label="Tags">
        <el-input
          v-model="tagInput"
          placeholder="Add tag and press Enter"
          @keyup.enter="addTag"
        />
        <div class="tags-list" v-if="form.tags.length > 0">
          <el-tag
            v-for="(tag, index) in form.tags"
            :key="index"
            closable
            @close="removeTag(index)"
            style="margin: 4px"
          >
            {{ tag }}
          </el-tag>
        </div>
      </el-form-item>

      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item>
            <el-checkbox v-model="form.isPublic">Public Competition</el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item>
            <el-checkbox v-model="form.allowTeams">Allow Teams</el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item>
            <el-checkbox v-model="form.featured">Featured</el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          Create Competition
        </el-button>
        <el-button @click="handleCancel">Cancel</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

// Props
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['submit', 'cancel'])

// Form reference
const formRef = ref()

// Form data
const form = reactive({
  title: '',
  description: '',
  type: '',
  category: '',
  difficulty: '',
  maxParticipants: 100,
  registrationStartDate: '',
  registrationEndDate: '',
  startDate: '',
  endDate: '',
  teamSize: {
    min: 1,
    max: 4
  },
  rules: '',
  requirements: [],
  tags: [],
  isPublic: true,
  allowTeams: true,
  featured: false
})

// Input helpers
const requirementInput = ref('')
const tagInput = ref('')

// Validation rules
const rules = {
  title: [
    { required: true, message: 'Please enter competition title', trigger: 'blur' }
  ],
  description: [
    { required: true, message: 'Please enter description', trigger: 'blur' }
  ],
  type: [
    { required: true, message: 'Please select competition type', trigger: 'change' }
  ],
  category: [
    { required: true, message: 'Please select category', trigger: 'change' }
  ],
  difficulty: [
    { required: true, message: 'Please select difficulty', trigger: 'change' }
  ],
  registrationStartDate: [
    { required: true, message: 'Please select registration start date', trigger: 'change' }
  ],
  registrationEndDate: [
    { required: true, message: 'Please select registration end date', trigger: 'change' }
  ],
  startDate: [
    { required: true, message: 'Please select start date', trigger: 'change' }
  ],
  endDate: [
    { required: true, message: 'Please select end date', trigger: 'change' }
  ]
}

// Methods
const addRequirement = () => {
  if (requirementInput.value.trim()) {
    form.requirements.push(requirementInput.value.trim())
    requirementInput.value = ''
  }
}

const removeRequirement = (index) => {
  form.requirements.splice(index, 1)
}

const addTag = () => {
  if (tagInput.value.trim()) {
    form.tags.push(tagInput.value.trim())
    tagInput.value = ''
  }
}

const removeTag = (index) => {
  form.tags.splice(index, 1)
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    // Convert dates to ISO strings
    const formData = {
      ...form,
      registrationStartDate: form.registrationStartDate.toISOString(),
      registrationEndDate: form.registrationEndDate.toISOString(),
      startDate: form.startDate.toISOString(),
      endDate: form.endDate.toISOString()
    }
    
    emit('submit', formData)
  } catch (error) {
    console.error('Form validation failed:', error)
  }
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.competition-form {
  .requirements-list,
  .tags-list {
    margin-top: 8px;
  }
}
</style>
