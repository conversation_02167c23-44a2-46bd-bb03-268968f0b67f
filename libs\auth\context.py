"""
Authentication utilities for the layered service architecture.

Provides consistent user_id based authentication across all service layers.
"""

from typing import Optional, Callable
from functools import wraps
from dataclasses import dataclass

from libs.errors import ErrorCode, create_error_data, ErrorCodeMapping  


@dataclass
class AuthContext:
    """Authentication context for service operations."""

    user_id: str
    is_admin: bool = False
    permissions: Optional[set[str]] = None

    def __post_init__(self):
        """Initialize default permissions."""
        if self.permissions is None:
            self.permissions = set()


def require_auth(func: Callable) -> Callable:
    """
    Decorator to ensure authentication context is provided.

    Validates that the first argument after self is an AuthContext.
    """

    @wraps(func)
    async def wrapper(self, auth_context: AuthContext | None, *args, **kwargs):
        if not isinstance(auth_context, AuthContext):
            return create_error_data(
                code=ErrorCodeMapping.get_http_status(ErrorCode.PERMISSION_DENIED),
                error_type=ErrorCode.PERMISSION_DENIED,
                message="Authentication context required",
                domain=self.__class__.__name__.lower()
            )

        if not auth_context.user_id:
            return create_error_data(
                code=ErrorCodeMapping.get_http_status(ErrorCode.PERMISSION_DENIED),
                error_type=ErrorCode.PERMISSION_DENIED,
                message="Valid user_id required",
                domain=self.__class__.__name__.lower()
            )

        return await func(self, auth_context, *args, **kwargs)

    return wrapper


def create_auth_context(
    user_id: str, is_admin: bool = False, permissions: Optional[set[str]] = None
) -> AuthContext:
    """
    Factory function to create authentication context.

    Args:
        user_id: The authenticated user's ID
        is_admin: Whether the user has admin privileges
        permissions: Set of specific permissions for the user

    Returns:
        AuthContext instance
    """
    return AuthContext(
        user_id=user_id, is_admin=is_admin, permissions=permissions or set()
    )


def validate_user_id(user_id: str) -> bool:
    """
    Validate user_id format and constraints.

    Args:
        user_id: The user ID to validate

    Returns:
        True if valid, False otherwise
    """
    if not user_id or not isinstance(user_id, str):
        return False

    # Basic validation - adjust based on your user_id format requirements
    if len(user_id.strip()) < 1:
        return False

    return True
