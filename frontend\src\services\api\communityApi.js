/**
 * Community API Service
 * Handles university and major data from the Community API
 */

import { api } from '../api.js'
import { handleApiResponse, handleApiError, buildQueryString, logApiCall } from '../../utils/apiHelpers.js'

const BASE_PATH = '/community'

/**
 * Get list of universities
 * @param {Object} params - Query parameters
 * @param {number} params.limit - Number of universities to return (max: 1000, default: -1 for all)
 * @param {number} params.offset - Number of universities to skip (default: -1)
 * @param {string} params.country - Filter by country
 * @param {string} params.province - Filter by province
 * @returns {Promise<Object>} Universities list response
 */
export const getUniversities = async (params = {}) => {
  try {
    const queryString = buildQueryString(params)
    const url = `${BASE_PATH}/universities${queryString}`
    
    logApiCall('GET', url, null, null)
    
    const response = await api.get(url)
    const result = handleApiResponse(response)
    
    logApiCall('GET', url, null, result)
    
    return {
      success: true,
      data: result.data.data || [],
      total: result.data.total,
      limit: result.data.limit,
      offset: result.data.offset
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('GET', `${BASE_PATH}/universities`, params, normalizedError)
    throw normalizedError
  }
}

/**
 * Get list of majors
 * @param {Object} params - Query parameters
 * @param {number} params.limit - Number of majors to return (1-1000, default: 20)
 * @param {number} params.offset - Number of majors to skip (default: 0)
 * @returns {Promise<Object>} Majors list response
 */
export const getMajors = async (params = {}) => {
  try {
    const queryString = buildQueryString(params)
    const url = `${BASE_PATH}/majors${queryString}`
    
    logApiCall('GET', url, null, null)
    
    const response = await api.get(url)
    const result = handleApiResponse(response)
    
    logApiCall('GET', url, null, result)
    
    return {
      success: true,
      data: result.data.data || [],
      total: result.data.total,
      limit: result.data.limit,
      offset: result.data.offset
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('GET', `${BASE_PATH}/majors`, params, normalizedError)
    throw normalizedError
  }
}

/**
 * Get content creators (currently disabled in backend)
 * @param {Object} data - Request data
 * @param {string} data.start_date - Start date for filtering
 * @returns {Promise<Object>} Content creators response
 */
export const getContentCreators = async (data) => {
  try {
    const url = `${BASE_PATH}/content_creators`
    
    logApiCall('POST', url, data, null)
    
    const response = await api.post(url, data)
    const result = handleApiResponse(response)
    
    logApiCall('POST', url, data, result)
    
    return {
      success: true,
      data: result.data.data || []
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('POST', `${BASE_PATH}/content_creators`, data, normalizedError)
    throw normalizedError
  }
}

/**
 * Get universities by country
 * @param {string} country - Country name
 * @param {Object} additionalParams - Additional query parameters
 * @returns {Promise<Object>} Universities list response
 */
export const getUniversitiesByCountry = async (country, additionalParams = {}) => {
  return getUniversities({
    country,
    ...additionalParams
  })
}

/**
 * Get universities by province
 * @param {string} province - Province name
 * @param {Object} additionalParams - Additional query parameters
 * @returns {Promise<Object>} Universities list response
 */
export const getUniversitiesByProvince = async (province, additionalParams = {}) => {
  return getUniversities({
    province,
    ...additionalParams
  })
}

/**
 * Get all universities (no pagination)
 * @returns {Promise<Object>} All universities response
 */
export const getAllUniversities = async () => {
  return getUniversities({ limit: -1, offset: -1 })
}

/**
 * Search universities by name (client-side filtering)
 * @param {string} searchTerm - Search term
 * @param {Object} additionalParams - Additional query parameters
 * @returns {Promise<Object>} Filtered universities response
 */
export const searchUniversities = async (searchTerm, additionalParams = {}) => {
  try {
    const response = await getAllUniversities()

    if (!response.success) {
      return response
    }

    let filteredData = response.data.filter(university =>
      university.university_name.toLowerCase().includes(searchTerm.toLowerCase())
    )

    // Apply additional filters if provided
    if (additionalParams.country) {
      filteredData = filteredData.filter(university =>
        university.country === additionalParams.country
      )
    }

    if (additionalParams.province) {
      filteredData = filteredData.filter(university =>
        university.province === additionalParams.province
      )
    }

    return {
      success: true,
      data: filteredData,
      total: filteredData.length,
      searchTerm
    }
  } catch (error) {
    throw error
  }
}

/**
 * Get university statistics (derived from analytics API)
 * This would typically call the analytics API for university-specific stats
 * @param {string} universityId - University ID
 * @returns {Promise<Object>} University statistics
 */
export const getUniversityStats = async (universityId) => {
  // This would be implemented when analytics API is integrated
  // For now, return a placeholder structure
  return {
    success: true,
    data: {
      university_id: universityId,
      total_students: 0,
      active_students: 0,
      total_competitions: 0,
      total_submissions: 0,
      average_score: 0
    }
  }
}

// Export all functions as a service object
export const communityApi = {
  getUniversities,
  getMajors,
  getContentCreators,
  getUniversitiesByCountry,
  getUniversitiesByProvince,
  getAllUniversities,
  searchUniversities,
  getUniversityStats
}
