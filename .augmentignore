# Dependencies and build artifacts
frontend/dist/
frontend/dist-ssr/
frontend/coverage/
frontend/node_modules

# Auto-generated files
frontend/auto-imports.d.ts
frontend/components.d.ts
frontend/.vite/

# Package lock files (too large and not useful for context)
frontend/package-lock.json
frontend/yarn.lock
frontend/pnpm-lock.yaml

# Log files
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Environment files with secrets
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Python cache and build
__pycache__/
*.py[cod]
*$py.class
*.so
build/
dist/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints

# Cache directories
.cache/
.parcel-cache/
.npm/
.eslintcache

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Large binary files
*.zip
*.tar.gz
*.tgz
*.rar
*.7z

# Database files
*.db
*.sqlite
*.sqlite3

# Media files (usually not needed for code context)
*.jpg
*.jpeg
*.png
*.gif
*.svg
*.ico
*.mp4
*.mp3
*.wav
*.pdf

# Documentation that's too large
CHANGELOG.md
CHANGELOG.txt