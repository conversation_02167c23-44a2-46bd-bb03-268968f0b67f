# ——— Version Control ———
.git
.gitignore

# ——— Documentation ———
README.md
docs/
*.md
!requirements.txt

# ——— Development Scripts ———
scripts/
examples/
tests/

# ——— Python Environment ———
__pycache__
*.pyc
*.pyo
*.pyd
.Python
.venv/
venv/
ENV/
env/
.env*
!.env.example

# ——— IDE and Editor Files ———
.vscode/
.idea/
*.swp
*.swo
*~

# ——— OS Specific Files ———
.DS_Store
.DS_Store?
._*
Thumbs.db

# ——— Testing and Coverage ———
.pytest_cache/
.coverage
.coverage.*
htmlcov/
.tox/
.nox/
.hypothesis/

# ——— Static Analysis ———
.mypy_cache/
.ruff_cache/

# ——— Logs ———
*.log
logs/
log/

# ——— Build Artifacts ———
build/
dist/
*.egg-info/

# ——— Node.js (if any) ———
node_modules/
npm-debug.log*

# ——— Database Files (for development) ———
*.db
*.sqlite3

# ——— Security Sensitive Files ———
opensshkey*
id_rsa*
*.pem
*.key

# ——— Development and Planning Files ———
plans/
migration_log/
.windsurf/
.cursor/

# ——— Temporary Files ———
*.tmp
*.temp
temp.*
