import streamlit as st

# st.title("欢迎回来，%s" % get_user_name())
st.title("和鲸内部数据中台")

pages = [
    st.Page("page/home.py", title="首页"),
    st.Page("page/community.py", title="社区相关"),
    st.Page("page/competitions.py", title="比赛相关"),
    # st.Page("pages/cs.py",title="客户成功相关"),
    st.Page("page/tags.py", title="用户打标"),
    # st.Page("pages/schools.py",title="学校统计"),
    # st.Page("pages/credits.py",title="学分发放"),
    # st.Page("pages/competitions.py",title="学分规则管理"),
    # st.Page("pages/utils.py",title="下载/工具"),
]
st.error(
    "注意：:red[请勿刷新浏览器]，如需刷新页面，点击右上角:blue[[...]] 选择 :blue[rerun]"
)
st.divider()

pg = st.navigation(pages)
pg.run()
