"""
Competition Management API Router.

Handles all competition-related endpoints organized by business function:
- Routes (high-level categories)
- Competitions (events within routes)
- Qualifications (standards for earning credits)
- Credits (points earned through qualifications)
- Badges (rewards for accumulated credits)
"""


from fastapi import APIRouter, Query, Body, Depends
from typing import Optional
import logging

from .schemas import (
    QualificationCreateRequest,
    CreditAwardRequest,
    CreditRevokeRequest,
    AdminLogsCreateRequest,
    RouteListResponse,
    CompetitionListResponse,
    QualificationListResponse
)
from .auth_routes import router as auth_router
from libs.auth.authentication import (
    get_current_user_id,
    get_current_admin_id,
)
from libs.validation import validate_pagination
from core.database.dependencies import (
    get_read_only_session,
    get_transaction_session,
)
from libs.schemas.api.responses import (
    success,
    SuccessResponse,
    ErrorResponse,
    AntdTableResponse,
)
from libs.schemas.api.base import PaginationConfig,BaseDataPostResponse
from .services import camp_admin_services

logger = logging.getLogger(__name__)

# Create router with prefix for competition endpoints
router = APIRouter(
    prefix="/camp-admin",
    tags=["camp-admin"],
    responses={404: {"description": "Not found"}},
)

# Include auth routes
router.include_router(auth_router)

# Routes Management
@router.get(
    "/routes",
    response_model=SuccessResponse | ErrorResponse | AntdTableResponse,
    summary="获取活动赛道",
    description="获取所有活动赛道",
)
async def list_routes(
    user_id: str = Depends(get_current_user_id),
    db_session = Depends(get_read_only_session),
    limit: int = Query(PaginationConfig.NO_LIMIT, le=100, description="Number of routes to return"),
    offset: int = Query(PaginationConfig.NO_OFFSET, description="Number of routes to skip"),

):
    """List all available competition routes."""
    result:RouteListResponse = await camp_admin_services.get_routes(limit=limit, offset=offset, db_session=db_session)
    return success(data=result.data)


# Competition Management
@router.get(
    "/",
    response_model=SuccessResponse | ErrorResponse | AntdTableResponse,
    summary="获取活动",
    description="获取所有活动，可以按照单一赛道 id 进行筛选",
)
async def list_competitions(
    limit: int = Query(PaginationConfig.NO_LIMIT, le=1000, description="Number of competitions to return"),
    offset: int = Query(PaginationConfig.NO_OFFSET, description="Number of competitions to skip"),
    route_id: Optional[str] = Query(None, description="Filter by route ID"),
    status: Optional[str] = Query(None, description="Filter by competition status"),
    user_id: str = Depends(get_current_user_id),    
    db_session = Depends(get_read_only_session), 
):
    """List competitions with optional filtering."""
    result:CompetitionListResponse = await camp_admin_services.get_competitions(
        route_id=route_id,
        status=status,
        limit=limit,
        offset=offset,
        db_session=db_session,
    )
    return success(data=result.data)


# Qualification Management
@router.get(
    "/qualifications",
    response_model=SuccessResponse | ErrorResponse | AntdTableResponse,
    summary="获取活动得分规则",
    description="获取活动得分规则，可以按照单一活动 id 进行筛选",
)
async def list_qualifications(
    competition_id: Optional[str] = Query(None, description="Filter by competition ID"),
    limit: int = Query(PaginationConfig.NO_LIMIT, le=1000, description="Number of qualifications to return"),
    offset: int = Query(PaginationConfig.NO_OFFSET, description="Number of qualifications to skip"),
    user_id: str = Depends(get_current_user_id),
    db_session = Depends(get_read_only_session),
):
    """List qualifications for a specific competition."""
    result:QualificationListResponse = await camp_admin_services.get_qualifications(
        competition_id=competition_id,
        limit=limit,
        offset=offset,
        db_session=db_session,
    )
    return success(data=result.data)


@router.post(
    "/qualifications",
    summary="创建活动得分规则",
    description="创建活动得分规则，仅限管理员操作",
)
async def create_qualification(
    qualification_data: QualificationCreateRequest = Body(...,
                                                          examples=[
                                                              {
                                                                  "competition_id": "123",
                                                                  "credit": 100,
                                                                  "qualification_type": 1,
                                                                  "qualification_logic": 1,
                                                                  "qualification_name": "得分规则名称",
                                                                  "related_task_id": "123"
                                                              }
                                                          ]),
    user_id: str = Depends(get_current_user_id),
    tx_session = Depends(get_transaction_session),
):
    """Create a new qualification standard."""

    qualification_id = await camp_admin_services.create_qualification(
        qualification_data=qualification_data,
        db_session=tx_session,
    )
    return success(data=qualification_id)


# Credit Management
@router.get(
    "/credits/history",
    response_model=SuccessResponse | ErrorResponse | AntdTableResponse,
    summary="获取学分发放历史记录",
    description="获取学分发放历史记录，可以按照单一用户 id 或活动 id 进行筛选",
)
async def get_credit_history(
    current_user_id: str = Depends(get_current_user_id),
    db_session = Depends(get_read_only_session),
    competition_id: Optional[str] = Query(None, description="Filter by competition ID"),
    user_id_filter: Optional[str] = Query(
        None, alias="user_id", description="Filter by user ID"
    ),
    limit: int = Query(200, ge=1, le=1000, description="Number of records to return"),
    offset: int = Query(0, ge=0, description="Number of records to skip"),
    ):
    """Get credit transaction history with filtering options."""
    # limit, offset = validate_pagination(limit, offset)
    result = await camp_admin_services.get_credit_logs(
        competition_id=competition_id,
        user_id=user_id_filter,
        limit=limit,
        offset=offset,
        db_session=db_session,
    )
    return success(data=result.data)


@router.post(
    "/credits/award",
    summary="学分发放",
    description="学分发放，可以按照单一用户 id 或活动 id 进行筛选",
)
async def award_credits(
    credit_data: CreditAwardRequest = Body(...),
    admin_id: str = Depends(get_current_admin_id),
    tx_session = Depends(get_transaction_session),
):
    """Award credits to a user for qualifying in a competition."""
    # Set admin_id in the request data
    # credit_data.admin_id = admin_id
    
    result = await camp_admin_services.award_credits(
        credit_log_data=credit_data,
        db_session=tx_session,
    )
    return success(data=result)


@router.post(
    "/credits/revoke",
    summary="撤销学分发放",
    description="撤销学分发放，仅限管理员操作，必须提供撤销原因",
)
async def revoke_credits(
    revoke_data: CreditRevokeRequest = Body(...),
    admin_id: str = Depends(get_current_admin_id),
    tx_session = Depends(get_transaction_session),
):
    """Revoke a previously awarded credit record."""
    await camp_admin_services.revoke_credits(
        record_id=revoke_data.record_id,
        reason="Admin revocation",
        tx_session=tx_session,
    )
    return success(data={"message": "Credits revoked successfully"})

# admin logs management
@router.get(
    "/logs",
    summary="获取管理员操作日志",
    description="获取管理员操作日志",
)
async def get_admin_logs(
    limit: int = Query(20, ge=1, le=100, description="Number of logs to return"),
    offset: int = Query(0, ge=0, description="Number of logs to skip"),
    user_id: str = Depends(get_current_user_id),
    db_session = Depends(get_read_only_session),
): 
    """Get a list of admin logs."""
    limit, offset = validate_pagination(limit, offset)
    result = await camp_admin_services.get_admin_logs(limit=limit, offset=offset, db_session=db_session)
    return success(data=result.data)


@router.post(
    "/logs/create", 
    summary="【慎用】创建管理员操作日志",
    description="创建管理员操作日志，仅限超级管理员操作",
    
)
async def create_admin_log(
    log_data: AdminLogsCreateRequest = Body(...),
    user_id: str = Depends(get_current_user_id),
    tx_session = Depends(get_transaction_session),
):
    """Create a new admin log."""
    log_data.admin_email_id = user_id
    response: BaseDataPostResponse = await camp_admin_services.create_admin_log(log_data=log_data, db_session=tx_session)
    return success(data=response.id,msg=response.message)
