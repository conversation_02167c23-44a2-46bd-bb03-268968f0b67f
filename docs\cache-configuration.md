# Cache Configuration Guide

This guide covers all configuration options for the Redis-based caching system.

## Environment Configuration

### Redis Connection Settings

```bash
# Basic Redis connection
REDIS_HOST=localhost                    # Redis server hostname
REDIS_PORT=6379                        # Redis server port
REDIS_PASSWORD=your_secure_password     # Redis password (optional)
REDIS_DATABASE=0                       # Redis database number (0-15)

# Advanced connection settings
REDIS_MAX_CONNECTIONS=20               # Maximum connections in pool
REDIS_SOCKET_TIMEOUT=5                 # Socket timeout in seconds
REDIS_SOCKET_CONNECT_TIMEOUT=5         # Connection timeout in seconds

# Alternative: Complete Redis URL
REDIS_URL=redis://username:password@localhost:6379/0
```

### Cache Behavior Settings

```bash
# Global cache settings
CACHE_ENABLED=true                     # Enable/disable caching globally
CACHE_DEFAULT_TTL=300                  # Default TTL in seconds (5 minutes)
CACHE_KEY_PREFIX=community_services    # Prefix for all cache keys

# Operation-specific TTL settings
CACHE_USER_PROFILE_TTL=600            # User profile cache TTL (10 minutes)
CACHE_USER_SEARCH_TTL=180             # User search cache TTL (3 minutes)
CACHE_HEYWHALE_TAGS_TTL=3600          # HeyWhale tags cache TTL (1 hour)
```

## Production Configuration

### High-Traffic Setup

```bash
# Increased connection pool for high traffic
REDIS_MAX_CONNECTIONS=100
REDIS_SOCKET_TIMEOUT=10
REDIS_SOCKET_CONNECT_TIMEOUT=10

# Optimized TTL for production
CACHE_DEFAULT_TTL=600                  # 10 minutes default
CACHE_USER_PROFILE_TTL=1800           # 30 minutes for profiles
CACHE_USER_SEARCH_TTL=300             # 5 minutes for search
CACHE_HEYWHALE_TAGS_TTL=7200          # 2 hours for tags
```

### Redis Cluster Configuration

For Redis cluster setups:

```bash
# Redis cluster nodes (comma-separated)
REDIS_CLUSTER_NODES=redis1:6379,redis2:6379,redis3:6379
REDIS_CLUSTER_PASSWORD=cluster_password
REDIS_CLUSTER_SKIP_FULL_COVERAGE_CHECK=false
```

### Redis Sentinel Configuration

For Redis Sentinel setups:

```bash
# Sentinel configuration
REDIS_SENTINEL_HOSTS=sentinel1:26379,sentinel2:26379,sentinel3:26379
REDIS_SENTINEL_SERVICE_NAME=mymaster
REDIS_SENTINEL_PASSWORD=sentinel_password
```

## Development Configuration

### Local Development

```bash
# Minimal setup for local development
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DATABASE=1                       # Use different DB for dev
CACHE_ENABLED=true
CACHE_DEFAULT_TTL=60                   # Shorter TTL for testing
```

### Docker Compose Setup

```yaml
# docker-compose.yml
version: '3.8'
services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --requirepass your_password
    volumes:
      - redis_data:/data

  app:
    build: .
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=your_password
      - CACHE_ENABLED=true
    depends_on:
      - redis

volumes:
  redis_data:
```

## Security Configuration

### Authentication

```bash
# Redis authentication
REDIS_PASSWORD=your_very_secure_password_here
REDIS_USERNAME=cache_user              # Redis 6+ ACL username

# TLS/SSL configuration
REDIS_SSL=true
REDIS_SSL_CERT_PATH=/path/to/client.crt
REDIS_SSL_KEY_PATH=/path/to/client.key
REDIS_SSL_CA_PATH=/path/to/ca.crt
```

### Network Security

```bash
# Bind Redis to specific interface
REDIS_BIND=127.0.0.1                  # Local only
# REDIS_BIND=0.0.0.0                  # All interfaces (not recommended)

# Firewall rules (example for iptables)
# iptables -A INPUT -p tcp --dport 6379 -s trusted_ip -j ACCEPT
# iptables -A INPUT -p tcp --dport 6379 -j DROP
```

## Monitoring Configuration

### Redis Monitoring

```bash
# Enable Redis monitoring
REDIS_MONITOR_ENABLED=true
REDIS_MONITOR_INTERVAL=60              # Monitor every 60 seconds
REDIS_MONITOR_ALERT_THRESHOLD=0.8      # Alert when memory usage > 80%

# Logging configuration
REDIS_LOG_LEVEL=INFO
REDIS_LOG_FILE=/var/log/redis/redis.log
```

### Application Monitoring

```bash
# Cache metrics collection
CACHE_METRICS_ENABLED=true
CACHE_METRICS_INTERVAL=300             # Collect metrics every 5 minutes
CACHE_METRICS_RETENTION=86400          # Keep metrics for 24 hours
```

## Performance Tuning

### Memory Configuration

```bash
# Redis memory settings
REDIS_MAXMEMORY=2gb                    # Maximum memory usage
REDIS_MAXMEMORY_POLICY=allkeys-lru     # Eviction policy

# Cache size limits
CACHE_MAX_KEY_SIZE=1048576             # 1MB max key size
CACHE_MAX_VALUE_SIZE=10485760          # 10MB max value size
```

### Connection Optimization

```bash
# Connection pool tuning
REDIS_MAX_CONNECTIONS=50               # Adjust based on load
REDIS_MIN_CONNECTIONS=5                # Minimum pool size
REDIS_CONNECTION_RETRY_ATTEMPTS=3      # Retry failed connections
REDIS_CONNECTION_RETRY_DELAY=1         # Delay between retries (seconds)
```

## Environment-Specific Examples

### Development (.env.development)

```bash
# Development environment
ENVIRONMENT=development
DEBUG=true

# Redis (local)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DATABASE=1
REDIS_MAX_CONNECTIONS=10

# Cache (aggressive for testing)
CACHE_ENABLED=true
CACHE_DEFAULT_TTL=30
CACHE_USER_PROFILE_TTL=60
CACHE_USER_SEARCH_TTL=30
CACHE_HEYWHALE_TAGS_TTL=300
```

### Staging (.env.staging)

```bash
# Staging environment
ENVIRONMENT=staging
DEBUG=false

# Redis (staging server)
REDIS_HOST=staging-redis.internal
REDIS_PORT=6379
REDIS_PASSWORD=staging_password
REDIS_DATABASE=0
REDIS_MAX_CONNECTIONS=30

# Cache (moderate TTL)
CACHE_ENABLED=true
CACHE_DEFAULT_TTL=300
CACHE_USER_PROFILE_TTL=900
CACHE_USER_SEARCH_TTL=180
CACHE_HEYWHALE_TAGS_TTL=1800
```

### Production (.env.production)

```bash
# Production environment
ENVIRONMENT=production
DEBUG=false

# Redis (production cluster)
REDIS_HOST=prod-redis-cluster.internal
REDIS_PORT=6379
REDIS_PASSWORD=super_secure_production_password
REDIS_DATABASE=0
REDIS_MAX_CONNECTIONS=100
REDIS_SOCKET_TIMEOUT=10

# Cache (optimized TTL)
CACHE_ENABLED=true
CACHE_DEFAULT_TTL=600
CACHE_USER_PROFILE_TTL=1800
CACHE_USER_SEARCH_TTL=300
CACHE_HEYWHALE_TAGS_TTL=3600

# Security
REDIS_SSL=true
REDIS_SSL_CERT_PATH=/etc/ssl/certs/redis-client.crt
REDIS_SSL_KEY_PATH=/etc/ssl/private/redis-client.key
```

## Configuration Validation

### Startup Checks

The application performs these validation checks on startup:

1. **Redis Connectivity**: Verifies connection to Redis server
2. **Authentication**: Tests Redis authentication if configured
3. **Memory Limits**: Checks Redis memory configuration
4. **TTL Values**: Validates TTL settings are positive integers
5. **Key Prefix**: Ensures cache key prefix is valid

### Health Check Endpoint

Monitor configuration health via API:

```bash
# Check cache health
curl http://localhost:8000/api/users/cache/health

# Expected response
{
  "status": "healthy",
  "latency_ms": 2,
  "connection": "aioredis",
  "backend": "redis",
  "memory_usage": "45.2MB",
  "connected_clients": 12
}
```

## Troubleshooting Configuration

### Common Configuration Issues

#### 1. Connection Refused

```bash
# Check Redis is running
redis-cli ping

# Check network connectivity
telnet redis_host 6379

# Verify configuration
echo $REDIS_HOST
echo $REDIS_PORT
```

#### 2. Authentication Failed

```bash
# Test authentication
redis-cli -h $REDIS_HOST -p $REDIS_PORT -a $REDIS_PASSWORD ping

# Check password in environment
echo $REDIS_PASSWORD
```

#### 3. Memory Issues

```bash
# Check Redis memory usage
redis-cli info memory

# Monitor memory in real-time
redis-cli --latency-history -i 1
```

### Configuration Testing

Test your configuration with this script:

```python
import asyncio
from core.config.settings import get_settings
from core.cache.client import get_cache_client

async def test_cache_config():
    settings = get_settings()
    print(f"Redis URL: {settings.redis_url}")
    print(f"Cache enabled: {settings.cache_enabled}")
    print(f"Default TTL: {settings.cache_default_ttl}")
    
    try:
        cache_client = await get_cache_client()
        await cache_client.set("test_key", "test_value", ttl=60)
        value = await cache_client.get("test_key")
        print(f"Cache test: {'PASS' if value == 'test_value' else 'FAIL'}")
        await cache_client.delete("test_key")
    except Exception as e:
        print(f"Cache test failed: {e}")

# Run the test
asyncio.run(test_cache_config())
```

This configuration guide ensures your caching system is properly set up for your specific environment and requirements. 