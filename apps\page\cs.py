import streamlit as st

import streamlit.components.v1 as components

iframe = "http://dify-dev-gmci1brccz.heywhale.com/chatbot/JSB3Cig8PbDSWXi8"

components.iframe(iframe, height=500, scrolling=True)
st.title("埋点数据")


# @st.cache_data()
# def get_event_tracks_list():
#     response = get_event_tracks().json()
#     if len(response['data']) == 0 and response['message']!='success':
#         st.error('未找到埋点数据')
#         return None
#     elif response['message']=='success':
#         return response['data']
#     else:
#         st.error('获取埋点数据失败')
#         return None


# event_tracks = get_event_tracks_list()
# if event_tracks is not None:
#     st.dataframe(event_tracks,use_container_width=True)
