import streamlit as st
import pandas as pd
from datetime import datetime
from libs.api import (
    get_tags,
    update_user_tags,
    sync_shence_tag,
    query_user_id_by,
    query_tag_user,
    get_shence_tag_dir,
)

if "select_tag" not in st.session_state:
    st.session_state.select_tag = ""

if "user_ids" not in st.session_state:
    st.session_state.user_ids = []

if "data" not in st.session_state:
    st.session_state.data = ""

if "dir_meta" not in st.session_state:
    st.session_state.dir_meta = {}

if "tags" not in st.session_state:
    st.session_state.tags = {}

if "tag_cname" not in st.session_state:
    st.session_state.tag_cname = ""


@st.cache_data(
    ttl=3600,  # Cache for 1 hour
    max_entries=None,  # Cache for unlimited entries
)
def get_tag_lst():
    """
    获取标签元数据
    """
    response = get_tags({}).json()
    if len(response["data"]) == 0 and response["message"] != "success":
        st.error("未找到标签")
        return []
    elif response["message"] == "success":
        data = response["data"]
        flattened = {}
        for cat in data:
            cat_name = cat["Name"]
            cat_id = cat["_id"]
            children = []
            for child in cat["children"]:
                flattened.update(
                    {cat_name + " - " + child["Name"]: cat_id + " - " + child["_id"]}
                )
        return flattened
    else:
        st.error("获取专业数据失败")
        return []


@st.cache_data(
    ttl=3600,  # Cache for 1 hour
    max_entries=None,  # Cache for unlimited entries
)
def get_shence_dir_meta():
    """
    获取神策标签目录元数据
    """
    response = get_shence_tag_dir().json()
    if response["message"] == "success":
        return response["data"]
    else:
        st.error("获取识��路径元数据失败")
        return []


@st.experimental_dialog("开始下载")
def download_data(file_name, hinter="用户身份信息"):
    st.write("即将下载【{}】的数据，请确认".format(hinter))
    st.download_button(
        label="确认",
        data=st.session_state.data,
        file_name="{}.csv".format(file_name),
        mime="text/csv",
        use_container_width=True,
    )


@st.cache_data
def convert_df(df):
    # IMPORTANT: Cache the conversion to prevent computation on every rerun
    return df.to_csv(index=False).encode("utf-8-sig")


# print(st.session_state.tags,st.session_state.dir_meta)
# tags = st.session_state.tags or get_tag_lst()
tags = get_tag_lst()

# shence_dir:list[dict] = st.session_state.dir_meta or get_shence_tag_dir().json()['data']
shence_dir: list[dict] = get_shence_dir_meta()


st.session_state.tags = tags
st.session_state.dir_meta = shence_dir

# 过滤掉奇怪的数据
valid_tags = {
    k: v for k, v in tags.items() if ("内部员工" not in k) and ("运营负责人" not in k)
}
valid_taggers = {
    k: v for k, v in tags.items() if ("内部员工" in k) or ("运营负责人" in k)
}

# 神策标签元数据目录
dir_meta = {item["id"]: item["cname"] for item in shence_dir if item["id"] != 2}
dir_meta_detail = {item["id"]: item["items"] for item in shence_dir if item["id"] != 2}


select_tag = st.selectbox(
    "请选择标签", options=valid_tags.keys(), key="tag_box", index=None
)
# st.session_state.tag_cname = select_tag

tagger = st.selectbox(
    "当前用户", options=valid_taggers.keys(), key="tagger_box", index=None
)

tag, query = st.tabs(["我要打标", "我要查询"])


with tag:
    if select_tag:
        st.session_state.select_tag = select_tag
        tag_id = valid_tags[st.session_state.select_tag].split("-")[1].strip()

    st.info("【建议】如使用手机号或邮箱打标，单次输入建议不超过 100 个手机号/邮箱")
    by_ids, by_phones, by_emails, uploader_toggle, shence_toggle = st.columns(
        [1.5, 1.5, 1.5, 2.5, 2.5]
    )
    use_id = by_ids.checkbox("user_id ", value=True)
    use_phone = by_phones.checkbox("手机号 ", value=False)
    use_email = by_emails.checkbox("邮箱 ", value=False)
    uploader_toggle = uploader_toggle.toggle("通过  上传文件", value=False)
    sync_shence = shence_toggle.toggle(
        "添加到神策", help="将会在神策当中创建一个新的用户标签"
    )

    if (use_id + use_phone + use_email) == 0:
        st.warning("请至少选择一个打标方式")

    if (use_id + use_phone + use_email) > 1:
        st.warning("无法同时选择多个打标方式")

    if (use_id + use_phone + use_email) == 1 and not uploader_toggle:
        label_name = "手机号" if use_phone else "邮箱" if use_email else "user_id"
        place_holder = (
            "12345678910" if use_phone else "<EMAIL>" if use_email else "abcd123456"
        )
        user_id = st.text_area(
            label_name,
            placeholder=f"每行一个{label_name}:\n\n{place_holder}\n...\n\n【重要！】请检查是否有重复的输入",
            help="请检查是否有重复的输入",
            height=350,
        )
        if user_id:
            user_ids = [
                user_id.strip() for user_id in user_id.split("\n") if user_id.strip()
            ]
            user_ids = query_user_id_by(
                "Phone" if use_phone else "Email" if use_email else "user_id",
                {"ids": user_ids},
            ).json()["data"]
            st.session_state.user_ids = user_ids

    if (use_id + use_phone + use_email) == 1 and uploader_toggle:
        label_name = "手机号" if use_phone else "邮箱" if use_email else "user_id"
        place_holder = (
            "12345678910" if use_phone else "<EMAIL>" if use_email else "abcd123456"
        )

        upload, download = st.columns([5, 1], vertical_alignment="center")
        sample_download = download.download_button(
            label="下载示例",
            data=pd.DataFrame({f"{label_name}": [f"{place_holder}", f"{place_holder}"]})
            .to_csv(index=False, encoding="utf-8-sig")
            .encode("utf-8-sig"),
            file_name="{}示例.csv".format(label_name),
            mime="text/csv",
            use_container_width=True,
        )
        file = upload.file_uploader(
            f"请上传包含 {label_name} 的 csv 文件",
            help="单个文件大小不得超过 200M",
            type=["csv"],
        )
        if file:
            try:
                df = pd.read_csv(file)
                st.info(f"csv 读取成功，共计 {df.shape[0]} 行数据")
                user_ids = df[f"{label_name}"].tolist()
            except Exception as e:
                st.error("csv 格式不正确:", e)
                st.info("可以下载样例查看")

    tag_name = ""
    if sync_shence:
        st.warning("【注意】目前只开放覆盖更新。如果存在相同的标签名，将会覆盖原有标签")
        if not tag_id or not tagger:
            st.error("请选择【标签】和 【当前用户】")
        tname, add_ts, dname = st.columns([2, 1, 1], vertical_alignment="center")

        add_timestamp = add_ts.toggle(
            "添加时间戳", help="帮助识别标签创建的时间", value=False
        )
        add_tagger = add_ts.toggle("添加创建者", help="帮助识别标签创建者", value=False)
        tagger_info = "【" + tagger + "】" if add_tagger else ""
        time_info = (
            "@" + datetime.now().strftime("%Y-%m-%d %H:%M") if add_timestamp else ""
        )
        tag_name = tname.text_input(
            "标签名", value=tagger_info + st.session_state.tag_cname + time_info
        )
        st.session_state.tag_cname = (
            tag_name
            if not tagger_info and not time_info
            else st.session_state.tag_cname
        )
        dir_id = dname.selectbox(
            "选择要添加的目录",
            options=dir_meta.keys(),
            format_func=lambda x: dir_meta[x],
        )

    if st.button("确认并打标"):
        if not user_ids:
            st.error("请先输入 user_id 或者 手机号/邮箱")

        if not tag_id or not tagger:
            st.error("请选择【标签】和 【当前用户】")

        if not tag_name or not dir_id:
            st.error("请先输入 标签名 和 所添加的目录")

        ready_to_sync = tag_name and dir_id if sync_shence else True
        ready_to_tag = user_ids and tag_id and tagger

        if ready_to_tag:
            filtered_ids = [d["user_id"] for d in user_ids]
            response = update_user_tags(
                {"tag_id": tag_id, "users": filtered_ids}
            ).json()["data"]
            if len(response["invalidData"]):
                st.error("存在无效的 user_id：", "\n".join(response["invalidData"]))
            else:
                st.success("打标成功")

        if ready_to_sync:
            resp = sync_shence_tag(
                {"tag_name": tag_name, "data": filtered_ids, "dir_id": dir_id}
            )
            if len(resp.json()["data"]):
                st.success("添加到神策成功")

with query:
    mapping = {
        "name": "用户名",
        "phone": "手机号",
        "email": "邮箱",
    }
    fields = st.multiselect(
        "查询的字段", options=mapping.keys(), format_func=lambda x: mapping[x]
    )
    query, download = st.columns([1, 1])
    if query.button(
        "在线查看", use_container_width=True, help="目前仅支持查看结果前 30 条"
    ):
        result = query_tag_user(tag_id, {"fields": fields}).json()["data"]
        with st.expander("预览查询结果"):
            st.dataframe(
                pd.DataFrame(result).head(30), height=500, use_container_width=True
            )
            if len(result) > 30:
                st.info("共计 {} 条数据。更多结果请下载查看".format(len(result)))

    if download.button("下载结果", use_container_width=True):
        result = query_tag_user(tag_id, {"fields": fields}).json()["data"]
        df = pd.DataFrame(result)
        st.session_state.data = convert_df(df)
        download_data(f"查询结果_{tag_id}", hinter=f" {select_tag} ")
