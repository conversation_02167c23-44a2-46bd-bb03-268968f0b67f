"""
User Management API Router.

Handles all user-related endpoints organized by business function:
- User profile management
- User discovery and search operations
- User identification and resolution
- User preferences and settings
- HeyWhale platform integration (badges and tags)
"""

from typing import List, Literal, Optional
from fastapi import APIRouter, Query, Body, Depends, Path, Request
import logging

from libs.mongo.schemas import MatchStageSpec
from core.cache.decorators import cached, cache_response, invalidate_cache
from core.cache.utils import generate_cache_key
from core.cache.manager import get_cache_manager

from .schemas import (
    UserProfileListResponse,
)
from gateway.integrations.schemas import BadgeSyncBatchRequest, PostTagRequest
from libs.auth import get_current_user_id
from libs.auth.permissions import (
    require_user_read_permission,
    require_user_discovery_permission,
)
from gateway.integrations.dependencies import require_heywhale_access_permission
from core.database.dependencies import get_mongo_kesci_db

from .services import user_services
from gateway.integrations.schemas import TagType
from libs.schemas.api.base import PaginationConfig,BaseDataPostResponse
from libs.schemas.api.responses import SuccessResponse, ErrorResponse

logger = logging.getLogger(__name__)


# Create router with global authentication dependency
router = APIRouter(
    prefix="/users",
    tags=["users"],
    dependencies=[Depends(get_current_user_id)],  # Authentication only
    responses={404: {"description": "Not found"}},
)

# Services are called directly from routes

# ——— User profile endpoints ———

@router.get(
    "/{user_id}/profile",
    response_model=SuccessResponse | ErrorResponse,
    summary="获取用户信息",
    description="""
                获取某个用户的详细信息，获取的信息类似分别为:\n
                basic: 用户的基本信息\n
                dynamic: 用户的基本信息+动态信息\n
                student: 用户的基本信息+学生信息\n
                rich: 用户的基本信息+学生信息+动态信息\n
                """,
)
@cache_response(
    domain="users",
    operation="profile",
    ttl=600,  # 10 minutes
    key_params=["user_id", "profile_type"],
    user_param="user_id"
)
async def get_user_profile(
    current_user_id: str = Depends(get_current_user_id),
    database = Depends(get_mongo_kesci_db),
    user_id: str = Path(..., description="用户 ID"),
    profile_type: str = Query(
        "basic", description="用户输出用户的字段类型 (basic, dynamic, student, rich)"
    ),
):
    """Get user profile by ID.
    
    获取某个用户的信息
    """
    # Check permission manually
    await require_user_read_permission(current_user_id)
    
    # Call service directly - exceptions bubble up to FastAPI handlers
    result = await user_services.get_user_profile(
        user_id=user_id,
        collection=database["users"],
        profile_type=profile_type
    )
    
    # Wrap successful response in SuccessResponse
    return SuccessResponse(data=result)

# ——— User discovery endpoints ———


@router.post(
    "/discovery",
    response_model=SuccessResponse | ErrorResponse,
    summary="根据特定的标准筛选出用户",
    description="根据用户注册时间、地点、学校、专业、身份、经验等条件，筛选出用户",
)
@cache_response(
    domain="users",
    operation="discovery",
    ttl=180,  # 3 minutes
    key_params=["profile_type", "limit", "offset", "days", "spec"]
)
async def search_user_by_query(
    current_user_id: str = Depends(get_current_user_id),
    database = Depends(get_mongo_kesci_db),
    profile_type: str = Query(
        "basic", description="Profile type (basic, dynamic, student, rich)"
    ),
    days: int = Query(365, ge=1, le=365, description="Number of days to search"),
    limit: int = Query(PaginationConfig.NO_LIMIT, le=1000, description="Number of users to return"),
    offset: int = Query(PaginationConfig.NO_OFFSET, description="Number of users to skip"),
    spec: MatchStageSpec = Body(...),
):
    """Discover users based on various criteria.
    
    根据用户注册时间、地点、学校、专业、身份、经验等条件，发现用户
    """
    # Check discovery permission manually
    await require_user_discovery_permission(current_user_id)
    
    
     # Call service directly - exceptions bubble up to FastAPI handlers
    result: UserProfileListResponse = await user_services.search_users_by_query(
        spec=spec,
        collection=database["users"],
        profile_type=profile_type,
        days=days,
        limit=limit,
        offset=offset
    )
    
    # Wrap successful response in SuccessResponse
    return SuccessResponse(data=result)

# ——— User search endpoints ———


@router.get(
    "/search",
    response_model=SuccessResponse | ErrorResponse,
    summary="根据特定的标准搜索用户",
    description="根据用户姓名 或 邮箱 或手机号，搜索用户",
)
@cache_response(
    domain="users",
    operation="search",
    ttl=180,  # 3 minutes
    key_params=["queries", "search_type", "limit", "offset", "profile_type"]
)
async def search_user_by_identifier(
    current_user_id: str = Depends(get_current_user_id),
    database = Depends(get_mongo_kesci_db),
    limit: PaginationConfig | int = Query(PaginationConfig.NO_LIMIT, le=1000, description="Number of users to return"),
    offset: PaginationConfig | int = Query(PaginationConfig.NO_OFFSET, description="Number of users to skip"),
    profile_type: str = Query(
        "basic", description="Profile type (basic, dynamic, student, rich)"
    ),
    queries: List[str] = Query(..., description="用户 id 或 姓名 或 邮箱 或手机号的列表"),
    search_type: Literal["name", "email", "phone", "user_id"] = Query(
        "name", description="搜索类型 (name, email, phone, user_id)"
    )
):
    """Search users by various criteria.
    
    根据用户姓名、邮箱、手机号、学校等条件，搜索用户
    """
    # Call service directly - exceptions bubble up to FastAPI handlers
    result:UserProfileListResponse = await user_services.search_users_by_identifier(
        queries=queries,
        search_type=search_type,
        limit=limit,
        offset=offset,
        collection=database["users"],
        profile_type=profile_type
    )
    
    # Wrap successful response in SuccessResponse
    return SuccessResponse(data=result.data)  


# ——— HeyWhale Integration endpoints ———


@router.get(
    "/integrations/heywhale/tags/tree",
    response_model=SuccessResponse | ErrorResponse,
    summary="获取 HeyWhale 标签树",
    description="获取 HeyWhale 的标签树，用于用户管理",
)
@cache_response(
    domain="users",
    operation="tags",
    ttl=3600,  # 1 hour - tags don't change frequently
)
async def get_heywhale_tags(
    current_user_id: str = Depends(get_current_user_id),
    database = Depends(get_mongo_kesci_db),
):
    """Get HeyWhale tag tree for user management.
    
    获取HeyWhale的标签树，调用 Heywhale 的外部服务
    """
    # Check HeyWhale access permission manually
    await require_heywhale_access_permission(current_user_id)
    
    # Call service directly - exceptions bubble up to FastAPI handlers
    result:BaseDataPostResponse = await user_services.get_heywhale_tags(
        tag_type=TagType.USER,
        database=database
    )
    
    # Return result directly (already in correct format for HeyWhale endpoints)
    return SuccessResponse(data=result.data,
                           message=result.message)


@router.post(
    "/integrations/heywhale/badges/sync",
    response_model=SuccessResponse | ErrorResponse,
    summary="同步用户徽章到 HeyWhale",
    description="同步用户徽章到 HeyWhale 平台",
)
# @invalidate_cache(domain="users", operation="profile")  # Invalidate profile cache after badge sync
async def sync_user_badges_heywhale(
    badge_data: BadgeSyncBatchRequest = Body(...),
    current_user_id: str = Depends(get_current_user_id),
    database = Depends(get_mongo_kesci_db),
):
    """Sync user badges with HeyWhale platform.
    
    同步用户徽章到HeyWhale平台
    """
    # Check HeyWhale access permission manually
    await require_heywhale_access_permission(current_user_id)
    
    # Call service directly - exceptions bubble up to FastAPI handlers
    result:BaseDataPostResponse = await user_services.sync_user_badges_heywhale(
        badge_data=badge_data.users,
        database=database
    )
    
    # Return result directly (already in correct format for HeyWhale endpoints)
    return SuccessResponse(data=result.data,
                           message=result.message)


@router.post(
    "/integrations/heywhale/tags/update",
    response_model=SuccessResponse | ErrorResponse,
    summary="更新管理后台的用户标签",
    description="更新管理后台的用户标签",
)
@invalidate_cache(domain="users", operation="tags")  # Invalidate tags cache after update
async def update_user_tags_heywhale(
    tag_data: PostTagRequest = Body(...),
    current_user_id: str = Depends(get_current_user_id),
    database = Depends(get_mongo_kesci_db),
):
    """Update user tags in HeyWhale platform.
    
    更新用户标签到HeyWhale平台
    """
    # Check HeyWhale access permission manually
    await require_heywhale_access_permission(current_user_id)
    
    # Call service directly - exceptions bubble up to FastAPI handlers
    result:BaseDataPostResponse = await user_services.update_heywhale_tags(
        tag_data=tag_data.users,
        database=database
    )
    return SuccessResponse(data=result.data,
                           message=result.message)

# ——— Cache Management endpoints (for demonstration) ———

@router.get(
    "/cache/stats",
    response_model=SuccessResponse | ErrorResponse,
    summary="获取缓存统计信息",
    description="获取用户域的缓存统计信息",
)
async def get_cache_stats(
    current_user_id: str = Depends(get_current_user_id),
    cache_manager = Depends(get_cache_manager),
):
    """Get cache statistics for users domain."""
    # Check permission manually (could add admin permission check)
    await require_user_read_permission(current_user_id)
    
    # Get cache statistics
    stats = await cache_manager.get_cache_stats("users")
    
    return SuccessResponse(data=stats)


@router.delete(
    "/cache/clear",
    response_model=SuccessResponse | ErrorResponse,
    summary="Clear cache",
    description="Clear cache for users domain",
)
async def clear_cache(
    operation: Optional[str] = Query(None, description="Specific operation to clear (optional)"),
    current_user_id: str = Depends(get_current_user_id),
    cache_manager = Depends(get_cache_manager),
):
    """Clear cache for users domain."""
    # Check permission manually (could add admin permission check)
    await require_user_read_permission(current_user_id)
    
    if operation:
        # Clear specific operation
        from core.cache.utils import generate_pattern_key
        from core.cache.client import get_cache_client
        
        cache_client = await get_cache_client()
        pattern = generate_pattern_key("users", operation)
        deleted_count = await cache_client.clear_pattern(pattern)
        
        result = {
            "status": "completed",
            "domain": "users",
            "operation": operation,
            "deleted_keys": deleted_count,
        }
    else:
        # Clear entire domain
        result = await cache_manager.clear_domain("users")
    
    return SuccessResponse(data=result)


@router.get(
    "/cache/health",
    response_model=SuccessResponse | ErrorResponse,
    summary="Check cache health",
    description="Check cache system health",
)
async def check_cache_health(
    current_user_id: str = Depends(get_current_user_id),
    cache_manager = Depends(get_cache_manager),
):
    """Check cache system health."""
    # Check permission manually
    await require_user_read_permission(current_user_id)
    
    # Get cache health
    health = await cache_manager.health_check()
    
    return SuccessResponse(data=health)


