"""
Competition domain-specific models and data structures.

Contains models and data classes specific to the competitions domain
that are not shared across the application.
"""

from typing import Optional
from dataclasses import dataclass
from enum import Enum


class CompetitionStatus(str, Enum):
    """Competition status enumeration."""

    DRAFT = "draft"
    ACTIVE = "active"
    UPCOMING = "upcoming"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class QualificationType(str, Enum):
    """Qualification type enumeration."""

    SCORE_BASED = "score_based"
    SUBMISSION_BASED = "submission_based"
    TIME_BASED = "time_based"


@dataclass
class CompetitionContext:
    """Context data for competition operations."""

    user_id: str
    competition_id: Optional[str] = None
    route_id: Optional[str] = None
    admin_privileges: bool = False


@dataclass
class QualificationContext:
    """Context data for qualification operations."""

    user_id: str
    competition_id: str
    qualification_id: Optional[str] = None
    score_threshold: Optional[float] = None


@dataclass
class CreditTransaction:
    """Credit transaction data structure."""

    user_id: str
    competition_id: str
    qualification_id: str
    credit_amount: int
    reason: str
    admin_id: Optional[str] = None
    related_object_id: Optional[str] = None
    related_object_type: Optional[str] = None


# TODO: Add other domain-specific models as needed
