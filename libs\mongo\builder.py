from typing import Dict, Any, Union, List, Optional
from pydantic import BaseModel
from .schemas import (
    MatchStageSpec, 
    FieldCriteria, 
    MatchType, 
    RegexOptions, 
    NestedArrayOptions, 
    RangeOptions, 
    TextSearchOptions,
    SimpleExactMatch,
    ArraySearchSpec,
    TextSearchSpec,
    LogicOperator,
)

# Enhanced builder with Pydantic validation
class MongoQueryBuilder(BaseModel):
    """
    Builds MongoDB aggregation pipeline $match stages with Pydantic validation.
    Supports deeply nested arrays and objects with arbitrary depth.
    """
    
    def __init__(self, spec: Union[MatchStageSpec, Dict[str, Any]]):
        """
        Initialize with a validated match stage specification.
        
        Args:
            spec: MatchStageSpec or dictionary that can be converted to one
        """
        if isinstance(spec, dict):
            self.spec = MatchStageSpec(**spec)
        else:
            self.spec = spec
    
    def build_match_stage(self) -> Dict[str, Any]:
        """
        Build the complete $match stage for MongoDB aggregation pipeline.
        
        Returns:
            Dictionary representing the $match stage
        """
        match_conditions = {}
        
        for field_path, criteria in self.spec.criteria.items():
            condition = self._build_field_condition(field_path, criteria)
            self._merge_condition(match_conditions, condition)
        
        return match_conditions
    
    def _build_field_condition(self, field_path: str, criteria: FieldCriteria) -> Dict[str, Any]:
        """Build condition for a single field based on criteria."""
        
        if criteria.match_type == MatchType.EXACT:
            return self._build_exact_match(field_path, criteria.values)
        
        elif criteria.match_type == MatchType.REGEX:
            return self._build_regex_match(field_path, criteria.values, criteria.options)
        
        elif criteria.match_type == MatchType.ARRAY_CONTAINS_ANY:
            return self._build_array_contains_any(field_path, criteria.values)
        
        elif criteria.match_type == MatchType.ARRAY_CONTAINS_ALL:
            return self._build_array_contains_all(field_path, criteria.values)
        
        elif criteria.match_type == MatchType.NESTED_ARRAY_ELEMENT_MATCH:
            return self._build_nested_array_element_match(field_path, criteria.values, criteria.options)
        
        elif criteria.match_type == MatchType.EXISTS:
            exists_value = criteria.options.exists if criteria.options else True
            return self._build_exists_match(field_path, exists_value)
        
        elif criteria.match_type == MatchType.RANGE:
            return self._build_range_match(field_path, criteria.options)
        
        elif criteria.match_type == MatchType.TEXT_SEARCH:
            return self._build_text_search(field_path, criteria.values, criteria.options)
        
        else:
            raise ValueError(f"Unsupported match type: {criteria.match_type}")
    
    def _build_exact_match(self, field_path: str, values: List[Any]) -> Dict[str, Any]:
        """Build exact match condition."""
        if len(values) == 1:
            return {field_path: values[0]}
        else:
            return {field_path: {"$in": values}}
    
    def _build_regex_match(self, field_path: str, values: List[str], options: Optional[RegexOptions]) -> Dict[str, Any]:
        """Build regex match condition."""
        opts = options or RegexOptions()
        
        regex_options = ""
        if not opts.case_sensitive:
            regex_options += "i"
        if opts.multiline:
            regex_options += "m"
        if opts.dotall:
            regex_options += "s"
        
        if len(values) == 1:
            return {field_path: {"$regex": values[0], "$options": regex_options}}
        else:
            or_conditions = []
            for pattern in values:
                or_conditions.append({field_path: {"$regex": pattern, "$options": regex_options}})
            return {"$or": or_conditions}
    
    def _build_array_contains_any(self, field_path: str, values: List[Any]) -> Dict[str, Any]:
        """Build array contains any condition."""
        return {field_path: {"$in": values}}
    
    def _build_array_contains_all(self, field_path: str, values: List[Any]) -> Dict[str, Any]:
        """Build array contains all condition."""
        return {field_path: {"$all": values}}
    
    def _build_nested_array_element_match(self, field_path: str, values: List[Dict[str, Any]], 
                                        options: Optional[NestedArrayOptions]) -> Dict[str, Any]:
        """Build nested array element match using $elemMatch."""
        opts = options or NestedArrayOptions()
        
        if len(values) == 1:
            return {field_path: {"$elemMatch": values[0]}}
        else:
            if opts.logic == LogicOperator.OR:
                return {field_path: {"$elemMatch": {"$or": values}}}
            else:
                return {field_path: {"$elemMatch": {"$and": values}}}
    
    def _build_exists_match(self, field_path: str, exists: bool) -> Dict[str, Any]:
        """Build field exists condition."""
        return {field_path: {"$exists": exists}}
    
    def _build_range_match(self, field_path: str, options: RangeOptions) -> Dict[str, Any]:
        """Build range match condition."""
        range_condition = {}
        
        if options.min_value is not None:
            range_condition["$gte"] = options.min_value
        if options.max_value is not None:
            range_condition["$lte"] = options.max_value
        if options.greater_than is not None:
            range_condition["$gt"] = options.greater_than
        if options.less_than is not None:
            range_condition["$lt"] = options.less_than
        
        return {field_path: range_condition}
    
    def _build_text_search(self, field_path: str, values: List[str], options: Optional[TextSearchOptions]) -> Dict[str, Any]:
        """Build text search condition."""
        opts = options or TextSearchOptions()
        
        regex_options = "" if opts.case_sensitive else "i"
        
        if opts.logic == LogicOperator.OR:
            or_conditions = []
            for term in values:
                pattern = f"\\b{term}\\b" if opts.whole_word else term
                or_conditions.append({field_path: {"$regex": pattern, "$options": regex_options}})
            return {"$or": or_conditions}
        else:
            and_conditions = []
            for term in values:
                pattern = f"\\b{term}\\b" if opts.whole_word else term
                and_conditions.append({field_path: {"$regex": pattern, "$options": regex_options}})
            return {"$and": and_conditions}
    
    def _merge_condition(self, match_conditions: Dict[str, Any], new_condition: Dict[str, Any]):
        """Merge a new condition into existing match conditions."""
        for key, value in new_condition.items():
            if key in match_conditions:
                if "$and" not in match_conditions:
                    existing = match_conditions.copy()
                    match_conditions.clear()
                    match_conditions["$and"] = [existing]
                match_conditions["$and"].append({key: value})
            else:
                match_conditions[key] = value
    
    @classmethod
    def from_simple_exact_match(cls, simple_spec: Union[SimpleExactMatch, Dict[str, List[Any]]]) -> 'MongoQueryBuilder':
        """Create builder from simple exact match specification."""
        if isinstance(simple_spec, dict):
            simple_spec = SimpleExactMatch(field_values=simple_spec)
        return cls(simple_spec.to_match_stage_spec())
    
    @classmethod
    def from_array_search(cls, array_spec: Union[ArraySearchSpec, Dict[str, Any]]) -> 'MongoQueryBuilder':
        """Create builder from array search specification."""
        if isinstance(array_spec, dict):
            array_spec = ArraySearchSpec(**array_spec)
        return cls(array_spec.to_match_stage_spec())
    
    @classmethod
    def from_text_search(cls, text_spec: Union[TextSearchSpec, Dict[str, Any]]) -> 'MongoQueryBuilder':
        """Create builder from text search specification."""
        if isinstance(text_spec, dict):
            text_spec = TextSearchSpec(**text_spec)
        return cls(text_spec.to_match_stage_spec())
