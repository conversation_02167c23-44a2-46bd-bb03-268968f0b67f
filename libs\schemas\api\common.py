from typing import Optional, List, Any, Dict
from pydantic import BaseModel, Field

# For GET /tags
# No request body, query param 'category'


# For POST /tags/update
class UserTagUpdateRequest(BaseModel):
    tag_id: str
    users: List[str] = Field(default_factory=list)


# For POST /tags/shence
class ShenceTagsCreateRequest(BaseModel):
    tag_id: Optional[str] = None
    tag_name: Optional[str] = None
    data: List[str] = Field(default_factory=list)  # User IDs
    dir_id: Optional[int] = 0


# For POST /user_id
class UserIdQueryRequest(BaseModel):
    ids: List[str] = Field(default_factory=list)


# For POST /tags/<tag_id>/query
class TagUserQueryRequest(BaseModel):
    fields: List[str] = Field(default_factory=list)


# Generic response for many of these, can be refined
class CommonResponse(BaseModel):
    message: str = "success"
    data: Any


class EventTracksResponse(BaseModel):
    """Response model for /event_tracks endpoint."""

    message: str = "success"
    data: List[
        Dict[str, Any]
    ]  # Or a more specific schema if the structure of filtered_event_tracks is known
