"""
Common API schemas and response utilities.

This module provides standardized schemas and response utilities
for consistent API responses across all domains.
"""

from typing import Optional, List, Any, Dict, Generic, TypeVar
from pydantic import BaseModel, Field
from datetime import datetime

# Generic type for data payload
T = TypeVar('T')


# For GET /tags
# No request body, query param 'category'


# For POST /tags/update
class UserTagUpdateRequest(BaseModel):
    tag_id: str
    users: List[str] = Field(default_factory=list)


# For POST /tags/shence
class ShenceTagsCreateRequest(BaseModel):
    tag_id: Optional[str] = None
    tag_name: Optional[str] = None
    data: List[str] = Field(default_factory=list)  # User IDs
    dir_id: Optional[int] = 0


# For POST /user_id
class UserIdQueryRequest(BaseModel):
    ids: List[str] = Field(default_factory=list)


# For POST /tags/<tag_id>/query
class TagUserQueryRequest(BaseModel):
    fields: List[str] = Field(default_factory=list)


class PaginationMeta(BaseModel):
    """Standardized pagination metadata."""
    
    total: int = Field(..., description="Total number of records")
    limit: int = Field(..., description="Number of records per page")
    offset: int = Field(..., description="Number of records skipped")
    page: int = Field(..., description="Current page number (calculated from offset/limit)")
    total_pages: int = Field(..., description="Total number of pages")
    has_next: bool = Field(..., description="Whether there are more pages")
    has_prev: bool = Field(..., description="Whether there are previous pages")
    
    @classmethod
    def create(cls, total: int, limit: int, offset: int) -> "PaginationMeta":
        """Create pagination metadata from basic parameters."""
        page = (offset // limit) + 1 if limit > 0 else 1
        total_pages = (total + limit - 1) // limit if limit > 0 else 1
        
        return cls(
            total=total,
            limit=limit,
            offset=offset,
            page=page,
            total_pages=total_pages,
            has_next=offset + limit < total,
            has_prev=offset > 0
        )


class StandardResponse(BaseModel, Generic[T]):
    """Standardized API response wrapper."""
    
    success: bool = Field(True, description="Operation success status")
    message: str = Field("success", description="Response message")
    data: Optional[T] = Field(None, description="Response data payload")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")


class PaginatedResponse(BaseModel, Generic[T]):
    """Standardized paginated response."""
    
    success: bool = Field(True, description="Operation success status")
    message: str = Field("success", description="Response message")
    data: List[T] = Field(default_factory=list, description="Response data items")
    pagination: PaginationMeta = Field(..., description="Pagination metadata")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")


class ErrorResponse(BaseModel):
    """Standardized error response."""
    
    success: bool = Field(False, description="Operation success status")
    message: str = Field(..., description="Error message")
    error_code: Optional[str] = Field(None, description="Specific error code")
    details: Optional[dict] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")


# Legacy support - keeping existing schemas for backward compatibility
class CommonResponse(BaseModel):
    message: str = "success"
    data: Any


class EventTracksResponse(BaseModel):
    """Event tracks response schema."""
    
    event_id: str = Field(..., description="Event identifier")
    track_name: str = Field(..., description="Track name")
    track_info: Optional[str] = Field(None, description="Track information")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")


# Response utility functions
def success_response(data: Any = None, message: str = "success") -> StandardResponse:
    """Create a successful response."""
    return StandardResponse(success=True, message=message, data=data)


def error_response(message: str, error_code: str = None, details: dict = None) -> ErrorResponse:
    """Create an error response."""
    return ErrorResponse(
        success=False,
        message=message,
        error_code=error_code,
        details=details
    )


def paginated_response(
    data: List[Any],
    total: int,
    limit: int,
    offset: int,
    message: str = "success"
) -> PaginatedResponse:
    """Create a paginated response with metadata."""
    pagination = PaginationMeta.create(total=total, limit=limit, offset=offset)
    return PaginatedResponse(
        success=True,
        message=message,
        data=data,
        pagination=pagination
    )
