"""
Competition Admin - CRUD operations for competition management.

Handles qualification creation/updates and credit awarding with audit logging and transaction support.
"""

from typing import Dict, Any, List
import logging
from datetime import datetime
from bson import ObjectId

from gateway.admin.base import BaseAdmin
from libs.auth.context import AuthContext, require_auth
from libs.errors import AdminResult, AdminError

logger = logging.getLogger(__name__)


class CompetitionAdmin(BaseAdmin):
    """Admin service for competition management operations."""

    def __init__(self):
        """Initialize competition admin service."""
        super().__init__()

    @require_auth
    async def create_qualification(
        self, auth_context: AuthContext, qualification_data: Dict[str, Any]
    ) -> AdminResult[Dict[str, Any]]:
        """
        Create a new qualification standard with audit logging.

        Args:
            auth_context: Authentication context
            qualification_data: Qualification data including competition_id, name, credit, etc.

        Returns:
            AdminResult containing the created qualification data
        """
        try:
            # Validate required fields
            required_fields = ["competition_id", "qualification_name", "credit"]
            missing_fields = [
                field
                for field in required_fields
                if field not in qualification_data or qualification_data[field] is None
            ]

            if missing_fields:
                return AdminError.validation_failed(
                    "required_fields",
                    f"Missing required fields: {', '.join(missing_fields)}",
                )

            # Validate credit amount
            credit = qualification_data["credit"]
            if not isinstance(credit, int) or credit <= 0:
                return AdminError.validation_failed(
                    "credit", "Credit must be a positive integer"
                )

            # Validate score threshold if provided
            if (
                "score_threshold" in qualification_data
                and qualification_data["score_threshold"] is not None
            ):
                score_threshold = qualification_data["score_threshold"]
                if not isinstance(score_threshold, (int, float)) or score_threshold < 0:
                    return AdminError.validation_failed(
                        "score_threshold",
                        "Score threshold must be a non-negative number",
                    )

            async with self.transaction_context() as session:
                # Check if competition exists
                competition = await self.db["competitions"].find_one(
                    {"competition_id": qualification_data["competition_id"]},
                    session=session,
                )

                if not competition:
                    return AdminError.validation_failed(
                        "competition_id", "Competition not found"
                    )

                # Generate qualification ID
                qualification_id = str(ObjectId())

                # Prepare qualification document
                qualification_document = {
                    "qualification_id": qualification_id,
                    "qualification_name": qualification_data["qualification_name"],
                    "competition_id": qualification_data["competition_id"],
                    "competition_name": qualification_data.get(
                        "competition_name", competition.get("competition_name")
                    ),
                    "route_name": qualification_data.get(
                        "route_name", competition.get("route_name")
                    ),
                    "credit": qualification_data["credit"],
                    "qualification_type": qualification_data.get(
                        "qualification_type", 1
                    ),
                    "qualification_logic": qualification_data.get(
                        "qualification_logic", 1
                    ),
                    "related_task_id": qualification_data.get("related_task_id"),
                    "score_threshold": qualification_data.get("score_threshold"),
                    "deleted": False,
                    "created_at": datetime.now(),
                    "created_by": auth_context.user_id,
                    "updated_at": datetime.now(),
                    "updated_by": auth_context.user_id,
                }

                # Insert qualification
                await self.db["qualifications"].insert_one(
                    qualification_document, session=session
                )

                # Log audit
                await self._log_audit(
                    auth_context,
                    "CREATE",
                    "qualifications",
                    qualification_id,
                    {"qualification_data": qualification_data},
                    session,
                )

                logger.info(
                    f"Created qualification {qualification_id} by {auth_context.user_id}"
                )
                return AdminResult.success(qualification_document)

        except Exception as e:
            logger.error(f"Failed to create qualification: {e}")
            return AdminError.transaction_failed("create qualification")

    @require_auth
    async def update_qualification(
        self, auth_context: AuthContext, qualification_id: str, updates: Dict[str, Any]
    ) -> AdminResult[Dict[str, Any]]:
        """
        Update an existing qualification with audit logging.

        Args:
            auth_context: Authentication context
            qualification_id: ID of the qualification to update
            updates: Fields to update

        Returns:
            AdminResult containing the updated qualification data
        """
        try:
            if not qualification_id or not qualification_id.strip():
                return AdminError.validation_failed(
                    "qualification_id", "Qualification ID is required"
                )

            if not updates:
                return AdminError.validation_failed("updates", "No updates provided")

            # Validate credit if provided
            if "credit" in updates:
                credit = updates["credit"]
                if not isinstance(credit, int) or credit <= 0:
                    return AdminError.validation_failed(
                        "credit", "Credit must be a positive integer"
                    )

            # Validate score threshold if provided
            if "score_threshold" in updates and updates["score_threshold"] is not None:
                score_threshold = updates["score_threshold"]
                if not isinstance(score_threshold, (int, float)) or score_threshold < 0:
                    return AdminError.validation_failed(
                        "score_threshold",
                        "Score threshold must be a non-negative number",
                    )

            async with self.transaction_context() as session:
                # Check if qualification exists
                existing_qualification = await self.db["qualifications"].find_one(
                    {"qualification_id": qualification_id, "deleted": {"$ne": True}},
                    session=session,
                )

                if not existing_qualification:
                    return AdminError.validation_failed(
                        "qualification_id", "Qualification not found"
                    )

                # Prepare update data
                update_data = {}
                allowed_fields = [
                    "qualification_name",
                    "credit",
                    "qualification_type",
                    "qualification_logic",
                    "related_task_id",
                    "score_threshold",
                    "competition_name",
                    "route_name",
                ]

                for field, value in updates.items():
                    if field in allowed_fields:
                        update_data[field] = value

                if not update_data:
                    return AdminError.validation_failed(
                        "updates", "No valid fields to update"
                    )

                # Add metadata
                update_data["updated_at"] = datetime.now()
                update_data["updated_by"] = auth_context.user_id

                # Update qualification
                result = await self.db["qualifications"].update_one(
                    {"qualification_id": qualification_id},
                    {"$set": update_data},
                    session=session,
                )

                if result.modified_count == 0:
                    return AdminError.transaction_failed(
                        "update qualification - no changes made"
                    )

                # Get updated qualification
                updated_qualification = await self.db["qualifications"].find_one(
                    {"qualification_id": qualification_id}, session=session
                )

                # Log audit
                await self._log_audit(
                    auth_context,
                    "UPDATE",
                    "qualifications",
                    qualification_id,
                    {"updates": updates, "applied_updates": update_data},
                    session,
                )

                logger.info(
                    f"Updated qualification {qualification_id} by {auth_context.user_id}"
                )
                return AdminResult.success(updated_qualification)

        except Exception as e:
            logger.error(f"Failed to update qualification {qualification_id}: {e}")
            return AdminError.transaction_failed("update qualification")

    @require_auth
    async def award_credits(
        self, auth_context: AuthContext, credit_data: Dict[str, Any]
    ) -> AdminResult[str]:
        """
        Award credits to a user for qualifying in a competition.

        Args:
            auth_context: Authentication context
            credit_data: Credit award data including user_id, competition_id, qualification_id, credit, reason

        Returns:
            AdminResult containing the credit record ID
        """
        try:
            # Validate required fields
            required_fields = [
                "user_id",
                "competition_id",
                "qualification_id",
                "credit",
                "reason",
            ]
            missing_fields = [
                field
                for field in required_fields
                if field not in credit_data or not credit_data[field]
            ]

            if missing_fields:
                return AdminError.validation_failed(
                    "required_fields",
                    f"Missing required fields: {', '.join(missing_fields)}",
                )

            # Validate credit amount
            credit = credit_data["credit"]
            if not isinstance(credit, int) or credit == 0:
                return AdminError.validation_failed(
                    "credit", "Credit must be a non-zero integer"
                )

            async with self.transaction_context() as session:
                # Verify user exists
                user = await self.db["users"].find_one(
                    {"user_id": credit_data["user_id"]}, session=session
                )

                if not user:
                    return AdminError.validation_failed("user_id", "User not found")

                # Verify competition exists
                competition = await self.db["competitions"].find_one(
                    {"competition_id": credit_data["competition_id"]}, session=session
                )

                if not competition:
                    return AdminError.validation_failed(
                        "competition_id", "Competition not found"
                    )

                # Verify qualification exists
                qualification = await self.db["qualifications"].find_one(
                    {
                        "qualification_id": credit_data["qualification_id"],
                        "deleted": {"$ne": True},
                    },
                    session=session,
                )

                if not qualification:
                    return AdminError.validation_failed(
                        "qualification_id", "Qualification not found"
                    )

                # Generate credit record ID
                credit_record_id = str(ObjectId())

                # Prepare credit record
                credit_record = {
                    "credit_id": credit_record_id,
                    "user_id": credit_data["user_id"],
                    "competition_id": credit_data["competition_id"],
                    "qualification_id": credit_data["qualification_id"],
                    "credit": credit_data["credit"],
                    "reason": credit_data["reason"],
                    "related_object_id": credit_data.get("related_object_id"),
                    "related_object_type": credit_data.get("related_object_type"),
                    "batch_id": credit_data.get("batch_id"),
                    "admin_id": auth_context.user_id,
                    "created_at": datetime.now(),
                    "revoked_at": None,
                }

                # Insert credit record
                await self.db["credit_logs"].insert_one(credit_record, session=session)

                # Update user's total credits (if tracking)
                await self.db["users"].update_one(
                    {"user_id": credit_data["user_id"]},
                    {
                        "$inc": {"total_credits": credit_data["credit"]},
                        "$set": {"updated_at": datetime.now()},
                    },
                    session=session,
                )

                # Log audit
                await self._log_audit(
                    auth_context,
                    "AWARD_CREDITS",
                    "credit_logs",
                    credit_record_id,
                    {
                        "user_id": credit_data["user_id"],
                        "credit_amount": credit_data["credit"],
                        "qualification_id": credit_data["qualification_id"],
                    },
                    session,
                )

                logger.info(
                    f"Awarded {credit_data['credit']} credits to user {credit_data['user_id']} by {auth_context.user_id}"
                )
                return AdminResult.success(credit_record_id)

        except Exception as e:
            logger.error(f"Failed to award credits: {e}")
            return AdminError.transaction_failed("award credits")

    @require_auth
    async def revoke_credits(
        self, auth_context: AuthContext, credit_record_id: str, reason: str
    ) -> AdminResult[bool]:
        """
        Revoke previously awarded credits.

        Args:
            auth_context: Authentication context
            credit_record_id: ID of the credit record to revoke
            reason: Reason for revocation

        Returns:
            AdminResult indicating success or failure
        """
        try:
            if not credit_record_id or not credit_record_id.strip():
                return AdminError.validation_failed(
                    "credit_record_id", "Credit record ID is required"
                )

            if not reason or not reason.strip():
                return AdminError.validation_failed(
                    "reason", "Revocation reason is required"
                )

            async with self.transaction_context() as session:
                # Find the credit record
                credit_record = await self.db["credit_logs"].find_one(
                    {"credit_id": credit_record_id, "revoked_at": None}, session=session
                )

                if not credit_record:
                    return AdminError.validation_failed(
                        "credit_record_id", "Credit record not found or already revoked"
                    )

                # Mark as revoked
                result = await self.db["credit_logs"].update_one(
                    {"credit_id": credit_record_id},
                    {
                        "$set": {
                            "revoked_at": datetime.now(),
                            "revoked_by": auth_context.user_id,
                            "revocation_reason": reason,
                        }
                    },
                    session=session,
                )

                if result.modified_count == 0:
                    return AdminError.transaction_failed("revoke credits")

                # Update user's total credits (subtract the revoked amount)
                await self.db["users"].update_one(
                    {"user_id": credit_record["user_id"]},
                    {
                        "$inc": {"total_credits": -credit_record["credit"]},
                        "$set": {"updated_at": datetime.now()},
                    },
                    session=session,
                )

                # Log audit
                await self._log_audit(
                    auth_context,
                    "REVOKE_CREDITS",
                    "credit_logs",
                    credit_record_id,
                    {
                        "user_id": credit_record["user_id"],
                        "credit_amount": credit_record["credit"],
                        "reason": reason,
                    },
                    session,
                )

                logger.info(
                    f"Revoked {credit_record['credit']} credits from user {credit_record['user_id']} by {auth_context.user_id}"
                )
                return AdminResult.success(True)

        except Exception as e:
            logger.error(f"Failed to revoke credits {credit_record_id}: {e}")
            return AdminError.transaction_failed("revoke credits")

    @require_auth
    async def delete_qualification(
        self, auth_context: AuthContext, qualification_id: str, soft_delete: bool = True
    ) -> AdminResult[bool]:
        """
        Delete a qualification (soft delete by default).

        Args:
            auth_context: Authentication context
            qualification_id: ID of the qualification to delete
            soft_delete: Whether to soft delete or hard delete

        Returns:
            AdminResult indicating success or failure
        """
        try:
            if not qualification_id or not qualification_id.strip():
                return AdminError.validation_failed(
                    "qualification_id", "Qualification ID is required"
                )

            async with self.transaction_context() as session:
                # Check if qualification exists
                existing_qualification = await self.db["qualifications"].find_one(
                    {"qualification_id": qualification_id}, session=session
                )

                if not existing_qualification:
                    return AdminError.validation_failed(
                        "qualification_id", "Qualification not found"
                    )

                if soft_delete:
                    # Soft delete - mark as deleted
                    result = await self.db["qualifications"].update_one(
                        {"qualification_id": qualification_id},
                        {
                            "$set": {
                                "deleted": True,
                                "deleted_at": datetime.now(),
                                "deleted_by": auth_context.user_id,
                                "updated_at": datetime.now(),
                                "updated_by": auth_context.user_id,
                            }
                        },
                        session=session,
                    )

                    operation = "SOFT_DELETE"
                    success = result.modified_count > 0
                else:
                    # Hard delete - remove from database
                    result = await self.db["qualifications"].delete_one(
                        {"qualification_id": qualification_id}, session=session
                    )

                    operation = "DELETE"
                    success = result.deleted_count > 0

                if not success:
                    return AdminError.transaction_failed(
                        f"{operation.lower()} qualification"
                    )

                # Log audit
                await self._log_audit(
                    auth_context,
                    operation,
                    "qualifications",
                    qualification_id,
                    {"soft_delete": soft_delete},
                    session,
                )

                logger.info(
                    f"{operation} qualification {qualification_id} by {auth_context.user_id}"
                )
                return AdminResult.success(True)

        except Exception as e:
            logger.error(f"Failed to delete qualification {qualification_id}: {e}")
            return AdminError.transaction_failed("delete qualification")

    @require_auth
    async def bulk_award_credits(
        self, auth_context: AuthContext, credit_awards: List[Dict[str, Any]]
    ) -> AdminResult[Dict[str, Any]]:
        """
        Award credits to multiple users in a single transaction.

        Args:
            auth_context: Authentication context
            credit_awards: List of credit awards, each containing user_id, competition_id, etc.

        Returns:
            AdminResult containing summary of bulk award operation
        """
        try:
            if not credit_awards:
                return AdminError.validation_failed(
                    "credit_awards", "No credit awards provided"
                )

            # Validate each award entry
            for i, award_entry in enumerate(credit_awards):
                required_fields = [
                    "user_id",
                    "competition_id",
                    "qualification_id",
                    "credit",
                    "reason",
                ]
                missing_fields = [
                    field
                    for field in required_fields
                    if field not in award_entry or not award_entry[field]
                ]

                if missing_fields:
                    return AdminError.validation_failed(
                        "credit_awards",
                        f"Entry {i} missing required fields: {', '.join(missing_fields)}",
                    )

            async with self.transaction_context() as session:
                awarded_count = 0
                failed_awards = []
                batch_id = str(ObjectId())  # Common batch ID for all awards

                for award_entry in credit_awards:
                    try:
                        # Add batch_id to the award entry
                        award_entry["batch_id"] = batch_id

                        # Use the single award method (without transaction context since we're already in one)
                        # Validate user exists
                        user = await self.db["users"].find_one(
                            {"user_id": award_entry["user_id"]}, session=session
                        )

                        if not user:
                            failed_awards.append(
                                {
                                    "user_id": award_entry["user_id"],
                                    "reason": "User not found",
                                }
                            )
                            continue

                        # Validate qualification exists
                        qualification = await self.db["qualifications"].find_one(
                            {
                                "qualification_id": award_entry["qualification_id"],
                                "deleted": {"$ne": True},
                            },
                            session=session,
                        )

                        if not qualification:
                            failed_awards.append(
                                {
                                    "user_id": award_entry["user_id"],
                                    "reason": "Qualification not found",
                                }
                            )
                            continue

                        # Generate credit record ID
                        credit_record_id = str(ObjectId())

                        # Prepare credit record
                        credit_record = {
                            "credit_id": credit_record_id,
                            "user_id": award_entry["user_id"],
                            "competition_id": award_entry["competition_id"],
                            "qualification_id": award_entry["qualification_id"],
                            "credit": award_entry["credit"],
                            "reason": award_entry["reason"],
                            "related_object_id": award_entry.get("related_object_id"),
                            "related_object_type": award_entry.get(
                                "related_object_type"
                            ),
                            "batch_id": batch_id,
                            "admin_id": auth_context.user_id,
                            "created_at": datetime.now(),
                            "revoked_at": None,
                        }

                        # Insert credit record
                        await self.db["credit_logs"].insert_one(
                            credit_record, session=session
                        )

                        # Update user's total credits
                        await self.db["users"].update_one(
                            {"user_id": award_entry["user_id"]},
                            {
                                "$inc": {"total_credits": award_entry["credit"]},
                                "$set": {"updated_at": datetime.now()},
                            },
                            session=session,
                        )

                        awarded_count += 1

                        # Log audit for each successful award
                        await self._log_audit(
                            auth_context,
                            "BULK_AWARD_CREDITS",
                            "credit_logs",
                            credit_record_id,
                            {
                                "user_id": award_entry["user_id"],
                                "credit_amount": award_entry["credit"],
                                "batch_id": batch_id,
                            },
                            session,
                        )

                    except Exception as e:
                        failed_awards.append(
                            {
                                "user_id": award_entry.get("user_id", "unknown"),
                                "reason": str(e),
                            }
                        )

                # Log bulk operation audit
                await self._log_audit(
                    auth_context,
                    "BULK_AWARD_OPERATION",
                    "credit_logs",
                    "bulk",
                    {
                        "batch_id": batch_id,
                        "total_requested": len(credit_awards),
                        "successful_awards": awarded_count,
                        "failed_awards": len(failed_awards),
                    },
                    session,
                )

                result_data = {
                    "batch_id": batch_id,
                    "total_requested": len(credit_awards),
                    "successful_awards": awarded_count,
                    "failed_awards": failed_awards,
                }

                logger.info(
                    f"Bulk awarded credits to {awarded_count}/{len(credit_awards)} users by {auth_context.user_id}"
                )
                return AdminResult.success(result_data)

        except Exception as e:
            logger.error(f"Failed to bulk award credits: {e}")
            return AdminError.transaction_failed("bulk award credits")
