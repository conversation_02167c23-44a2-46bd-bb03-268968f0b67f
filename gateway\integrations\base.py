"""
Base classes for the Integration layer.

Provides HTTP client, retry logic, and graceful error handling for external services.
"""

import asyncio
import json
import aiohttp
from typing import Any, Dict, Optional, TypeVar
from datetime import datetime
from dataclasses import dataclass
import logging

from libs.auth.context import AuthContext
from libs.errors import IntegrationErrors
from libs.exceptions import ExternalServiceError

logger = logging.getLogger(__name__)

T = TypeVar("T")


@dataclass
class HTTPResponse:
    """Wrapper for HTTP response data."""

    status_code: int
    data: Optional[Dict[str, Any]]
    headers: Dict[str, str]
    success: bool
    error_message: Optional[str] = None

    @classmethod
    def from_aiohttp_response(
        cls, response: aiohttp.ClientResponse, data: Optional[Dict[str, Any]] = None
    ) -> "HTTPResponse":
        """Create HTTPResponse from aiohttp response."""
        return cls(
            status_code=response.status,
            data=data,
            headers=dict(response.headers),
            success=200 <= response.status < 300,
            error_message=None
            if 200 <= response.status < 300
            else f"HTTP {response.status}",
        )


@dataclass
class RetryConfig:
    """Configuration for retry logic."""

    max_attempts: int = 3
    base_delay: float = 1.0  # seconds
    max_delay: float = 60.0  # seconds
    exponential_base: float = 2.0
    jitter: bool = True

    def get_delay(self, attempt: int) -> float:
        """Calculate delay for given attempt number."""
        delay = min(self.base_delay * (self.exponential_base**attempt), self.max_delay)

        if self.jitter:
            # Add random jitter (±25%)
            import random

            jitter_factor = 0.75 + (random.random() * 0.5)  # 0.75 to 1.25
            delay *= jitter_factor

        return delay


class BaseIntegration:
    """
    Base class for all Integration layer services.

    Provides HTTP client, retry logic, and graceful error handling.
    """

    def __init__(self, base_url: Optional[str] = None, timeout: int = 30):
        """Initialize base integration with HTTP client configuration."""
        self.base_url = base_url
        self.timeout = aiohttp.ClientTimeout(total=timeout)
        self.service_name = self.__class__.__name__.lower().replace("integration", "")
        self._session: Optional[aiohttp.ClientSession] = None

        logger.debug(f"Initialized {self.__class__.__name__}")

    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session."""
        if self._session is None or self._session.closed:
            self._session = aiohttp.ClientSession(
                timeout=self.timeout,
                headers={"User-Agent": f"community-services/{self.service_name}"},
            )
        return self._session

    async def close(self):
        """Close HTTP session."""
        if self._session and not self._session.closed:
            await self._session.close()
            logger.debug(f"Closed HTTP session for {self.service_name}")

    async def __aenter__(self):
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()

    async def _make_request(
        self,
        method: str,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        retry_config: Optional[RetryConfig] = None,
    ) -> HTTPResponse:
        """
        Make HTTP request with retry logic.

        Args:
            method: HTTP method (GET, POST, etc.)
            url: Request URL (can be relative to base_url)
            headers: Request headers
            data: Request body data
            params: URL parameters
            retry_config: Retry configuration

        Returns:
            ServiceResult with HTTPResponse
        """
        # Build full URL
        if self.base_url and not url.startswith("http"):
            full_url = f"{self.base_url.rstrip('/')}/{url.lstrip('/')}"
        else:
            full_url = url

        # Default retry config
        if retry_config is None:
            retry_config = RetryConfig()

        session = await self._get_session()
        last_error = None

        for attempt in range(retry_config.max_attempts):
            try:
                logger.debug(
                    f"Making {method} request to {full_url} (attempt {attempt + 1})"
                )

                async with session.request(
                    method=method,
                    url=full_url,
                    headers=headers,
                    json=data if data else None,
                    params=params,
                ) as response:
                    # Read response data
                    try:
                        response_data = await response.json()
                    except (aiohttp.ContentTypeError, ValueError):
                        # Not JSON response
                        response_data = None

                    http_response = HTTPResponse.from_aiohttp_response(
                        response, response_data
                    )

                    # Check if we should retry
                    if not http_response.success and self._should_retry(
                        response.status, attempt, retry_config
                    ):
                        last_error = (
                            f"HTTP {response.status}: {http_response.error_message}"
                        )
                        if attempt < retry_config.max_attempts - 1:
                            delay = retry_config.get_delay(attempt)
                            logger.warning(
                                f"Request failed, retrying in {delay:.2f}s: {last_error}"
                            )
                            await asyncio.sleep(delay)
                            continue

                    # Return response (success or final failure)
                    if http_response.success:
                        logger.debug(f"Request successful: {method} {full_url}")
                        return http_response
                    else:
                        logger.error(
                            f"Request failed: {method} {full_url} - {http_response.error_message}"
                        )
                        return ExternalServiceError.external_service_failed(
                            self.service_name,
                            http_response.error_message or "Unknown error",
                            details={"status_code": response.status, "url": full_url},
                        )

            except asyncio.TimeoutError:
                last_error = f"Request timeout after {self.timeout.total}s"
                logger.warning(f"Request timeout (attempt {attempt + 1}): {full_url}")

                if attempt < retry_config.max_attempts - 1:
                    delay = retry_config.get_delay(attempt)
                    await asyncio.sleep(delay)
                    continue

                return ExternalServiceError.timeout_error(
                    self.service_name, int(self.timeout.total)
                )

            except Exception as e:
                last_error = str(e)
                logger.error(f"Request error (attempt {attempt + 1}): {e}")

                if attempt < retry_config.max_attempts - 1:
                    delay = retry_config.get_delay(attempt)
                    await asyncio.sleep(delay)
                    continue

        # All attempts failed
        return ExternalServiceError.external_service_failed(
            self.service_name,
            last_error or "All retry attempts failed",
            details={"attempts": retry_config.max_attempts, "url": full_url},
        )

    def _should_retry(
        self, status_code: int, attempt: int, retry_config: RetryConfig
    ) -> bool:
        """
        Determine if request should be retried based on status code.

        Args:
            status_code: HTTP status code
            attempt: Current attempt number (0-based)
            retry_config: Retry configuration

        Returns:
            True if should retry, False otherwise
        """
        # Don't retry if we've reached max attempts
        if attempt >= retry_config.max_attempts - 1:
            return False

        # Retry on server errors (5xx) and some client errors
        retry_codes = {
            408,  # Request Timeout
            429,  # Too Many Requests
            500,  # Internal Server Error
            502,  # Bad Gateway
            503,  # Service Unavailable
            504,  # Gateway Timeout
        }

        return status_code in retry_codes

    async def get(
        self,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, Any]] = None,
        retry_config: Optional[RetryConfig] = None,
    ) -> HTTPResponse:
        """Make GET request."""
        return await self._make_request(
            "GET", url, headers=headers, params=params, retry_config=retry_config
        )

    async def post(
        self,
        url: str,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        retry_config: Optional[RetryConfig] = None,
    ) -> HTTPResponse:
        """Make POST request."""
        return await self._make_request(
            "POST", url, headers=headers, data=data, retry_config=retry_config
        )

    async def put(
        self,
        url: str,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        retry_config: Optional[RetryConfig] = None,
    ) -> HTTPResponse:
        """Make PUT request."""
        return await self._make_request(
            "PUT", url, headers=headers, data=data, retry_config=retry_config
        )

    async def delete(
        self,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        retry_config: Optional[RetryConfig] = None,
    ) -> HTTPResponse:
        """Make DELETE request."""
        return await self._make_request(
            "DELETE", url, headers=headers, retry_config=retry_config
        )

    async def health_check(self) -> HTTPResponse:
        """
        Check the health of the integration service.

        Returns:
            ServiceResult with health status information
        """
        try:
            # Test HTTP client
            await self._get_session()

            health_data = {
                "service": self.service_name,
                "status": "healthy",
                "http_client": "ready",
                "base_url": self.base_url,
                "timeout": self.timeout.total,
                "timestamp": datetime.now().isoformat(),
            }

            return HTTPResponse.from_aiohttp_response(
                response=aiohttp.ClientResponse(
                    status=200,
                    headers={"Content-Type": "application/json"},
                    body=json.dumps(health_data).encode(),
                )
            )

        except Exception as e:
            logger.error(f"Health check failed for {self.service_name}: {e}")
            return ExternalServiceError.external_service_failed(
                self.service_name, f"Health check failed: {str(e)}"
            )


# ——— Webhook utilities ———


async def execute_webhook(
    url: str,
    payload: Dict[str, Any],
    auth_context: AuthContext,
    headers: Optional[Dict[str, str]] = None,
    timeout: int = 30,
    retry_config: Optional[RetryConfig] = None,
) -> HTTPResponse:
    """
    Execute webhook with retry logic.

    Args:
        url: Webhook URL
        payload: Webhook payload
        auth_context: Authentication context
        headers: Additional headers
        timeout: Request timeout
        retry_config: Retry configuration

    Returns:
        IntegrationResult containing HTTPResponse
    """

    class WebhookIntegration(BaseIntegration):
        async def health_check(self) -> HTTPResponse:
            return HTTPResponse.from_aiohttp_response(
                response=aiohttp.ClientResponse(
                    status=200,
                    headers={"Content-Type": "application/json"},
                    body=json.dumps({"status": "ok"}).encode(),
                )
            )

    async with WebhookIntegration() as integration:
        return await integration._make_request(
            "POST", url, headers=headers, data=payload, retry_config=retry_config
        )
