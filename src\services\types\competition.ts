/**
 * Competition-related TypeScript interfaces
 */

import { BaseEntity, User, FileUpload } from './common'

// Competition types
export interface Competition extends BaseEntity {
  title: string
  description: string
  type: CompetitionType
  status: CompetitionStatus
  category: CompetitionCategory
  difficulty: CompetitionDifficulty
  
  // Dates
  registrationStartDate: string
  registrationEndDate: string
  startDate: string
  endDate: string
  
  // Participation
  maxParticipants?: number
  currentParticipants: number
  teamSize: {
    min: number
    max: number
  }
  
  // Rewards
  rewards: CompetitionReward[]
  totalPrizePool?: number
  
  // Organization
  organizer: User
  sponsors?: CompetitionSponsor[]
  
  // Content
  rules: string
  requirements: string[]
  tags: string[]
  
  // Media
  banner?: FileUpload
  attachments?: FileUpload[]
  
  // Statistics
  stats: CompetitionStats
  
  // Settings
  isPublic: boolean
  allowTeams: boolean
  requireApproval: boolean
  
  // Metadata
  slug: string
  featured: boolean
  viewCount: number
}

export type CompetitionType = 'hackathon' | 'contest' | 'challenge' | 'tournament' | 'workshop'
export type CompetitionStatus = 'draft' | 'published' | 'registration_open' | 'registration_closed' | 'active' | 'completed' | 'cancelled'
export type CompetitionCategory = 'programming' | 'data_science' | 'ai_ml' | 'web_development' | 'mobile' | 'design' | 'other'
export type CompetitionDifficulty = 'beginner' | 'intermediate' | 'advanced' | 'expert'

export interface CompetitionReward {
  rank: number
  title: string
  description: string
  credits: number
  cash?: number
  items?: string[]
}

export interface CompetitionSponsor {
  id: string
  name: string
  logo: string
  website?: string
  tier: 'platinum' | 'gold' | 'silver' | 'bronze'
}

export interface CompetitionStats {
  totalParticipants: number
  totalTeams: number
  totalSubmissions: number
  averageScore?: number
  completionRate: number
  participantsBySchool: Record<string, number>
  participantsByLevel: Record<string, number>
}

// Participant types
export interface CompetitionParticipant extends BaseEntity {
  competitionId: string
  user: User
  team?: CompetitionTeam
  registrationDate: string
  status: ParticipantStatus
  submissions: CompetitionSubmission[]
  finalScore?: number
  rank?: number
  notes?: string
}

export type ParticipantStatus = 'registered' | 'approved' | 'rejected' | 'active' | 'disqualified' | 'completed'

export interface CompetitionTeam extends BaseEntity {
  name: string
  description?: string
  leader: User
  members: User[]
  competitionId: string
  status: TeamStatus
  inviteCode?: string
  maxMembers: number
}

export type TeamStatus = 'forming' | 'complete' | 'active' | 'disqualified'

// Submission types
export interface CompetitionSubmission extends BaseEntity {
  competitionId: string
  participantId: string
  teamId?: string
  title: string
  description: string
  files: FileUpload[]
  score?: number
  feedback?: string
  status: SubmissionStatus
  submittedAt: string
  evaluatedAt?: string
  evaluatedBy?: User
}

export type SubmissionStatus = 'draft' | 'submitted' | 'under_review' | 'evaluated' | 'rejected'

// Form types for creating/editing competitions
export interface CompetitionFormData {
  title: string
  description: string
  type: CompetitionType
  category: CompetitionCategory
  difficulty: CompetitionDifficulty
  
  registrationStartDate: string
  registrationEndDate: string
  startDate: string
  endDate: string
  
  maxParticipants?: number
  teamSize: {
    min: number
    max: number
  }
  
  rewards: CompetitionReward[]
  rules: string
  requirements: string[]
  tags: string[]
  
  isPublic: boolean
  allowTeams: boolean
  requireApproval: boolean
  featured: boolean
}

// API request/response types
export interface CompetitionListParams {
  page?: number
  limit?: number
  status?: CompetitionStatus
  type?: CompetitionType
  category?: CompetitionCategory
  difficulty?: CompetitionDifficulty
  search?: string
  featured?: boolean
  organizerId?: string
  tags?: string[]
  dateRange?: {
    start: string
    end: string
  }
}

export interface CompetitionParticipantListParams {
  competitionId: string
  page?: number
  limit?: number
  status?: ParticipantStatus
  search?: string
  teamId?: string
}

export interface JoinCompetitionRequest {
  competitionId: string
  teamId?: string
  message?: string
}

export interface CreateTeamRequest {
  competitionId: string
  name: string
  description?: string
}

export interface InviteTeamMemberRequest {
  teamId: string
  userId: string
  message?: string
}
