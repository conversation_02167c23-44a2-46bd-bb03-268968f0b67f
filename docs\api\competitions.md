# Competitions API Documentation

## Overview

The Competitions API provides endpoints for managing competition user registration and participation data. All endpoints are prefixed with `/api/v1/competitions`.

## Authentication

All endpoints require authentication via the `get_current_user_id` dependency.

## Endpoints

### Competition User Registration

#### Get Competition Users by Type
```http
POST /api/v1/competitions/register
```

**Description**: Fetch competition users based on the competition type and an optional registration start date.

**Parameters**:
- `comp_type` (query, optional): Type of competition (default: "DATA_ANALYSIS")
- `profile_type` (query, optional): Type of competition user profile - `basic`, `detail`, `full_form` (default: `basic`)
- `limit` (query, optional): Number of users to return (1-1000, default: 20)
- `offset` (query, optional): Number of users to skip (default: 0)

**Request Body**: `CompetitionUserRegisterRequest`
```json
{
  "register_start_date": "2023-01-01"
}
```

**Response**: `SuccessResponse` with `CompetitionUserProfileResponse`

**Example Response**:
```json
{
  "success": true,
  "data": [
    {
      "user_id": "string",
      "register_ts": "2023-01-01T00:00:00Z",
      "Phone": "string",
      "Email": "string",
      "competition_id": "string"
    }
  ],
  "total": 100,
  "limit": 20,
  "offset": 0
}
```

---

#### Get Competition Users by Identity
```http
POST /api/v1/competitions/register/identity
```

**Description**: Fetch competition users based on identity and an optional registration start date.

**Parameters**:
- `profile_type` (query, optional): Type of competition user profile - `basic`, `detail`, `full_form` (default: `basic`)
- `limit` (query, optional): Number of users to return (1-1000, default: 20)
- `offset` (query, optional): Number of users to skip (default: 0)

**Request Body**: `CompetitionUserIdentityRegisterRequest`
```json
{
  "identity": "student",
  "register_start_date": "2023-01-01"
}
```

**Response**: `SuccessResponse` with `CompetitionUserProfileResponse`

---

## Data Models

### Competition User Profile Types

#### CompetitionUserProfileBasic
```json
{
  "user_id": "string",
  "register_ts": "2023-01-01T00:00:00Z",
  "Phone": "string",
  "Email": "string",
  "competition_id": "string"
}
```

#### CompetitionUserProfileDetail (extends Basic)
```json
{
  "competition_type": "DATA_ANALYSIS",
  "competition_name": "Data Science Competition 2023"
}
```

#### CompetitionUserProfileFullForm (extends Basic)
```json
{
  "register_form": {
    "field1": "value1",
    "field2": "value2"
  }
}
```

### Request Schemas

#### CompetitionUserRegisterRequest
```json
{
  "register_start_date": "2023-01-01"
}
```

#### CompetitionUserIdentityRegisterRequest
```json
{
  "identity": "student",
  "register_start_date": "2023-01-01"
}
```

### Profile Types Enum
- `basic`: Basic user information (user_id, register_ts, Phone, Email, competition_id)
- `detail`: Basic + competition details (competition_type, competition_name)
- `full_form`: Basic + complete registration form data

## Error Responses

All endpoints may return standard error responses:
- `400 Bad Request`: Invalid parameters or request body
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Competition not found
- `500 Internal Server Error`: Server error

## Usage Examples

### Frontend Integration

#### Fetch Competition Users by Type
```javascript
// Get DATA_ANALYSIS competition users
const response = await fetch('/api/v1/competitions/register?comp_type=DATA_ANALYSIS', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer <token>'
  },
  body: JSON.stringify({
    register_start_date: '2023-01-01'
  })
});

const data = await response.json();
```

#### Fetch Competition Users by Identity with Pagination
```javascript
// Get student competition users with pagination
const response = await fetch('/api/v1/competitions/register/identity?limit=50&offset=0', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer <token>'
  },
  body: JSON.stringify({
    identity: 'student',
    register_start_date: '2023-01-01'
  })
});
```

#### Get Detailed Competition User Profiles
```javascript
// Get detailed profiles including competition info
const response = await fetch('/api/v1/competitions/register?profile_type=detail', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer <token>'
  },
  body: JSON.stringify({
    register_start_date: '2023-01-01'
  })
});
```

## Notes for Frontend Development

1. **Profile Types**: Choose appropriate profile type based on data needs
   - Use `basic` for simple user lists
   - Use `detail` when you need competition information
   - Use `full_form` when you need complete registration data

2. **Pagination**: Always implement pagination for large datasets
3. **Date Format**: Use ISO date format (YYYY-MM-DD) for registration dates
4. **Error Handling**: Check response status and handle errors appropriately
5. **Authentication**: Include proper authorization headers
6. **Competition Types**: Common types include "DATA_ANALYSIS", but check with backend for full list

## Related APIs

- **Users API**: For detailed user profile information
- **Community API**: For university and major data related to competitions
- **Analytics API**: For competition statistics and rankings
