import streamlit as st
from pages.login import login_pg
from pages.register import register_pg
from libs.utils import verify_login
from datetime import datetime, timedelta
from libs.utils import add_user


def auth_page():
    l = st.navigation([login_pg, register_pg], position="hidden")
    l.run()


def register():
    st.title("社区管理员注册😀")

    register_form = st.form("register_form")
    email = register_form.text_input("Email", placeholder="输入你的邮箱")
    password = register_form.text_input(
        "Password", type="password", placeholder="密码长度至少为8位"
    )
    user_name = register_form.text_input(
        "User Name",
        placeholder="选填：输入你的用户名",
    )
    register_button = register_form.form_submit_button("注册", use_container_width=True)

    if register_button:
        message, status = add_user(email, password, user_name)
        if not status:
            st.error(message)
        else:
            st.success("Registration successful! Please log in.")
            st.page_link(login_pg, label="返回登录")


register_pg = st.Page(register, title="注册页面", url_path="registers")


def login():
    cookies = st.session_state.cookies
    st.title("社区管理员系统-夏令营")
    login_form = st.form("login_form")

    email = login_form.text_input("email", type="default", placeholder="登录邮箱")
    password = login_form.text_input(
        "Password", type="password", placeholder="登录密码"
    )
    login_button = login_form.form_submit_button("登录", use_container_width=True)
    st.page_link(register_pg, label="没有账号？点击注册", icon="🤔")

    if login_button:
        message, is_valid = verify_login(email, password)
        if not is_valid:
            st.error(message)
        else:
            st.success("登录成功！")
            st.session_state.logged_in = True
            st.session_state.username = email
            st.session_state.logged_in = True
            st.session_state.expire_cookie = False
            cookies["logged_in"] = "true"
            cookies["username"] = email
            expiration_time = datetime.now() + timedelta(days=365)
            cookies["expired_at"] = str(expiration_time.timestamp())
            cookies.save()
            st.rerun()
    st.session_state.cookies = cookies


login_pg = st.Page(login, title="登录页面", url_path="logins")


def app():
    return auth_page()


if __name__ == "__main__":
    app()
