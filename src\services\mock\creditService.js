/**
 * Mock Credit API Service
 */

import { initializeMockData } from './mockData.js'

// Initialize mock data
const mockData = initializeMockData()
let { credits, users } = mockData

// Helper functions
const delay = (ms = 500) => new Promise(resolve => setTimeout(resolve, ms))

const paginate = (data, page = 1, limit = 20) => {
  const offset = (page - 1) * limit
  const paginatedData = data.slice(offset, offset + limit)
  
  return {
    data: paginatedData,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: data.length,
      pages: Math.ceil(data.length / limit),
      hasNext: offset + limit < data.length,
      hasPrev: page > 1
    }
  }
}

const filterCredits = (credits, params) => {
  let filtered = [...credits]
  
  if (params.userId) {
    filtered = filtered.filter(credit => credit.userId === params.userId)
  }
  
  if (params.type) {
    filtered = filtered.filter(credit => credit.type === params.type)
  }
  
  if (params.source) {
    filtered = filtered.filter(credit => credit.source === params.source)
  }
  
  if (params.status) {
    filtered = filtered.filter(credit => credit.status === params.status)
  }
  
  if (params.search) {
    const searchLower = params.search.toLowerCase()
    filtered = filtered.filter(credit => 
      credit.reason.toLowerCase().includes(searchLower) ||
      credit.description?.toLowerCase().includes(searchLower) ||
      credit.user.name.toLowerCase().includes(searchLower)
    )
  }
  
  if (params.dateRange) {
    const { start, end } = params.dateRange
    if (start) {
      filtered = filtered.filter(credit => new Date(credit.issuedAt) >= new Date(start))
    }
    if (end) {
      filtered = filtered.filter(credit => new Date(credit.issuedAt) <= new Date(end))
    }
  }
  
  return filtered
}

// Mock API functions
export const creditService = {
  // Get credits list
  async getCredits(params = {}) {
    await delay()
    
    try {
      const filtered = filterCredits(credits, params)
      const result = paginate(filtered, params.page, params.limit)
      
      return {
        success: true,
        ...result
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'FETCH_ERROR',
          message: 'Failed to fetch credits'
        }
      }
    }
  },

  // Get credit by ID
  async getCredit(id) {
    await delay()
    
    try {
      const credit = credits.find(c => c.id === id)
      
      if (!credit) {
        return {
          success: false,
          error: {
            code: 'CREDIT_NOT_FOUND',
            message: 'Credit not found'
          }
        }
      }
      
      return {
        success: true,
        data: credit
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'FETCH_ERROR',
          message: 'Failed to fetch credit'
        }
      }
    }
  },

  // Create credit
  async createCredit(creditData) {
    await delay()
    
    try {
      const user = users.find(u => u.id === creditData.userId)
      if (!user) {
        return {
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found'
          }
        }
      }

      const newCredit = {
        id: `credit_${credits.length + 1}`,
        ...creditData,
        user,
        issuedBy: users[0], // Mock current user
        issuedAt: new Date().toISOString(),
        auditLog: [
          {
            id: `audit_${credits.length + 1}_1`,
            action: 'created',
            performedBy: users[0],
            performedAt: new Date().toISOString(),
            details: {},
            comment: 'Credit created'
          }
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      credits.push(newCredit)
      
      return {
        success: true,
        data: newCredit,
        message: 'Credit created successfully'
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'CREATE_ERROR',
          message: 'Failed to create credit'
        }
      }
    }
  },

  // Update credit
  async updateCredit(id, creditData) {
    await delay()
    
    try {
      const index = credits.findIndex(c => c.id === id)
      
      if (index === -1) {
        return {
          success: false,
          error: {
            code: 'CREDIT_NOT_FOUND',
            message: 'Credit not found'
          }
        }
      }
      
      credits[index] = {
        ...credits[index],
        ...creditData,
        updatedAt: new Date().toISOString()
      }
      
      // Add audit log entry
      credits[index].auditLog.push({
        id: `audit_${id}_${credits[index].auditLog.length + 1}`,
        action: 'modified',
        performedBy: users[0],
        performedAt: new Date().toISOString(),
        details: creditData,
        comment: 'Credit updated'
      })
      
      return {
        success: true,
        data: credits[index],
        message: 'Credit updated successfully'
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'UPDATE_ERROR',
          message: 'Failed to update credit'
        }
      }
    }
  },

  // Cancel credit
  async cancelCredit(id, reason) {
    await delay()
    
    try {
      const index = credits.findIndex(c => c.id === id)
      
      if (index === -1) {
        return {
          success: false,
          error: {
            code: 'CREDIT_NOT_FOUND',
            message: 'Credit not found'
          }
        }
      }
      
      credits[index].status = 'cancelled'
      credits[index].updatedAt = new Date().toISOString()
      
      // Add audit log entry
      credits[index].auditLog.push({
        id: `audit_${id}_${credits[index].auditLog.length + 1}`,
        action: 'cancelled',
        performedBy: users[0],
        performedAt: new Date().toISOString(),
        details: { reason },
        comment: reason || 'Credit cancelled'
      })
      
      return {
        success: true,
        data: credits[index],
        message: 'Credit cancelled successfully'
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'CANCEL_ERROR',
          message: 'Failed to cancel credit'
        }
      }
    }
  },

  // Get user credit balance
  async getUserCreditBalance(userId) {
    await delay()
    
    try {
      const userCredits = credits.filter(c => c.userId === userId)
      
      const totalCredits = userCredits
        .filter(c => c.status === 'issued' && c.type !== 'penalty')
        .reduce((sum, c) => sum + c.amount, 0)
      
      const penalties = userCredits
        .filter(c => c.status === 'issued' && c.type === 'penalty')
        .reduce((sum, c) => sum + c.amount, 0)
      
      const pendingCredits = userCredits
        .filter(c => c.status === 'pending')
        .reduce((sum, c) => sum + c.amount, 0)
      
      const expiredCredits = userCredits
        .filter(c => c.status === 'expired')
        .reduce((sum, c) => sum + c.amount, 0)
      
      const balance = {
        userId,
        totalCredits: totalCredits - penalties,
        availableCredits: totalCredits - penalties,
        pendingCredits,
        expiredCredits,
        lastUpdated: new Date().toISOString()
      }
      
      return {
        success: true,
        data: balance
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'BALANCE_ERROR',
          message: 'Failed to fetch credit balance'
        }
      }
    }
  },

  // Get credit statistics
  async getCreditStats() {
    await delay()
    
    try {
      const totalIssued = credits.filter(c => c.status === 'issued').length
      const totalPending = credits.filter(c => c.status === 'pending').length
      const totalExpired = credits.filter(c => c.status === 'expired').length
      
      const issuedCredits = credits.filter(c => c.status === 'issued')
      const averageAmount = issuedCredits.length > 0 
        ? issuedCredits.reduce((sum, c) => sum + c.amount, 0) / issuedCredits.length 
        : 0
      
      const distributionByType = credits.reduce((acc, c) => {
        acc[c.type] = (acc[c.type] || 0) + 1
        return acc
      }, {})
      
      const distributionBySource = credits.reduce((acc, c) => {
        acc[c.source] = (acc[c.source] || 0) + 1
        return acc
      }, {})
      
      const monthlyTrend = [
        { month: '2024-01', issued: 45, amount: 12500 },
        { month: '2024-02', issued: 67, amount: 18900 },
        { month: '2024-03', issued: 89, amount: 24300 }
      ]
      
      const stats = {
        totalIssued,
        totalPending,
        totalExpired,
        averageAmount: Math.round(averageAmount),
        distributionByType,
        distributionBySource,
        monthlyTrend
      }
      
      return {
        success: true,
        data: stats
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'STATS_ERROR',
          message: 'Failed to fetch credit statistics'
        }
      }
    }
  },

  // Bulk credit distribution
  async bulkDistributeCredits(distributionData) {
    await delay(1000) // Longer delay for bulk operation
    
    try {
      const { recipients, creditAmount, reason, description } = distributionData
      const results = []
      
      for (const recipient of recipients) {
        const user = users.find(u => u.id === recipient.userId)
        if (!user) {
          results.push({
            userId: recipient.userId,
            status: 'failed',
            error: 'User not found'
          })
          continue
        }
        
        const newCredit = {
          id: `credit_bulk_${Date.now()}_${recipient.userId}`,
          amount: recipient.amount || creditAmount,
          type: 'reward',
          source: 'manual',
          status: 'issued',
          userId: recipient.userId,
          user,
          reason: recipient.reason || reason,
          description,
          issuedBy: users[0],
          issuedAt: new Date().toISOString(),
          auditLog: [
            {
              id: `audit_bulk_${Date.now()}_1`,
              action: 'created',
              performedBy: users[0],
              performedAt: new Date().toISOString(),
              details: { bulkDistribution: true },
              comment: 'Bulk credit distribution'
            }
          ],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
        
        credits.push(newCredit)
        results.push({
          userId: recipient.userId,
          status: 'processed',
          creditId: newCredit.id
        })
      }
      
      const successCount = results.filter(r => r.status === 'processed').length
      const failureCount = results.filter(r => r.status === 'failed').length
      
      return {
        success: true,
        data: {
          totalRecipients: recipients.length,
          successCount,
          failureCount,
          results
        },
        message: `Bulk distribution completed: ${successCount} successful, ${failureCount} failed`
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'BULK_DISTRIBUTION_ERROR',
          message: 'Failed to distribute credits'
        }
      }
    }
  }
}
