import streamlit as st


def home_page():
    pages = [
        st.Page("pages/admin_home.py", title="主页"),
        # st.Page("pages/schools.py",title="学校统计"),
        # st.Page("pages/credits.py",title="学分发放"),
        # st.Page("pages/competitions.py",title="学分规则管理"),
        # st.Page("pages/utils.py",title="下载/工具"),
    ]

    pg = st.navigation(pages)
    pg.run()


def app():
    return home_page()


if __name__ == "__main__":
    app()
