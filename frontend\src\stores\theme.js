import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'

export const useThemeStore = defineStore('theme', () => {
  // State
  const currentTheme = ref('light') // 'light' | 'dark' | 'auto'
  const systemPrefersDark = ref(false)

  // Getters
  const isDark = computed(() => {
    if (currentTheme.value === 'auto') {
      return systemPrefersDark.value
    }
    return currentTheme.value === 'dark'
  })

  const themeClass = computed(() => {
    return isDark.value ? 'dark' : 'light'
  })

  const themeIcon = computed(() => {
    return isDark.value ? 'Sunny' : 'Moon'
  })

  const themeLabel = computed(() => {
    return isDark.value ? 'Switch to Light Mode' : 'Switch to Dark Mode'
  })

  // Actions
  const setTheme = (theme) => {
    if (['light', 'dark', 'auto'].includes(theme)) {
      currentTheme.value = theme
      applyTheme()
      saveThemePreference()
    }
  }

  const toggleTheme = () => {
    if (currentTheme.value === 'light') {
      setTheme('dark')
    } else if (currentTheme.value === 'dark') {
      setTheme('light')
    } else {
      // If auto, toggle to opposite of current system preference
      setTheme(systemPrefersDark.value ? 'light' : 'dark')
    }
  }

  const applyTheme = () => {
    const html = document.documentElement
    
    // Remove existing theme classes
    html.classList.remove('light', 'dark')
    
    // Add current theme class
    html.classList.add(themeClass.value)
    
    // Set data attribute for CSS custom properties
    html.setAttribute('data-theme', themeClass.value)
    
    // Update Element Plus theme if available
    if (isDark.value) {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }
  }

  const saveThemePreference = () => {
    localStorage.setItem('theme_preference', currentTheme.value)
  }

  const loadThemePreference = () => {
    const saved = localStorage.getItem('theme_preference')
    if (saved && ['light', 'dark', 'auto'].includes(saved)) {
      currentTheme.value = saved
    }
  }

  const detectSystemTheme = () => {
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      systemPrefersDark.value = mediaQuery.matches
      
      // Listen for system theme changes
      mediaQuery.addEventListener('change', (e) => {
        systemPrefersDark.value = e.matches
        if (currentTheme.value === 'auto') {
          applyTheme()
        }
      })
    }
  }

  const initializeTheme = () => {
    // Detect system theme preference
    detectSystemTheme()
    
    // Load saved theme preference
    loadThemePreference()
    
    // Apply initial theme
    applyTheme()
  }

  // Watch for theme changes to apply them
  watch(
    () => isDark.value,
    () => {
      applyTheme()
    }
  )

  return {
    // State
    currentTheme,
    systemPrefersDark,
    
    // Getters
    isDark,
    themeClass,
    themeIcon,
    themeLabel,
    
    // Actions
    setTheme,
    toggleTheme,
    initializeTheme,
    applyTheme
  }
})
