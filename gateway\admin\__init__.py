"""
Admin Layer - CRUD operations with audit logging and transaction support.

Provides admin services for user, competition, credit, and tag management.
"""

from .base import BaseAdmin, AuditLog
from .user import UserAdmin
from .competition import CompetitionAdmin
from .credit import CreditAdmin
from .tag import TagAdmin

__all__ = [
    "BaseAdmin",
    "AuditLog",
    "UserAdmin",
    "CompetitionAdmin",
    "CreditAdmin",
    "TagAdmin",
]
