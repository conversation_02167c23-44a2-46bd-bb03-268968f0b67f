import streamlit as st

# @mysql_operation()
# def get_user_name(session,cookie_user_name=None):
#     email=st.session_state.get('username','')
#     if cookie_user_name:
#         user = session.query(Adminstrators).filter(Adminstrators.email_address==cookie_user_name).first()
#     else:
#         user = session.query(Adminstrators).filter(Adminstrators.email_address==email).first()

#     if user:
#         user_name = user.user_name
#     else:
#         user_name = "Anonymous"
#     return user_name

# st.title("欢迎回来，%s" % get_user_name())
st.title("")
st.warning("注意：请勿将此网站链接分享给公司之外的人")
st.warning("注意：请勿刷新浏览器，如需刷新页面，点击右上角[...] 选择 rerun")
# if st.button("登出"):
#     st.session_state.logged_in = False
#     st.session_state.username = ''
#     st.session_state.expire_cookie = True
#     st.rerun()

# st.session_state.default_competition = ''
# st.session_state.default_school = ''
