"""
Test for the new domain-focused architecture.

Verifies that the domain-focused architecture with admin/integration layers works correctly.
"""

import pytest
from unittest.mock import AsyncMock, patch

from core.container import (
    get_layered_container,
    initialize_container,
    get_credit_admin,
    get_shence_integration,
)
from gateway.domains.users.services import UserServices
from gateway.domains.competitions.services import CompetitionServices
from gateway.domains.analytics.services import AnalyticsServices
from gateway.domains.community.services import CommunityServices
from libs.auth import create_auth_context


class TestDomainArchitecture:
    """Test the new domain-focused architecture."""

    def test_container_initialization(self):
        """Test that the service container initializes correctly."""
        container = get_layered_container()
        assert container is not None

        # Initialize services
        container = initialize_container()
        assert container.is_initialized()

        # Check service status
        status = container.get_service_status()
        assert status["initialized"] is True
        # Domain queries have been moved to domain services
        assert "credit" in status["query_services"]  # Non-domain service still exists
        assert "credit_admin" in status["admin_services"]
        assert "shence_integration" in status["integration_services"]

    def test_service_access(self):
        """Test that services can be accessed through domain services and convenience functions."""
        # Initialize services
        initialize_container()

        # Test domain service access
        user_service = UserServices()
        assert user_service is not None
        assert hasattr(user_service, "get_user_profile")

        competition_service = CompetitionServices()
        assert competition_service is not None
        assert hasattr(competition_service, "get_routes")

        # Test admin service access
        credit_admin = get_credit_admin()
        assert credit_admin is not None
        assert hasattr(credit_admin, "award_credits")
        assert hasattr(credit_admin, "health_check")

        # Test integration service access
        shence_integration = get_shence_integration()
        assert shence_integration is not None
        assert hasattr(shence_integration, "track_event")
        assert hasattr(shence_integration, "health_check")

    def test_auth_context_creation(self):
        """Test authentication context creation."""
        # Test valid user ID
        auth_context = create_auth_context("test_user_123")
        assert auth_context.user_id == "test_user_123"
        assert auth_context.is_authenticated is True

        # Test with user data
        user_data = {"name": "Test User", "role": "admin"}
        auth_context = create_auth_context("test_user_123", user_data)
        assert auth_context.get_user_property("name") == "Test User"
        assert auth_context.get_user_property("role") == "admin"
        assert auth_context.get_user_property("missing", "default") == "default"

        # Test invalid user ID
        with pytest.raises(ValueError):
            create_auth_context("")

    @pytest.mark.asyncio
    async def test_domain_service_functionality(self):
        """Test domain service functionality."""
        # Test domain services can be instantiated and have expected methods
        user_service = UserServices()
        assert hasattr(user_service, "get_user_profile")
        assert hasattr(user_service, "update_user_profile")
        assert hasattr(user_service, "get_users_by_location")

        competition_service = CompetitionServices()
        assert hasattr(competition_service, "get_routes")
        assert hasattr(competition_service, "get_competitions")

        analytics_service = AnalyticsServices()
        assert hasattr(analytics_service, "get_rankings")

        community_service = CommunityServices()
        assert hasattr(community_service, "get_universities")
        assert hasattr(community_service, "get_majors")

    @pytest.mark.asyncio
    async def test_admin_service_health_check(self):
        """Test admin service health check."""
        initialize_container()
        credit_admin = get_credit_admin()

        # Mock MongoDB connection for testing
        with (
            patch("gateway.admin.base.get_mongo_db_client"),
            patch("gateway.admin.base.get_mongo_database") as mock_db,
        ):
            mock_collection = AsyncMock()
            mock_collection.count_documents.return_value = 1
            mock_db.return_value.__getitem__.return_value = mock_collection

            # Mock transaction support check
            with patch("gateway.admin.base.check_transaction_support") as mock_tx:
                mock_tx.return_value.is_success = True
                mock_tx.return_value.data = {"transactions_supported": True}

                result = await credit_admin.health_check()
                assert result.is_success
                assert result.data["service"] == "CreditAdmin"
                assert result.data["status"] == "healthy"

    @pytest.mark.asyncio
    async def test_integration_service_health_check(self):
        """Test integration service health check."""
        initialize_container()
        shence_integration = get_shence_integration()

        # Mock configuration
        with patch.object(shence_integration, "_base_url", "https://test.shence.com"):
            # Mock HTTP request
            with patch.object(shence_integration, "_get_request") as mock_request:
                mock_response = AsyncMock()
                mock_response.is_success = True
                mock_response.data.is_success = True

                mock_request.return_value.is_success = True
                mock_request.return_value.data = mock_response

                result = await shence_integration.health_check()
                assert result.is_success
                assert result.data["service"] == "ShenceIntegration"
                assert result.data["status"] == "healthy"

    def test_service_isolation(self):
        """Test that services are properly isolated by domain and layer."""
        initialize_container()

        user_service = UserServices()
        credit_admin = get_credit_admin()
        shence_integration = get_shence_integration()

        # Verify services are different instances
        assert user_service is not credit_admin
        assert user_service is not shence_integration
        assert credit_admin is not shence_integration

        # Verify services have domain/layer-specific methods
        # Domain services should have domain-specific methods
        assert hasattr(user_service, "get_user_profile")
        assert not hasattr(user_service, "award_credits")  # Admin method
        assert not hasattr(user_service, "track_event")  # Integration method

        # Admin layer should have CRUD and audit methods
        assert hasattr(credit_admin, "award_credits")
        assert not hasattr(credit_admin, "get_user_profile")  # Domain method
        assert not hasattr(credit_admin, "track_event")  # Integration method

        # Integration layer should have external API methods
        assert hasattr(shence_integration, "track_event")
        assert not hasattr(shence_integration, "get_user_profile")  # Domain method
        assert not hasattr(shence_integration, "award_credits")  # Admin method


if __name__ == "__main__":
    pytest.main([__file__])
