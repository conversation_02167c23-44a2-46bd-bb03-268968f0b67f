from models.base import Adminstrators
from libs.wrappers import mysql_operation
from hashlib import sha256
from importlib import import_module
import string
import random


def hash_password(pswd):
    """
    returns a hashed password for pswd
    """
    return sha256(pswd.encode("utf-8")).hexdigest()


def random_username():
    return "".join(
        random.choice(string.ascii_uppercase + string.digits) for _ in range(10)
    )


def check_email_validity(email):
    """
    checks if email is valid
    """
    if "@" in email and "." in email:
        return True
    return False


# 加载页面
def load_page(page_name):
    module = import_module(page_name)
    module.app()


# 确认登录
@mysql_operation()
def verify_login(session, email, password):
    if (
        not session.query(Adminstrators)
        .filter(Adminstrators.email_address == email)
        .first()
    ):
        return f"user {email} does not exist", False

    if (
        hash_password(password)
        == session.query(Adminstrators)
        .filter(Adminstrators.email_address == email)
        .first()
        .password
    ):
        user_name = (
            session.query(Adminstrators)
            .filter(Adminstrators.email_address == email)
            .first()
            .user_name
        )
        return f"welcome {user_name}", True
    else:
        return "password authentication failed", False


@mysql_operation()
def add_user(session, email, password, user_name=None):
    if not check_email_validity(email):
        return "invalid email address", False
    if not len(password) >= 8:
        return "password must be at least 8 characters", False
    if not user_name:
        user_name = random_username()
    if (
        session.query(Adminstrators)
        .filter(Adminstrators.email_address == email)
        .first()
    ):
        return f"user {email} already exists", False
    else:
        user = Adminstrators(
            email_address=email, password=hash_password(password), user_name=user_name
        )
        session.add(user)
        return f"user {email} added successfully", True
