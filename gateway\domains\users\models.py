"""
User domain-specific models and data structures.

Contains models and data classes specific to the users domain
that are not shared across the application.
"""

from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from datetime import datetime
from enum import Enum


class UserStatus(str, Enum):
    """User status enumeration."""

    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    BANNED = "banned"


class UserRole(str, Enum):
    """User role enumeration."""

    USER = "user"
    ADMIN = "admin"
    MODERATOR = "moderator"


@dataclass
class UserContext:
    """Context data for user operations."""

    user_id: str
    role: UserRole = UserRole.USER
    permissions: List[str] = None

    def __post_init__(self):
        if self.permissions is None:
            self.permissions = []


@dataclass
class UserSearchQuery:
    """User search query parameters."""

    query: str
    filters: Dict[str, Any] = None
    sort_by: Optional[str] = None
    sort_order: str = "asc"

    def __post_init__(self):
        if self.filters is None:
            self.filters = {}


@dataclass
class UserActivity:
    """User activity log entry."""

    user_id: str
    activity_type: str
    description: str
    metadata: Dict[str, Any] = None
    timestamp: datetime = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()


# TODO: Add other domain-specific models as needed
