<template>
  <div class="qualifications-page">
    <!-- Page Header -->
    <div class="page-header">
      <div class="header-content">
        <h1>规则管理</h1>
        <p>管理所有活动的学分规则</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          创建学分规则
        </el-button>
        <el-button @click="refreshData" :loading="refreshing">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- Filters -->
    <el-card class="filter-card" style="margin-bottom: 20px;">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-select v-model="filters.competitionId" placeholder="Filter by Competition" clearable @change="loadQualifications">
            <el-option
              v-for="competition in competitions"
              :key="competition.id"
              :label="competition.title || competition.name"
              :value="competition.id"
            />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select v-model="filters.type" placeholder="Filter by Type" clearable @change="loadQualifications">
            <el-option
              v-for="type in qualificationTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="filters.search"
            placeholder="Search qualifications..."
            @input="handleSearch"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="6">
          <el-button type="info" @click="exportQualifications">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- Statistics Cards -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><Trophy /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ totalQualifications }}</div>
              <div class="stat-label">学分规则总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon active">
              <el-icon><Star /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ activeQualifications }}</div>
              <div class="stat-label">总发放人次</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon credits">
              <el-icon><Coin /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ totalCredits }}</div>
              <div class="stat-label">总学分发放</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon competitions">
              <el-icon><Flag /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ uniqueCompetitions }}</div>
              <div class="stat-label">活动数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- Qualifications Table -->
    <el-card>
      <el-table
        :data="paginatedQualifications"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="qualification_name" label="规则说明" min-width="200">
          <template #default="{ row }">
            <div class="qualification-name">
              <strong>{{ row.qualification_name }}</strong>
              <div class="qualification-meta">
                <el-tag size="small" type="info">{{ getTypeName(row.qualification_type) }}</el-tag>
                <span class="competition-name">{{ getCompetitionName(row.competition_id) }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="credit" label="对应学分" width="100" align="center">
          <template #default="{ row }">
            <el-tag type="success" size="large">{{ row.credit }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="qualification_logic" label="得分逻辑" width="120">
          <template #default="{ row }">
            {{ getLogicName(row.qualification_logic) }}
          </template>
        </el-table-column>

        <el-table-column prop="score_threshold" label="分数基准线" width="100" align="center">
          <template #default="{ row }">
            <span v-if="row.score_threshold">{{ row.score_threshold }}%</span>
            <span v-else class="text-muted">N/A</span>
          </template>
        </el-table-column>

        <el-table-column prop="related_task_id" label="关联任务 ID" width="120">
          <template #default="{ row }">
            <span v-if="row.related_task_id">{{ row.related_task_id }}</span>
            <span v-else class="text-muted">None</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="editQualification(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <!-- <el-button type="success" size="small" @click="awardCredits(row)">
              <el-icon><Coin /></el-icon>
              学分管理
            </el-button> -->
            <el-button type="danger" size="small" @click="deleteQualification(row)">
              <el-icon><Delete /></el-icon>
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- Pagination -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="filteredQualifications.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- Create Qualification Dialog -->
    <el-dialog
      v-model="showCreateDialog"
      title="Create Qualification"
      width="600px"
    >
      <QualificationForm
        @success="handleCreateSuccess"
        @cancel="showCreateDialog = false"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Refresh,
  Search,
  Download,
  Trophy,
  Star,
  Coin,
  Flag,
  Edit,
  Delete
} from '@element-plus/icons-vue'
import { competitionService } from '@/services/competitionService'
import QualificationForm from '@/components/qualifications/QualificationForm.vue'
import { debounce } from 'lodash-es'

const router = useRouter()

// State
const loading = ref(true)
const refreshing = ref(false)
const showCreateDialog = ref(false)
const qualifications = ref([])
const competitions = ref([])
const currentPage = ref(1)
const pageSize = ref(20)

// Filters
const filters = ref({
  competitionId: '',
  type: '',
  search: ''
})

// Options
const qualificationTypes = [
  { value: 1, label: 'Score Based' },
  { value: 2, label: 'Rank Based' },
  { value: 3, label: 'Participation' },
  { value: 4, label: 'Completion' },
  { value: 5, label: 'Custom' }
]

// Computed
const filteredQualifications = computed(() => {
  let filtered = [...qualifications.value]

  if (filters.value.competitionId) {
    filtered = filtered.filter(q => q.competition_id === filters.value.competitionId)
  }

  if (filters.value.type) {
    filtered = filtered.filter(q => q.qualification_type === filters.value.type)
  }

  if (filters.value.search) {
    const search = filters.value.search.toLowerCase()
    filtered = filtered.filter(q => 
      q.qualification_name.toLowerCase().includes(search) ||
      (q.description && q.description.toLowerCase().includes(search))
    )
  }

  return filtered
})

const paginatedQualifications = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredQualifications.value.slice(start, end)
})

const totalQualifications = computed(() => qualifications.value.length)
const activeQualifications = computed(() => qualifications.value.filter(q => q.is_active !== false).length)
const totalCredits = computed(() => qualifications.value.reduce((sum, q) => sum + (q.credit || 0), 0))
const uniqueCompetitions = computed(() => new Set(qualifications.value.map(q => q.competition_id)).size)

// Methods
const getTypeName = (type) => {
  const typeObj = qualificationTypes.find(t => t.value === type)
  return typeObj?.label || 'Unknown'
}

const getLogicName = (logic) => {
  const logicNames = {
    1: 'Greater Than',
    2: 'Less Than', 
    3: 'Equal To',
    4: 'Between',
    5: 'Top N',
    6: 'Bottom N'
  }
  return logicNames[logic] || 'Unknown'
}

const getCompetitionName = (competitionId) => {
  const competition = competitions.value.find(c => c.id === competitionId)
  return competition?.title || competition?.name || 'Unknown Competition'
}

const loadQualifications = async () => {
  loading.value = true
  try {
    // Load competitions and all qualifications in parallel
    const [competitionsResponse, qualificationsResponse] = await Promise.all([
      competitionService.getCompetitions(),
      competitionService.getAllQualifications()
    ])

    if (competitionsResponse.success) {
      competitions.value = competitionsResponse.data
    }

    if (qualificationsResponse.success) {
      qualifications.value = qualificationsResponse.data
    } else {
      throw new Error(qualificationsResponse.error?.message || 'Failed to load qualifications')
    }
  } catch (error) {
    console.error('Failed to load qualifications:', error)
    ElMessage.error('Failed to load qualifications')
  } finally {
    loading.value = false
  }
}

const handleSearch = debounce(() => {
  currentPage.value = 1
}, 300)

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

const refreshData = async () => {
  refreshing.value = true
  await loadQualifications()
  refreshing.value = false
  ElMessage.success('Data refreshed')
}

const editQualification = (qualification) => {
  router.push(`/competitions/${qualification.competition_id}/qualifications/${qualification.id}/edit`)
}

const awardCredits = (qualification) => {
  router.push(`/credits?competition=${qualification.competition_id}&qualification=${qualification.id}`)
}

const deleteQualification = async (qualification) => {
  try {
    await ElMessageBox.confirm(
      `Are you sure you want to delete "${qualification.qualification_name}"?`,
      'Delete Qualification',
      {
        confirmButtonText: 'Delete',
        cancelButtonText: 'Cancel',
        type: 'warning'
      }
    )

    // TODO: Implement actual delete API call
    ElMessage.success('Qualification deleted successfully')
    await loadQualifications()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('Failed to delete qualification')
    }
  }
}

const exportQualifications = () => {
  ElMessage.info('Export functionality not implemented yet')
}

const handleCreateSuccess = () => {
  showCreateDialog.value = false
  loadQualifications()
}

// Lifecycle
onMounted(() => {
  loadQualifications()
})
</script>

<style lang="scss" scoped>
.qualifications-page {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;

  .header-content {
    h1 {
      font-size: 28px;
      font-weight: 600;
      color: var(--text-color);
      margin-bottom: 8px;
    }

    p {
      color: var(--text-color-secondary);
      font-size: 16px;
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.filter-card {
  .el-select,
  .el-input {
    width: 100%;
  }
}

.stat-card {
  .stat-content {
    display: flex;
    align-items: center;

    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;

      .el-icon {
        font-size: 24px;
        color: white;
      }

      &.total {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      &.active {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }

      &.credits {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }

      &.competitions {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }
    }

    .stat-info {
      flex: 1;

      .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: var(--text-color);
        line-height: 1;
      }

      .stat-label {
        font-size: 14px;
        color: var(--text-color-secondary);
        margin-top: 4px;
      }
    }
  }
}

.qualification-name {
  .qualification-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 4px;

    .competition-name {
      font-size: 12px;
      color: var(--text-color-secondary);
    }
  }
}

.text-muted {
  color: var(--text-color-secondary);
  font-style: italic;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

// Responsive design
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;

    .header-actions {
      justify-content: center;
    }
  }
}
</style>
