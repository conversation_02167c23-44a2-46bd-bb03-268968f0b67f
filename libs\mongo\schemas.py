from typing import Dict, List, Any, Union, Optional
from enum import Enum
from pydantic import BaseModel, Field, model_validator, field_validator
from datetime import datetime
import re

class MatchType(str, Enum):
    """Enumeration of supported match types for MongoDB queries."""
    EXACT = "exact"
    REGEX = "regex"
    ARRAY_CONTAINS_ANY = "array_contains_any"
    ARRAY_CONTAINS_ALL = "array_contains_all"
    NESTED_ARRAY_ELEMENT_MATCH = "nested_array_element_match"
    EXISTS = "exists"
    RANGE = "range"
    TEXT_SEARCH = "text_search"

class LogicOperator(str, Enum):
    """Logic operators for combining conditions."""
    AND = "and"
    OR = "or"

class RangeOperator(str, Enum):
    """Range comparison operators."""
    GTE = "gte"  # greater than or equal
    LTE = "lte"  # less than or equal
    GT = "gt"    # greater than
    LT = "lt"    # less than

# Base options models for different match types
class BaseMatchOptions(BaseModel):
    """Base class for match options."""
    pass

class RegexOptions(BaseMatchOptions):
    """Options for regex matching."""
    case_sensitive: bool = Field(default=False, description="Whether regex should be case sensitive")
    multiline: bool = Field(default=False, description="Enable multiline mode")
    dotall: bool = Field(default=False, description="Enable dotall mode")

class TextSearchOptions(BaseMatchOptions):
    """Options for text search."""
    logic: LogicOperator = Field(default=LogicOperator.OR, description="Logic for combining multiple search terms")
    case_sensitive: bool = Field(default=False, description="Whether search should be case sensitive")
    whole_word: bool = Field(default=False, description="Whether to match whole words only")

class NestedArrayOptions(BaseMatchOptions):
    """Options for nested array element matching."""
    logic: LogicOperator = Field(default=LogicOperator.AND, description="Logic for combining element conditions")

class RangeOptions(BaseMatchOptions):
    """Options for range matching."""
    min_value: Optional[Union[int, float, str, datetime]] = Field(default=None, alias="min", description="Minimum value (inclusive)")
    max_value: Optional[Union[int, float, str, datetime]] = Field(default=None, alias="max", description="Maximum value (inclusive)")
    greater_than: Optional[Union[int, float, str, datetime]] = Field(default=None, description="Greater than value (exclusive)")
    less_than: Optional[Union[int, float, str, datetime]] = Field(default=None, description="Less than value (exclusive)")
    
    @model_validator(mode='after')
    def validate_range_values(cls, values):
        """Ensure at least one range value is provided."""
        range_fields = ['min_value', 'max_value', 'greater_than', 'less_than']
        if not any(values.get(field) is not None for field in range_fields):
            raise ValueError("At least one range value must be specified")
        return values

class ExistsOptions(BaseMatchOptions):
    """Options for field existence checking."""
    exists: bool = Field(default=True, description="Whether field should exist (True) or not exist (False)")

# Main criteria model
class FieldCriteria(BaseModel):
    """Criteria for a single field match."""
    match_type: MatchType = Field(..., description="Type of matching to perform")
    values: List[Any] = Field(default_factory=list, description="Values to match against")
    options: Optional[Union[RegexOptions, TextSearchOptions, NestedArrayOptions, RangeOptions, ExistsOptions]] = Field(
        default=None, 
        description="Type-specific options for the match"
    )
    
    @field_validator('values')
    def validate_values_for_match_type(cls, v, values):
        """Validate values based on match type."""
        match_type = values.get('match_type')
        
        if match_type == MatchType.EXISTS:
            # EXISTS doesn't need values, options.exists determines behavior
            return v
        elif match_type == MatchType.RANGE:
            # RANGE uses options instead of values
            return v
        elif not v:
            raise ValueError(f"Values are required for match type: {match_type}")
        
        return v
    
    @model_validator(mode='after')  
    def validate_options_compatibility(cls, values):
        """Ensure options are compatible with match type."""
        match_type = values.get('match_type')
        options = values.get('options')
        
        if match_type == MatchType.REGEX and options and not isinstance(options, RegexOptions):
            raise ValueError("REGEX match type requires RegexOptions")
        elif match_type == MatchType.TEXT_SEARCH and options and not isinstance(options, TextSearchOptions):
            raise ValueError("TEXT_SEARCH match type requires TextSearchOptions")
        elif match_type == MatchType.NESTED_ARRAY_ELEMENT_MATCH and options and not isinstance(options, NestedArrayOptions):
            raise ValueError("NESTED_ARRAY_ELEMENT_MATCH match type requires NestedArrayOptions")
        elif match_type == MatchType.RANGE and options and not isinstance(options, RangeOptions):
            raise ValueError("RANGE match type requires RangeOptions")
        elif match_type == MatchType.EXISTS and options and not isinstance(options, ExistsOptions):
            raise ValueError("EXISTS match type requires ExistsOptions")
        
        return values

class FieldPath(BaseModel):
    """Validates MongoDB field paths."""
    path: str = Field(..., min_length=1, description="MongoDB field path using dot notation")
    
    @field_validator('path')
    def validate_field_path(cls, v):
        """Validate MongoDB field path format."""
        # Check for invalid characters
        if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*)*$', v):
            raise ValueError(f"Invalid field path format: {v}")
        return v

class MatchStageSpec(BaseModel):
    """Complete specification for building a MongoDB match stage.
    
    Example:
    {
        "criteria": {
            "Name": {
                "match_type": MatchType.TEXT_SEARCH,
                "values": [query],
            }
        }
    }
    """
    criteria: Dict[str, FieldCriteria] = Field(..., description="Field criteria mapping")
    
    @field_validator('criteria')
    def validate_criteria_keys(cls, v):
        """Validate field path keys."""
        for field_path in v.keys():
            FieldPath(path=field_path)  # This will raise if invalid
        return v
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        allow_population_by_field_name = True

# Convenience models for common patterns
class SimpleExactMatch(BaseModel):
    """Simple exact match specification."""
    field_values: Dict[str, List[Any]] = Field(..., description="Field to values mapping")
    
    def to_match_stage_spec(self) -> MatchStageSpec:
        """Convert to full MatchStageSpec."""
        criteria = {}
        for field_path, values in self.field_values.items():
            criteria[field_path] = FieldCriteria(
                match_type=MatchType.EXACT,
                values=values
            )
        return MatchStageSpec(criteria=criteria)

class ArraySearchSpec(BaseModel):
    """Array search specification."""
    field_values: Dict[str, List[Any]] = Field(..., description="Field to values mapping")
    match_all: bool = Field(default=False, description="Whether to match all values (True) or any values (False)")
    
    def to_match_stage_spec(self) -> MatchStageSpec:
        """Convert to full MatchStageSpec."""
        criteria = {}
        match_type = MatchType.ARRAY_CONTAINS_ALL if self.match_all else MatchType.ARRAY_CONTAINS_ANY
        
        for field_path, values in self.field_values.items():
            criteria[field_path] = FieldCriteria(
                match_type=match_type,
                values=values
            )
        return MatchStageSpec(criteria=criteria)

class TextSearchSpec(BaseModel):
    """Text search specification."""
    field_terms: Dict[str, List[str]] = Field(..., description="Field to search terms mapping")
    case_sensitive: bool = Field(default=False, description="Case sensitive search")
    whole_word: bool = Field(default=False, description="Match whole words only")
    logic: LogicOperator = Field(default=LogicOperator.OR, description="Logic for combining terms")
    
    def to_match_stage_spec(self) -> MatchStageSpec:
        """Convert to full MatchStageSpec."""
        criteria = {}
        options = TextSearchOptions(
            case_sensitive=self.case_sensitive,
            whole_word=self.whole_word,
            logic=self.logic
        )
        
        for field_path, terms in self.field_terms.items():
            criteria[field_path] = FieldCriteria(
                match_type=MatchType.TEXT_SEARCH,
                values=terms,
                options=options
            )
        return MatchStageSpec(criteria=criteria)

