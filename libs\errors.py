"""
Domain-focused error management for clean, consistent error handling.

Provides domain-specific error messages and constants without 
complex wrapper patterns, maintaining simple response structures.
"""

from enum import Enum
import logging

logger = logging.getLogger(__name__)


class ErrorCode(Enum):
    """Standard error codes for consistent error classification."""
    
    # Client errors (4xx equivalent)
    VALIDATION_ERROR = "VALIDATION_ERROR"
    NOT_FOUND = "NOT_FOUND"
    PERMISSION_DENIED = "PERMISSION_DENIED"
    BUSINESS_RULE_VIOLATION = "BUSINESS_RULE_VIOLATION"
    
    # Server errors (5xx equivalent)
    DATABASE_ERROR = "DATABASE_ERROR"
    EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR"
    TIMEOUT_ERROR = "TIMEOUT_ERROR"
    UNKNOWN_ERROR = "UNKNOWN_ERROR"


# ——— Domain-Specific Error Message Factories ———

class UserDomainErrors:
    """Standard error messages for Users domain."""
    
    @staticmethod
    def user_not_found(user_id: str) -> str:
        """Standard user not found message."""
        return f"用户未找到: {user_id}"
    
    @staticmethod
    def profile_validation_failed(field: str, reason: str) -> str:
        """Standard profile validation error message."""
        return f"用户资料验证失败 [{field}]: {reason}"
    
    @staticmethod
    def search_query_invalid(reason: str) -> str:
        """Standard search validation error message."""
        return f"搜索查询无效: {reason}"
    
    @staticmethod
    def user_id_required() -> str:
        """Standard user ID required message."""
        return "用户ID不能为空"
    
    @staticmethod
    def invalid_profile_type(profile_type: str) -> str:
        """Standard invalid profile type message."""
        return f"无效的用户资料类型: {profile_type}"
    
    @staticmethod
    def invalid_search_type(search_type: str) -> str:
        """Standard invalid search type message."""
        return f"无效的搜索类型: {search_type}"


class CompetitionDomainErrors:
    """Standard error messages for Competitions domain."""
    
    @staticmethod
    def competition_not_found(competition_id: str) -> str:
        return f"竞赛未找到: {competition_id}"
    
    @staticmethod
    def invalid_profile_type(profile_type: str) -> str:
        return f"无效的报名信息字段: {profile_type}。请使用 basic, detail, full_form 中的一个"
    
    @staticmethod
    def qualification_failed(rule: str) -> str:
        return f"参赛资格不符: {rule}"
    
    @staticmethod
    def invalid_competition_status(status: str) -> str:
        return f"无效的竞赛状态: {status}"
    
    @staticmethod
    def database_error(reason: str) -> str:
        return f"数据库错误: {reason}"


class AnalyticsDomainErrors:
    """Standard error messages for Analytics domain."""
    
    @staticmethod
    def aggregation_failed(metric: str, reason: str) -> str:
        return f"数据聚合失败 [{metric}]: {reason}"
    
    @staticmethod
    def invalid_time_range(start_date: str, end_date: str) -> str:
        return f"时间范围无效: {start_date} 至 {end_date}"
    
    @staticmethod
    def ranking_calculation_error(reason: str) -> str:
        return f"排名计算失败: {reason}"


class CommunityDomainErrors:
    """Standard error messages for Community domain."""
    
    @staticmethod
    def university_not_found(university_name: str) -> str:
        return f"学校未找到: {university_name}"
    
    @staticmethod
    def major_not_found(major_name: str) -> str:
        return f"专业未找到: {major_name}"

    @staticmethod
    def database_error(reason: str) -> str:
        return f"数据库错误: {reason}"

class CampAdminDomainErrors:
    """Standard error messages for CampAdmin domain."""
    
    @staticmethod
    def admin_log_create_failed(reason: str) -> str:
        return f"管理员日志创建失败: {reason}"
    
    @staticmethod
    def award_credit_failed(reason: str) -> str:
        return f"颁发学分失败: {reason}"
    
    @staticmethod
    def credit_log_create_failed(reason: str) -> str:
        return f"学分日志创建失败: {reason}"
    
    @staticmethod
    def credit_log_revoke_failed(reason: str) -> str:
        return f"学分撤销失败: {reason}"
    
    @staticmethod
    def qualification_create_failed(reason: str) -> str:
        return f"资格创建失败: {reason}"
    
    @staticmethod
    def database_error(reason: str) -> str:
        return f"数据库错误: {reason}"
    
    @staticmethod
    def invalid_pagination(limit: int, offset: int) -> str:
        return f"无效的分页参数: limit={limit}, offset={offset}"
    

class IntegrationErrors:
    """Standard error messages for Integration operations."""
    
    @staticmethod
    def external_service_failed(service: str, reason: str) -> str:
        return f"{service} 服务调用失败: {reason}"
    
    @staticmethod
    def timeout_error(service: str, timeout_seconds: int) -> str:
        return f"{service} 请求超时 ({timeout_seconds}秒)"
    
    @staticmethod
    def authentication_failed(service: str) -> str:
        return f"{service} 认证失败"
    
    @staticmethod
    def rate_limit_exceeded(service: str, retry_after: int = None) -> str:
        message = f"{service} 请求频率超限"
        if retry_after:
            message += f"，请在 {retry_after} 秒后重试"
        return message
    
    @staticmethod
    def configuration_error(service: str, missing_config: str) -> str:
        return f"{service} 配置错误: 缺少 {missing_config}"


# ——— HTTP Status Code Mappings ———

class ErrorCodeMapping:
    """Maps error codes to HTTP status codes."""
    
    HTTP_STATUS_MAP = {
        ErrorCode.VALIDATION_ERROR: 400,
        ErrorCode.NOT_FOUND: 404,
        ErrorCode.PERMISSION_DENIED: 403,
        ErrorCode.BUSINESS_RULE_VIOLATION: 422,
        ErrorCode.DATABASE_ERROR: 500,
        ErrorCode.EXTERNAL_SERVICE_ERROR: 502,
        ErrorCode.TIMEOUT_ERROR: 504,
        ErrorCode.UNKNOWN_ERROR: 500,
    }
    
    @classmethod
    def get_http_status(cls, error_code: ErrorCode) -> int:
        """Get HTTP status code for error code."""
        return cls.HTTP_STATUS_MAP.get(error_code, 500)


# ——— Legacy Error Constants (for backward compatibility) ———
CACHE_ERROR = ErrorCode.DATABASE_ERROR
QUERY_TIMEOUT = ErrorCode.TIMEOUT_ERROR
AGGREGATION_ERROR = ErrorCode.DATABASE_ERROR
TRANSACTION_FAILED = ErrorCode.DATABASE_ERROR
VALIDATION_ERROR = ErrorCode.VALIDATION_ERROR
NOT_FOUND = ErrorCode.NOT_FOUND
HTTP_CLIENT_ERROR = ErrorCode.EXTERNAL_SERVICE_ERROR
INTEGRATION_TIMEOUT = ErrorCode.TIMEOUT_ERROR


# ——— Utility Functions ———

def create_error_data(domain: str, error_type: str, **kwargs) -> dict:
    """Create standardized error data dictionary."""
    return {
        "domain": domain,
        "error_type": error_type,
        **kwargs
    }


def log_domain_error(domain: str, error_message: str, **context):
    """Log domain-specific errors with context."""
    logger.error(
        f"Domain error [{domain}]: {error_message}",
        extra={"domain": domain, "context": context}
    )
