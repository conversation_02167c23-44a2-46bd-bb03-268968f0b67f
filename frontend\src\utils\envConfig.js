/**
 * Environment Configuration Utility
 * Centralizes environment variable access and provides type-safe configuration
 */

/**
 * Get environment variable with fallback
 * @param {string} key - Environment variable key
 * @param {any} defaultValue - Default value if not found
 * @returns {any} Environment variable value or default
 */
const getEnvVar = (key, defaultValue = null) => {
  const value = import.meta.env[key]
  if (value === undefined || value === null) {
    return defaultValue
  }
  
  // Convert string booleans to actual booleans
  if (value === 'true') return true
  if (value === 'false') return false
  
  // Convert string numbers to actual numbers
  if (!isNaN(value) && !isNaN(parseFloat(value))) {
    return parseFloat(value)
  }
  
  return value
}

/**
 * Environment configuration object
 */
export const envConfig = {
  // Application Configuration
  app: {
    title: getEnvVar('VITE_APP_TITLE', 'Community Services Admin'),
    version: getEnvVar('VITE_APP_VERSION', '1.0.0'),
    env: getEnvVar('VITE_APP_ENV', 'development'),
    buildTimestamp: getEnvVar('VITE_BUILD_TIMESTAMP', null),
    buildVersion: getEnvVar('VITE_BUILD_VERSION', null),
    gitCommit: getEnvVar('VITE_GIT_COMMIT', null)
  },

  // API Configuration
  api: {
    baseURL: getEnvVar('VITE_API_BASE_URL', '/api/v1'),
    timeout: getEnvVar('VITE_API_TIMEOUT', 10000),
    retryAttempts: getEnvVar('VITE_RETRY_ATTEMPTS', 3),
    authApiUrl: getEnvVar('VITE_AUTH_API_URL', null),
    analyticsApiUrl: getEnvVar('VITE_ANALYTICS_API_URL', null)
  },

  // Mock API Configuration
  mock: {
    enabled: getEnvVar('VITE_MOCK_API', false),
    delay: getEnvVar('VITE_MOCK_DELAY', 500),
    delayVariance: getEnvVar('VITE_MOCK_DELAY_VARIANCE', 100)
  },

  // Debug Configuration
  debug: {
    enabled: getEnvVar('VITE_DEBUG', false),
    logLevel: getEnvVar('VITE_LOG_LEVEL', 'info')
  },

  // Feature Flags
  features: {
    analytics: getEnvVar('VITE_FEATURE_ANALYTICS', true),
    competitions: getEnvVar('VITE_FEATURE_COMPETITIONS', true),
    credits: getEnvVar('VITE_FEATURE_CREDITS', true),
    schools: getEnvVar('VITE_FEATURE_SCHOOLS', true)
  },

  // Security Configuration
  security: {
    secureCookies: getEnvVar('VITE_SECURE_COOKIES', false),
    csrfProtection: getEnvVar('VITE_CSRF_PROTECTION', false),
    httpsOnly: getEnvVar('VITE_HTTPS_ONLY', false)
  },

  // Analytics Configuration
  analytics: {
    enabled: getEnvVar('VITE_ENABLE_ANALYTICS', false),
    debug: getEnvVar('VITE_ANALYTICS_DEBUG', false)
  },

  // Error Reporting Configuration
  errorReporting: {
    enabled: getEnvVar('VITE_ERROR_REPORTING', false),
    url: getEnvVar('VITE_ERROR_REPORTING_URL', null)
  },

  // Cache Configuration
  cache: {
    enabled: getEnvVar('VITE_CACHE_ENABLED', true),
    ttl: getEnvVar('VITE_CACHE_TTL', 300000) // 5 minutes default
  },

  // Test Configuration
  test: {
    mode: getEnvVar('VITE_TEST_MODE', false),
    skipAuth: getEnvVar('VITE_SKIP_AUTH', false)
  }
}

/**
 * Check if running in development mode
 * @returns {boolean}
 */
export const isDevelopment = () => {
  return envConfig.app.env === 'development' || envConfig.app.env === 'local'
}

/**
 * Check if running in production mode
 * @returns {boolean}
 */
export const isProduction = () => {
  return envConfig.app.env === 'production'
}

/**
 * Check if running in staging mode
 * @returns {boolean}
 */
export const isStaging = () => {
  return envConfig.app.env === 'staging'
}

/**
 * Check if running in test mode
 * @returns {boolean}
 */
export const isTest = () => {
  return envConfig.app.env === 'test' || envConfig.test.mode
}

/**
 * Get API base URL with environment-specific handling
 * @returns {string}
 */
export const getApiBaseUrl = () => {
  // Use specific API URLs if provided
  if (envConfig.api.authApiUrl && window.location.pathname.includes('/auth')) {
    return envConfig.api.authApiUrl
  }
  
  if (envConfig.api.analyticsApiUrl && window.location.pathname.includes('/analytics')) {
    return envConfig.api.analyticsApiUrl
  }
  
  return envConfig.api.baseURL
}

/**
 * Get feature flag status
 * @param {string} feature - Feature name
 * @returns {boolean}
 */
export const isFeatureEnabled = (feature) => {
  return envConfig.features[feature] || false
}

/**
 * Log environment configuration (development only)
 */
export const logEnvironmentConfig = () => {
  if (isDevelopment() && envConfig.debug.enabled) {
    console.group('🔧 Environment Configuration')
    console.log('Environment:', envConfig.app.env)
    console.log('API Base URL:', envConfig.api.baseURL)
    console.log('Mock API:', envConfig.mock.enabled ? 'Enabled' : 'Disabled')
    console.log('Debug Mode:', envConfig.debug.enabled ? 'Enabled' : 'Disabled')
    console.log('Features:', envConfig.features)
    console.groupEnd()
  }
}

// Export default configuration
export default envConfig
