WITH UnCredits AS (
    SELECT
        u.university,
        SUM(u.credit) AS total_credits
    FROM
        qualified_users_triggered u
    WHERE
        u.university IS NOT NULL AND u.university != ''
    GROUP BY
        u.university
),
RankedUn AS (
    SELECT
        uc.*,
        ROW_NUMBER() OVER (ORDER BY uc.total_credits DESC, uc.university ASC) AS row_num -- Added ASC to university for tie-breaking
    FROM
        UnCredits uc
),
TotalCount AS (
    SELECT COUNT(*) AS total_count FROM RankedUn
)
SELECT
    ru.university,
    ru.total_credits,
    tc.total_count
FROM
    RankedUn ru,
    TotalCount tc -- Implicit JOIN, consider explicit JOIN if preferred
ORDER BY
    ru.row_num ASC
LIMIT {{page_size}} OFFSET {{offset}}; -- Updated placeholder syntax 