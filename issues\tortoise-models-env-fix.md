# TORTOISE_MODELS Environment Variable Fix

**Date**: 2025-01-17  
**Issue**: JSON parsing error for `TORTOISE_MODELS` environment variable  
**Status**: ✅ RESOLVED

## Problem Description

The application was failing to start with the following error:
```
pydantic_settings.exceptions.SettingsError: error parsing value for field "tortoise_models" from source "DotEnvSettingsSource"
```

### Root Cause Analysis

1. **Invalid JSON Format**: The `.env` file contains `TORTOISE_MODELS=app.db.models.base_models` (single string)
2. **Pydantic Expectation**: Pydantic expects List fields from environment variables to be in JSON format
3. **Field Definition**: The `tortoise_models` field was defined as `List[str]` which triggered automatic JSON parsing

## Solution Implemented

**Fixed directly in `core/config/settings.py`:**

1. **Separate Raw Field**: Added `tortoise_models_raw: Optional[str]` field to capture the raw environment variable
2. **Model Validator**: Added `parse_tortoise_models()` method that handles both:
   - Comma-separated strings: `app.db.models.base_models`
   - JSON arrays: `["app.db.models.base_models", "shared.models"]`
3. **Backward Compatibility**: Supports both formats automatically

### Code Changes

```python
# Before (caused JSON parsing error)
tortoise_models: List[str] = Field(default=["shared.models", "aerich.models"], env="TORTOISE_MODELS")

# After (handles both formats)
tortoise_models_raw: Optional[str] = Field(None, env="TORTOISE_MODELS", exclude=True)
tortoise_models: List[str] = Field(default=["shared.models", "aerich.models"])

@model_validator(mode="after")
def parse_tortoise_models(self):
    """Parse TORTOISE_MODELS from string to list."""
    if self.tortoise_models_raw:
        raw_value = self.tortoise_models_raw.strip()
        if raw_value.startswith('[') and raw_value.endswith(']'):
            # JSON array format
            self.tortoise_models = json.loads(raw_value)
        else:
            # Comma-separated string format
            self.tortoise_models = [model.strip() for model in raw_value.split(",") if model.strip()]
    return self
```

## Verification

✅ **Settings Load Successfully**: `python -c "from core.config.settings import settings; print('OK')"`  
✅ **FastAPI App Creation**: `python -c "from gateway.api.main import create_app; app = create_app(); print('OK')"`  
✅ **Backward Compatibility**: Works with existing `.env` file format  
✅ **Forward Compatibility**: Also supports JSON array format  

## Technical Details

- **Field Type**: `tortoise_models_raw` captures raw string, `tortoise_models` contains parsed list
- **Parsing Logic**: Automatically detects format (JSON vs comma-separated)
- **Error Handling**: Graceful fallback from JSON to comma-separated parsing
- **Default Value**: `["shared.models", "aerich.models"]` when no environment variable is set

## Files Modified

- `core/config/settings.py` - Fixed field definition and added parsing logic

## Status

**RESOLVED** - No further action required. The application now starts successfully with the existing `.env` file configuration. 