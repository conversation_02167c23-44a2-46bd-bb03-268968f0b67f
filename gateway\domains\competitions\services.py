"""
Competition business logic and data access services.

Contains all business logic for competitions including data access,
validation, and integration with external services.
"""

from typing import List, Dict, Any, Optional
from datetime import datetime, timed<PERSON><PERSON>
from bson import ObjectId

from core.config import logger
from core.database.mongo_db import mongo_manager
from gateway.domains.competitions.constants import (
    COMPETITION_REGISTER_PROFILE_BASIC, 
    COMPETITION_REGISTER_PROFILE_DETAIL, 
    COMPETITION_REGISTER_PROFILE_FULL_FORM
)
from gateway.domains.competitions.schemas import (
    CompetitionUserProfileResponse
)
from libs.exceptions import ServiceException, BusinessLogicError, MongoDBError  
from libs.errors import CompetitionDomainErrors
from libs.schemas.api.base import PaginationConfig

def resolve_profile_projection(profile_type: str) -> Dict[str, Any]:
    """Resolve profile projection based on profile type."""
    if profile_type == "basic":
        return COMPETITION_REGISTER_PROFILE_BASIC
    elif profile_type == "detail":
        return COMPETITION_REGISTER_PROFILE_DETAIL
    elif profile_type == "full_form":
        return COMPETITION_REGISTER_PROFILE_FULL_FORM
    else:
        raise ServiceException(
            code=400,
            errmsg=CompetitionDomainErrors.invalid_profile_type(profile_type),
            data={"profile_type": profile_type}
        )

class CompetitionServices:
    """Business logic services for competition operations."""

    def __init__(self):
        """Initialize competition services."""
        self.mongo_db = mongo_manager
        
    async def get_competition_users_by_type(
        self, competition_type: str,
        register_start_date: Optional[str] = None, 
        database=None, 
        profile_type: str = "basic",
        limit: Optional[int] = PaginationConfig.DEFAULT_LIMIT,
        offset: Optional[int] = PaginationConfig.DEFAULT_OFFSET,
    ) -> CompetitionUserProfileResponse:
        """Get competition users filtered by competition type.
        
        根据比赛或活动的类型筛选报名用户。默认返回一年内的报名用户
        """
        if limit is None:
            limit = PaginationConfig.DEFAULT_LIMIT
        if offset is None:
            offset = PaginationConfig.DEFAULT_OFFSET
        
        
        if database is None:
            raise ServiceException(
                code=400,
                errmsg=CompetitionDomainErrors.database_error("Database Dependency Error"),
                data={"database": database, "collection": "registerinfos_v2"}
            )
        collection = database["registerinfos_v2"]
        register_start_date:str = register_start_date or \
            (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d %H:%M:%S")
        start_object_id = ObjectId.from_datetime(datetime.strptime(register_start_date, "%Y-%m-%d %H:%M:%S"))
        pipeline = [
            {
                "$project": {
                    "_id": 1,
                    "User": 1,
                    "Competition": 1,
                    "Phone": 1,
                    "RegisterForm": 1,
                    "register_ts": {"$dateToString": {"date": "$Date", "format": "%Y-%m-%d %H:%M:%S"}},
                }
            },
            {"$match": {"_id": {"$gte": ObjectId(start_object_id)}}},
            {
                "$lookup": {
                    "from": "competitions_v2",
                    "localField": "Competition",
                    "foreignField": "_id",
                    "as": "comp_info",
                }
            },
            {
                "$unwind": {
                    "path": "$comp_info",
                    "preserveNullAndEmptyArrays": False,
                }
            },
            {
                "$match": {"comp_info.DetailType": competition_type},
            },
            {"$project": resolve_profile_projection(profile_type)},
        ]
        if limit != PaginationConfig.NO_LIMIT:
            pipeline.append({"$limit": limit})
        if offset != PaginationConfig.NO_OFFSET:
            pipeline.append({"$skip": offset})
        
        try:
            cursor = collection.aggregate(pipeline)
            response:List[Dict[str, Any]] = await cursor.to_list(length=None)
            return CompetitionUserProfileResponse(data=response, 
                                                  total=len(response),
                                                  limit=limit,
                                                  offset=offset)
        except Exception as e:
            raise MongoDBError(
                operation="get_competition_users_by_type",
                message=CompetitionDomainErrors.database_error(e),
                details={"database": database, "collection": "registerinfos_v2"},
                collection="registerinfos_v2",
            )

    async def get_competition_users_by_identity(
        self, identity: str,
        register_start_date: Optional[str] = None,
        database=None,
        profile_type: str = "basic",
        limit: Optional[int] = PaginationConfig.NO_LIMIT,
        offset: Optional[int] = PaginationConfig.NO_OFFSET,
    ) -> CompetitionUserProfileResponse:
        """Get competition users filtered by identity.
        
        根据身份筛选报名用户。默认返回一年内的报名用户
        """
        if limit is None:
            limit = PaginationConfig.NO_LIMIT
        if offset is None:
            offset = PaginationConfig.NO_OFFSET
        
        collection = database["registerinfos_v2"]

        if not register_start_date:
            register_start_date:str = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d %H:%M:%S")

        start_object_id = ObjectId.from_datetime(datetime.strptime(register_start_date, "%Y-%m-%d %H:%M:%S"))
        
        projection = resolve_profile_projection(profile_type)

        pipeline = [
            {
                "$match": {
                    "RegisterForm": {"$exists": True},
                    "_id": {"$gte": ObjectId(start_object_id)},
                }
            },
            {
                "$lookup": {
                    "from": "competitions_v2",
                    "localField": "Competition",
                    "foreignField": "_id",
                    "as": "comp_info",
                }
            },
            {"$unwind": {"path": "$comp_info", "preserveNullAndEmptyArrays": False}},
            {
                "$addFields": {
                    "matchedElements": {
                        "$filter": {
                            "input": "$RegisterForm",
                            "as": "item",
                            "cond": {
                                "$and": [
                                    {"$eq": ["$$item.Name", "身份"]},
                                    {"$eq": ["$$item.Value", identity]},
                                ]
                            },
                        }
                    }
                }
            },
            {"$match": {"matchedElements.0": {"$exists": True}}},
            {"$project": {"matchedElements": 0}},
            {"$project":{**projection,"register_ts": {"$dateToString": {"date": "$Date", "format": "%Y-%m-%d %H:%M:%S"}},
            }},
        ]
        if limit != PaginationConfig.NO_LIMIT:
            pipeline.append({"$limit": limit})
        if offset != PaginationConfig.NO_OFFSET:
            pipeline.append({"$skip": offset})
        print(pipeline, flush=True)
        try:
            cursor = collection.aggregate(pipeline)
            response:List[Dict[str, Any]] = await cursor.to_list(length=None)
            # print(response[0],response[1], flush=True)
            return CompetitionUserProfileResponse(data=response, 
                                                  total=len(response),
                                                  limit=limit,
                                                  offset=offset)
        except Exception as e:
            raise MongoDBError(
                operation="get_competition_users_by_identity",
                message=CompetitionDomainErrors.database_error(e),
                details={"database": database, "collection": "registerinfos_v2"},
                collection="registerinfos_v2",
            )   

# Global services instance
competition_service = CompetitionServices()
