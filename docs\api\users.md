# Users API Documentation

## Overview

The Users API provides endpoints for user management, profile operations, user discovery, and HeyWhale platform integration. All endpoints require authentication and are prefixed with `/api/v1/users`.

## Authentication

All endpoints require authentication via the `get_current_user_id` dependency. Additional permission checks are applied per endpoint.

## Endpoints

### User Profile Management

#### Get User Profile
```http
GET /api/v1/users/{user_id}/profile
```

**Description**: Get detailed user information with different profile types.

**Parameters**:
- `user_id` (path, required): User ID
- `profile_type` (query, optional): Profile type - `basic`, `dynamic`, `student`, `rich` (default: `basic`)

**Profile Types**:
- `basic`: Basic user information
- `dynamic`: Basic + dynamic information (last login location/time)
- `student`: Basic + student information (university, major, grade)
- `rich`: Basic + student + dynamic information + tags

**Response**: `SuccessResponse` with user profile data

**Permissions**: Requires `user_read_permission`

**Caching**: 10 minutes TTL

---

### User Discovery

#### Discover Users by Criteria
```http
POST /api/v1/users/discovery
```

**Description**: Discover users based on registration time, location, university, major, identity, and experience criteria.

**Parameters**:
- `profile_type` (query, optional): Profile type (default: `basic`)
- `days` (query, optional): Number of days to search (1-365, default: 365)
- `limit` (query, optional): Number of users to return (max: 1000)
- `offset` (query, optional): Number of users to skip (default: 0)

**Request Body**: `MatchStageSpec` - MongoDB aggregation match specification

**Response**: `SuccessResponse` with `UserProfileListResponse`

**Permissions**: Requires `user_discovery_permission`

**Caching**: 3 minutes TTL

---

### User Search

#### Search Users by Identifier
```http
GET /api/v1/users/search
```

**Description**: Search users by name, email, phone, or user ID.

**Parameters**:
- `queries` (query, required): List of search terms
- `search_type` (query, optional): Search type - `name`, `email`, `phone`, `user_id` (default: `name`)
- `profile_type` (query, optional): Profile type (default: `basic`)
- `limit` (query, optional): Number of users to return (max: 1000)
- `offset` (query, optional): Number of users to skip (default: 0)

**Response**: `SuccessResponse` with user profile list

**Caching**: 3 minutes TTL

---

### HeyWhale Integration

#### Get HeyWhale Tags Tree
```http
GET /api/v1/users/integrations/heywhale/tags/tree
```

**Description**: Get HeyWhale tag tree for user management.

**Response**: `SuccessResponse` with tag tree data

**Permissions**: Requires `heywhale_access_permission`

**Caching**: 1 hour TTL

#### Sync User Badges to HeyWhale
```http
POST /api/v1/users/integrations/heywhale/badges/sync
```

**Description**: Synchronize user badges with HeyWhale platform.

**Request Body**: `BadgeSyncBatchRequest`
```json
{
  "users": [
    {
      "user_id": "string",
      "badges": ["badge1", "badge2"]
    }
  ]
}
```

**Response**: `SuccessResponse` with sync results

**Permissions**: Requires `heywhale_access_permission`

#### Update User Tags in HeyWhale
```http
POST /api/v1/users/integrations/heywhale/tags/update
```

**Description**: Update user tags in HeyWhale platform.

**Request Body**: `PostTagRequest`
```json
{
  "users": [
    {
      "user_id": "string",
      "tags": ["tag1", "tag2"]
    }
  ]
}
```

**Response**: `SuccessResponse` with update results

**Permissions**: Requires `heywhale_access_permission`

**Cache Invalidation**: Invalidates tags cache after update

---

### Cache Management

#### Get Cache Statistics
```http
GET /api/v1/users/cache/stats
```

**Description**: Get cache statistics for users domain.

**Response**: `SuccessResponse` with cache statistics

**Permissions**: Requires `user_read_permission`

#### Clear Cache
```http
DELETE /api/v1/users/cache/clear
```

**Description**: Clear cache for users domain.

**Parameters**:
- `operation` (query, optional): Specific operation to clear

**Response**: `SuccessResponse` with clear results

**Permissions**: Requires `user_read_permission`

#### Check Cache Health
```http
GET /api/v1/users/cache/health
```

**Description**: Check cache system health.

**Response**: `SuccessResponse` with health status

**Permissions**: Requires `user_read_permission`

## Data Models

### User Profile Types

#### UserBasicProfile
```json
{
  "user_id": "string",
  "name": "string",
  "email": "string", 
  "phone": "string",
  "identity": "string",
  "join_date": "2023-01-01T00:00:00Z"
}
```

#### UserDynamicProfile (extends UserBasicProfile)
```json
{
  "last_login_location": "string",
  "last_login_time": "2023-01-01T00:00:00Z"
}
```

#### UserStudentProfile (extends UserBasicProfile)
```json
{
  "university": "string",
  "major": "string",
  "grade": "string",
  "degree": "string"
}
```

#### UserRichProfile (extends UserDynamicProfile)
```json
{
  "experience": "string",
  "tags": ["tag1", "tag2"],
  "organization": "string",
  "position": "string",
  "field": "string"
}
```

## Error Responses

All endpoints may return standard error responses:
- `400 Bad Request`: Invalid parameters
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: User not found
- `500 Internal Server Error`: Server error
