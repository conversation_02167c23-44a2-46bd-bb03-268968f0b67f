# -*- coding:utf-8 -*-
from pydantic import BaseModel, Field
from typing import List, Any, Optional, Dict
from enum import Enum




class BaseResp(BaseModel):
    code: int = Field(
        default=200, description="状态码"
    )  # Added default for success cases
    msg: str = Field(default="", description="信息")  # Added default for success cases
    data: Any = Field(
        default=None, description="数据"
    )  # Changed from List to Any, default None


class ResAntTable(BaseModel):
    """Response schema for Ant Design table components."""
    success: bool = Field(description="状态码")
    data: List = Field(description="数据")
    total: int = Field(description="总条数")

class WebsocketMessage(BaseModel):
    action: Optional[str]
    user: Optional[int]
    data: Optional[Any]

class PaginationConfig(int, Enum):
    DEFAULT_LIMIT = 20
    DEFAULT_OFFSET = 0
    NO_LIMIT = -1
    NO_OFFSET = -1
    
class BaseDataListResponse(BaseModel):
    """
    基础数据列表响应, 用于分页查询
    """
    data: List[Any] = Field(default=None, description="数据")
    total: Optional[int] = Field(None, description="总条数")
    limit: Optional[int] = Field(None, description="每页条数")
    offset: Optional[int] = Field(None, description="偏移量")

class BaseDataPostResponse(BaseModel):
    """
    消息响应, 用于响应创建，更新，删除的消息
    
    id:str = Field(..., description="数据 ID")
    success:bool = Field(..., description="是否成功")
    message:str | Dict[str,Any] = Field(..., description="消息")
    
    """
    id: str = Field(..., description="数据 ID")
    success: bool = Field(..., description="是否成功")
    message: str | Dict[str,Any] = Field(..., description="消息")
    data: Optional[Any] = Field(None, description="数据")
    
    class Config:
        extra = "allow"
    
class ExternalServiceResponse(BaseModel):
    """
    外部服务响应数据范式
    
    data: Optional[Any] = Field(None, description="数据")
    message: Optional[str] = Field(None, description="消息")   
    success: Optional[bool] = Field(None, description="是否成功")
    """
    data: Optional[Any] = Field(None, description="数据")
    message: Optional[str] = Field(None, description="消息")   
    success: Optional[bool] = Field(None, description="是否成功")


