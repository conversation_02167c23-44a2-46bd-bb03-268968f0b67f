"""
Shence Integration - External API calls with graceful error handling.

<PERSON><PERSON> (Sensors Data) analytics platform integration with retry logic.
"""

from typing import Dict, Any, Optional
import logging
from datetime import datetime

from gateway.integrations.base import BaseIntegration, HTTPResponse, RetryConfig
from libs.auth.context import AuthContext
from libs.errors import IntegrationErrors
from libs.exceptions import ExternalServiceError

logger = logging.getLogger(__name__)


class ShenceIntegration(BaseIntegration):
    """Integration service for Shence (Sensors Data) analytics platform."""

    def __init__(self):
        """Initialize Shence integration service."""
        super().__init__()
        # self._base_url = self._config.shence_url
        # self._project_name = self._config.shence_project_name
        # self._secret_id = self._config.shence_secret_id
        # self._secret_key = self._config.shence_secret_key
        # self._token = self._config.shence_token

    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for Shence API."""
        headers = {}

        if self._token:
            headers["Authorization"] = f"Bearer {self._token}"

        if self._config.external_services.shence_cookie:
            headers["Cookie"] = self._config.external_services.shence_cookie

        if self._config.external_services.shence_xsrf_token:
            headers["X-XSRF-TOKEN"] = self._config.external_services.shence_xsrf_token

        return headers

    async def track_event(
        self,
        user_id: str,
        event_name: str,
        auth_context: AuthContext,
        properties: Optional[Dict[str, Any]] = None,
    ) -> HTTPResponse:
        """
        Track user event to Shence analytics platform.

        Args:
            user_id: User identifier
            event_name: Name of the event to track
            auth_context: Authentication context
            properties: Optional event properties

        Returns:
            IntegrationResult indicating success
        """
        try:
            if not self._base_url:
                raise ExternalServiceError(
                    IntegrationErrors.HTTP_CLIENT_ERROR,
                    "Shence URL not configured",
                    "integration",
                    {"service": "shence", "operation": "track_event"},
                )

            # Prepare event payload
            event_payload = {
                "project": self._project_name,
                "distinct_id": user_id,
                "event": event_name,
                "properties": properties or {},
                "time": int(datetime.now().timestamp() * 1000),  # Milliseconds
                "type": "track",
            }

            # Add default properties
            event_payload["properties"].update(
                {
                    "source": "community-services",
                    "user_id": user_id,
                    "tracked_by": auth_context.user_id,
                }
            )

            # Make API request
            url = f"{self._base_url}/sa"
            headers = self._get_auth_headers()

            retry_config = RetryConfig(
                max_attempts=3, base_delay=1.0, retry_on_status=[429, 502, 503, 504]
            )

            result = await self._post_request(
                url=url,
                auth_context=auth_context,
                data=event_payload,
                headers=headers,
                timeout=10,
                retry_config=retry_config,
            )

            if not result.is_success:
                raise ExternalServiceError(
                    IntegrationErrors.HTTP_CLIENT_ERROR,
                    f"Failed to track event to Shence: {result.error.message}",
                    "integration",
                    {
                        "service": "shence",
                        "operation": "track_event",
                        "event_name": event_name,
                        "user_id": user_id,
                    },
                )
            return result

            response = result.data

            if not response.is_success:
                return self._handle_response_error(response, "Track event to Shence")

            logger.info(
                "Successfully tracked event to Shence",
                event_name=event_name,
                user_id=user_id,
                tracked_by=auth_context.user_id,
            )

        except Exception as e:
            logger.error(f"Failed to track event to Shence: {e}")
            raise e

    async def identify_user(
        self, user_id: str, user_properties: Dict[str, Any], auth_context: AuthContext
    ) -> HTTPResponse:
        """
        Identify user in Shence system with profile data.

        Args:
            user_id: User identifier
            user_properties: User profile properties
            auth_context: Authentication context

        Returns:
            IntegrationResult indicating success
        """
        try:
            if not self._base_url:
                raise ExternalServiceError(
                    IntegrationErrors.HTTP_CLIENT_ERROR,
                    "Shence URL not configured",
                    "integration",
                    {"service": "shence", "operation": "identify_user"},
                )

            # Prepare identify payload
            identify_payload = {
                "project": self._project_name,
                "distinct_id": user_id,
                "properties": user_properties.copy(),
                "time": int(datetime.now().timestamp() * 1000),
                "type": "profile_set",
            }

            # Add metadata
            identify_payload["properties"].update(
                {
                    "source": "community-services",
                    "identified_by": auth_context.user_id,
                    "last_updated": datetime.now().isoformat(),
                }
            )

            # Make API request
            url = f"{self._base_url}/sa"
            headers = self._get_auth_headers()

            retry_config = RetryConfig(
                max_attempts=3, base_delay=1.0, retry_on_status=[429, 502, 503, 504]
            )

            result = await self._post_request(
                url=url,
                auth_context=auth_context,
                data=identify_payload,
                headers=headers,
                timeout=10,
                retry_config=retry_config,
            )

            if not result.is_success:
                raise ExternalServiceError(
                    IntegrationErrors.HTTP_CLIENT_ERROR,
                    f"Failed to identify user in Shence: {result.error.message}",
                    "integration",
                    {
                        "service": "shence",
                        "operation": "identify_user",
                        "user_id": user_id,
                    },
                )

            response = result.data

            if not response.is_success:
                return self._handle_response_error(response, "Identify user in Shence")

            logger.info(
                "Successfully identified user in Shence",
                user_id=user_id,
                identified_by=auth_context.user_id,
            )

            return result

        except Exception as e:
            logger.error(f"Failed to identify user in Shence: {e}")
            raise e

    async def track_page_view(
        self,
        user_id: str,
        page_url: str,
        auth_context: AuthContext,
        properties: Optional[Dict[str, Any]] = None,
    ) -> HTTPResponse:
        """
        Track page view to Shence analytics.

        Args:
            user_id: User identifier
            page_url: URL of the page viewed
            auth_context: Authentication context
            properties: Optional additional properties

        Returns:
            IntegrationResult indicating success
        """
        try:
            # Use track_event with page view specific properties
            page_properties = {
                "page_url": page_url,
                "page_type": "page_view",
                **(properties or {}),
            }

            return await self.track_event(
                user_id=user_id,
                event_name="page_view",
                auth_context=auth_context,
                properties=page_properties,
            )

        except Exception as e:
            logger.error(f"Failed to track page view to Shence: {e}")
            raise e

    async def sync_tags(
        self, tag_data: Dict[str, Any], auth_context: AuthContext
    ) -> HTTPResponse:
        """
        Sync tags with Shence platform.

        Args:
            tag_data: Tag synchronization data
            auth_context: Authentication context

        Returns:
            IntegrationResult containing sync results
        """
        try:
            if not self._base_url:
                raise ExternalServiceError(
                    IntegrationErrors.HTTP_CLIENT_ERROR,
                    "Shence URL not configured",
                    "integration",
                    {"service": "shence", "operation": "sync_tags"},
                )

            # Prepare sync payload
            sync_payload = {
                "project": self._project_name,
                "data": tag_data,
                "sync_type": "tags",
                "timestamp": datetime.now().isoformat(),
                "synced_by": auth_context.user_id,
            }

            # Make API request
            url = f"{self._base_url}/api/tags/sync"
            headers = self._get_auth_headers()

            retry_config = RetryConfig(
                max_attempts=2,  # Fewer retries for sync operations
                base_delay=2.0,
                retry_on_status=[429, 502, 503, 504],
            )

            result = await self._post_request(
                url=url,
                auth_context=auth_context,
                data=sync_payload,
                headers=headers,
                timeout=30,  # Longer timeout for sync
                retry_config=retry_config,
            )

            if not result.is_success:
                raise ExternalServiceError(
                    IntegrationErrors.HTTP_CLIENT_ERROR,
                    f"Failed to sync tags with Shence: {result.error.message}",
                    "integration",
                    {"service": "shence", "operation": "sync_tags"},
                )

            response = result.data

            if not response.is_success:
                return self._handle_response_error(response, "Sync tags with Shence")

            sync_results = {
                "status": "success",
                "synced_at": datetime.now().isoformat(),
                "response_data": response.data,
            }

            logger.info(
                "Successfully synced tags with Shence", synced_by=auth_context.user_id
            )

            return result

        except Exception as e:
            logger.error(f"Failed to sync tags with Shence: {e}")
            raise e

    async def health_check(self) -> HTTPResponse:
        """Health check for Shence integration service."""
        try:
            if not self._base_url:
                raise ExternalServiceError(
                    IntegrationErrors.HTTP_CLIENT_ERROR,
                    "Shence URL not configured",
                    "integration",
                    {"service": "shence"},
                )

            # Test basic connectivity
            from libs.auth.context import create_auth_context

            test_auth = create_auth_context("health_check")

            url = f"{self._base_url}/health"
            headers = self._get_auth_headers()

            result = await self._get_request(
                url=url, auth_context=test_auth, headers=headers, timeout=5
            )

            if result.is_success and result.data.is_success:
                return result
            else:
                raise ExternalServiceError(
                    IntegrationErrors.HTTP_CLIENT_ERROR,
                    "Shence health check failed",
                    "integration",
                    {
                        "service": "shence",
                        "base_url": self._base_url,
                        "error": result.error.message
                        if not result.is_success
                        else "HTTP error",
                    }
                )

        except Exception as e:
            raise e
