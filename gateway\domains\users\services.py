"""
User business logic and data access services.

Contains all business logic for users including data access,
validation, and integration with external services.
"""

import re
from typing import List, Dict, Any, Optional, Literal
from datetime import datetime, timedelta
from bson import ObjectId
from pydantic import ValidationError

from core.config import logger
from core.database.mongo_db import mongo_manager
from gateway.domains.users.schemas import (
    UserProfileListResponse, 
    )
from gateway.integrations.heywhale import HeyWhaleIntegrationService
from gateway.integrations.schemas import (
    TagType,
    PostTagRequest,
    BadgeSyncResponse,
    PostTagResponse,
    GetTagListResponse,
    BadgeSyncBatchRequest
)
from libs.exceptions.handlers import MongoDBError,ExternalServiceError
from libs.schemas.api.base import (
    PaginationConfig,
    BaseDataPostResponse,
)
from .constants import (
    USER_PROFILE_PROJECTION_BASIC, 
    USER_PROFILE_PROJECTION_DYNAMIC, 
    USER_PROFILE_PROJECTION_RICH,
    USER_PROFILE_PROJECTION_STUDENT
)
from libs.mongo.builder import MongoQueryBuilder
from libs.mongo.schemas import (
    MatchStageSpec,
)
from libs.mongo.snippets import convert_utc_to_local

# Import custom exceptions and error message factories
from libs.exceptions import  ServiceException
from libs.errors import UserDomainErrors, create_error_data


def resolve_profile_projection(profile_type: str) -> Dict[str, Any]:
    """Resolve profile projection based on profile type."""
    if profile_type == "basic":
        return USER_PROFILE_PROJECTION_BASIC
    elif profile_type == "dynamic":
        return USER_PROFILE_PROJECTION_DYNAMIC
    elif profile_type == "rich":
        return USER_PROFILE_PROJECTION_RICH
    elif profile_type == "student":
        return USER_PROFILE_PROJECTION_STUDENT
    else:
        raise ServiceException(
            rule="invalid_profile_type",
            message=UserDomainErrors.invalid_profile_type(profile_type),
            context={"valid_types": ["basic", "dynamic", "rich", "student"]}
        )


class UserServices:
    """Business logic services for user operations."""

    def __init__(self):
        """Initialize user services."""
        self.mongo_db = mongo_manager

    async def get_user_profile(self, user_id: str,*args, **kwargs) -> UserProfileListResponse:  
        
        """Get user profile information.
        
        获取用户的资料，根据profile_type，获取最终的投影  
        
        """
        if not user_id or not user_id.strip():
            raise ServiceException(
                code=400,
                errmsg=UserDomainErrors.user_id_required(),
                data={"field": "user_id"}
            )
        
        if kwargs.get("collection") is None:
            raise ServiceException(
                code=500,
                errmsg="缺少合集字段",
                data={"missing": "collection"}
            )
    
        try:
            collection = kwargs.get("collection")
            profile_type = kwargs.get("profile_type") or "basic"

            projection = resolve_profile_projection(profile_type)
        except ServiceException as e:
            raise e
    
        
        try:
            # 查询用户的基础信息
            pipeline = [
                {"$match": {"_id": ObjectId(user_id)}},
                {
                    "$project": projection
                },
            ]

            cursor = collection.aggregate(pipeline)
            users = await cursor.to_list(length=None)

            if not users:
                raise ServiceException(
                    code=404,
                    errmsg=UserDomainErrors.user_not_found(user_id),
                    data=create_error_data("users", "not_found", user_id=user_id)
                )
            
            logger.info(f"Retrieved user profile for user_id: {user_id}")
            return UserProfileListResponse(data=users)

        except Exception as e:
            logger.error(f"Failed to get user profile: {e}")
            raise MongoDBError(
                operation="get_user_profile",
                message="获取用户资料失败",
                collection=collection,
                details={"error": str(e), "user_id": user_id}
            )

    async def search_users_by_identifier(
        self,
        queries: List[str],
        search_type: Literal["name", "email", "phone", "user_id"] = "name",
        limit: Optional[PaginationConfig | int] = PaginationConfig.NO_LIMIT,
        offset: Optional[PaginationConfig | int] = PaginationConfig.NO_OFFSET,
        *args, **kwargs
    ) -> UserProfileListResponse:
        """Search for users by identifier.
        
        根据用户名，邮箱，手机号，user_id，搜索用户
        Args:
            query: 搜索关键词列表
            search_type: 搜索类型
            limit: 每页数量
            offset: 偏移量
        """
        if not queries:
            raise ServiceException(
                rule="invalid_queries",
                message="搜索查询不能为空",
                context={"field": "queries"}
            )
        if kwargs.get("collection") is None:
            raise ServiceException(
                rule="missing_collection",
                message="缺少合集字段",
                context={"field": "collection"}
            )
        if search_type not in ["name", "email", "phone", "user_id"]:
            raise ServiceException(
                rule="invalid_search_type",
                message=f"无效的搜索类型: {search_type}",
                context={"valid_types": ["name", "email", "phone", "user_id"]}
            )


        collection = kwargs.get("collection")
        profile_type = kwargs.get("profile_type") or "basic"

        # 根据profile_type，获取最终的投影  
        projection = resolve_profile_projection(profile_type)

        # Build search pipeline based on search type
        collection = kwargs.get("collection")

        # Build filter based on identifier type
        if search_type == "phone":
            filter_dict = {"Phone": {"$in": queries}}
        elif search_type == "email":
            filter_dict = {"Email": {"$in": queries}}
        elif search_type == "user_id":
            try:
                filter_dict = {"_id": {"$in": [ObjectId(query) for query in queries]}}
            except Exception:
                raise ServiceException(
                    code=400,
                    errmsg="无效的用户ID格式",
                    data={"queries": queries}
                )
        else:
            filter_dict = {"Name": {"$in": queries}} 

        if limit >0 and offset >=0:
            pipeline = [
                {"$match": filter_dict},
                {"$project": projection},
                {"$skip": offset},
                {"$limit":limit},
            ]
        elif limit == -1 and offset == -1:   
            pipeline = [
                {"$match": filter_dict},
                {"$project": projection},
            ]   
        else:
            raise ServiceException(
                code=400,
                errmsg="无效的 limit 或 offset",
                data={"limit": limit, "offset": offset}
            )

        try:
            cursor = collection.aggregate(pipeline)
            users = await cursor.to_list(length=None)
            logger.info(f"Found {len(users)} users matching query: {queries}")
            return UserProfileListResponse(data=users)

        except Exception as e:
            logger.error(f"Failed to search users: {e}")
            raise MongoDBError(
                operation="search_users_by_identifier",
                message=f"根据 {search_type} 搜索用户失败",
                collection=collection,
                details={"error": str(e), "queries": queries}
            )

    async def search_users_by_query(
        self,
        spec: MatchStageSpec | Dict[str, Any] = {},
        limit: Optional[PaginationConfig | int] = PaginationConfig.NO_LIMIT,
        offset: Optional[PaginationConfig | int] = PaginationConfig.NO_OFFSET,
        days: Optional[int] = 365,
        profile_type: Optional[str] = "basic",
        *args, **kwargs
    ) -> UserProfileListResponse:
        """Search users by query builder.
        
        默认搜索一年内的用户
        
        Args:
            spec: 查询条件
            limit: 每页数量
            offset: 偏移量
        """
        collection = kwargs.get("collection")

        # 根据profile_type，获取最终的投影  
        projection = resolve_profile_projection(profile_type)
        # 构建查询条件,根据 spec 构建查询条件
        try:
            spec = MatchStageSpec.model_validate(spec)
        except ValidationError as e:
            logger.error(f"Invalid query spec: {e}")
            raise ServiceException(
                code=400,
                errmsg="查询条件格式错误",
                data={"validation_errors": e.errors()}
            )
        
        query_builder = MongoQueryBuilder(spec)
        match_stage = query_builder.build_match_stage()
        date_filter = {"join_date":
                        {"$gte":(datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d %H:%M:%S")}}

        pipeline = [
            convert_utc_to_local("JoinDate","join_date"),
            {"$match":date_filter},
            {"$match": match_stage},
            {"$project": projection},
            {"$skip": offset},
            {"$limit": limit},
        ]
        
        try:
        
            cursor = collection.aggregate(pipeline)
            users = await cursor.to_list(length=None)

            logger.info(f"Found {len(users)} users matching query: {spec}")
            return UserProfileListResponse(data=users)

        except Exception as e:
            logger.error(f"Failed to search users: {e}")
            raise MongoDBError(
                operation="search_users_by_query",
                message="根据特征查询用户失败",
                collection=collection,
                details={"error": str(e), "spec": spec}
            )

    async def sync_user_badges_heywhale(self, 
                                        badge_data: BadgeSyncBatchRequest, 
                                        *args, **kwargs) -> BaseDataPostResponse:
        """Sync user badges with HeyWhale platform."""
        try:
            # TODO: Move to HeyWhale integration service
            # This function should be moved to gateway/integrations/heywhale.py
            # and called from here via dependency injection
            logger.warning(
                "sync_user_badges_heywhale should be moved to HeyWhale integration service"
            )
            heywhale_integration = HeyWhaleIntegrationService()
            result:BadgeSyncResponse = await heywhale_integration.sync_badges(badge_data)


            # Placeholder implementation
            logger.info(f"Synced badges for {len(badge_data)} users with HeyWhale")
            return BaseDataPostResponse(
                id=str(ObjectId()),
                success=True,
                message=result.message,
                data=result.data
            )
        except ExternalServiceError as e:
            raise e
        except Exception as e:
            logger.error(f"Failed to sync user badges with HeyWhale: {e}")
            raise ServiceException(
                code=500,
                errmsg=f"HeyWhale徽章同步失败: {str(e)}",
                data={"error": str(e)}
            )

    async def update_heywhale_tags(self, tag_data: PostTagRequest, database=None) -> Dict[str, Any]:
        """Update user tags in HeyWhale platform."""
        try:
            # TODO: Move to HeyWhale integration service
            # This function should be moved to gateway/integrations/heywhale.py
            # and called from here via dependency injection
            logger.warning(
                "update_user_tags_heywhale should be moved to HeyWhale integration service"
            )
            heywhale_integration = HeyWhaleIntegrationService()
            result:PostTagResponse = await heywhale_integration.update_tag(tag_data)
            # Placeholder implementation
            logger.info(f"Updated tags for {len(tag_data)} users in HeyWhale")
            return BaseDataPostResponse(
                id=str(ObjectId()),
                success=result.success,
                message=result.message,
                data=result.invalid_data
            )
        except ExternalServiceError as e:
            raise e
        except Exception as e:
            logger.error(f"Failed to update user tags in HeyWhale: {e}")
            raise ServiceException(
                code=500,
                errmsg="HeyWhale标签更新失败",
                data={"error": str(e)}
            )

    async def get_heywhale_tags(self, tag_type:TagType, database=None) -> Dict[str, Any]:
        """Get HeyWhale tag tree structure."""
        try:
            # TODO: Move to HeyWhale integration service
            # This function should be moved to gateway/integrations/heywhale.py
            # and called from here via dependency injection
            logger.warning(
                "get_heywhale_tag_tree should be moved to HeyWhale integration service"
            )
            heywhale_integration = HeyWhaleIntegrationService()
            result:GetTagListResponse = await heywhale_integration.get_tags(tag_type)
            # Placeholder implementation
            return BaseDataPostResponse(
                id=str(ObjectId()),
                success=True,
                message=result.message,
                data=result.tags
            )
        except ExternalServiceError as e:
            raise e
        except Exception as e:
            logger.error(f"Failed to get HeyWhale tag tree: {e}")
            raise ServiceException(
                code=500,
                errmsg="获取HeyWhale标签树失败",
                data={"error": str(e)}
            )


# ——— Module-level singleton instance ———
user_services = UserServices()
