"""
Pydantic schemas for competition management endpoints.
"""

from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from libs.schemas.api.base import BaseDataListResponse
from libs.schemas.api.base import (
    BaseDataPostResponse, 
)
from datetime import datetime


# ——— Migrated from shared/schemas/api/job.py ———


class CompetitionUserRegisterRequest(BaseModel):
    """Request schema for fetching competition users by registration date."""

    register_start_date: Optional[str] = None


class CompetitionUserIdentityRegisterRequest(BaseModel):
    """Request schema for fetching competition users by identity."""

    identity: str
    register_start_date: Optional[str] = None


# ——— Migrated from shared/schemas/api/cs.py ———




# ——— Original domain schemas ———


# Request Schemas
class QualificationCreateRequest(BaseModel):
    """Request schema for creating a new qualification."""

    competition_id: str = Field(..., description="活动 ID，必填")
    credit: int = Field(..., gte=0, description="学分数量，必填, 必须不小于0")
    qualification_type: Optional[int] = Field(default=1, description="得分规则类型，选填, 默认为按照任务提交分数判断")
    qualification_logic: Optional[int] = Field(default=1, description="得分规则逻辑，选填，默认为大于等于某个分数阈值")
    qualification_name: Optional[str] = Field(
        None, description="得分规则名称"
    )
    related_task_id: Optional[str] = Field(None, description="关联的任务 ID，选填")
    score_threshold: Optional[int] = Field(
        0, description="得分规则分数阈值，选填"
    )

class QualificationCreateResponse(BaseDataPostResponse):
    """Response schema for creating a new qualification."""
    
    qualification_id: str = Field(..., description="Qualification ID")
    competition_id: str = Field(..., description="Competition ID")
    competition_name: str = Field(..., description="Competition name")
    route_name: str = Field(..., description="Route name")


class CreditLogCreateResponse(BaseDataPostResponse):
    """Response schema for creating a new credit log."""
    
    id: str = Field(..., description="Credit log ID", alias='record_id')
    
class CreditAwardRequest(BaseModel):
    """Request schema for awarding credits."""

    user_ids: List[str] = Field(..., description="用户 ID 列表，必填")
    competition_id: str = Field(..., description="活动 ID，必填")
    qual_id: str = Field(..., description="发放得分规则的 ID，必填")
    credit: int = Field(..., gte=0, description="发放学分数量，必填, 必须不小于0")
    remark: Optional[str] = Field(None, description="学分发放备注")

class CreditLogRevokeResponse(BaseDataPostResponse):
    """Response schema for revoking credits."""

    id: str = Field(..., description="Credit log ID", alias='record_id')


class CreditRevokeRequest(BaseModel):
    """Request schema for revoking credits."""

    record_id: str = Field(..., description="The ID of the credit record to revoke")


class AdminLogsCreateRequest(BaseModel):
    """Request schema for creating admin logs."""
    
    action_type: int = Field(..., description="Action type")
    action_related_id: Optional[str] = Field(None, description="Action related ID")
    remark: Optional[str] = Field(None, description="Remark") 
    admin_email_id: Optional[str] = Field(None, description="Admin ID")

class AdminLogsCreateResponse(BaseModel):
    """Response schema for creating admin logs."""
    
    record_id: str = Field(..., description="Admin log ID")
    action_type: int = Field(..., description="Action type")
    action_related_id: Optional[str] = Field(None, description="Action related ID")
    remark: Optional[str] = Field(None, description="Remark") 

# Note: Removed duplicate CompetitionUserRegisterRequest and CompetitionUserIdentityRegisterRequest
# as they are now included from the migrated schemas above


# Response Schemas
class RouteResponse(BaseModel):
    """Response schema for route data."""

    id: str = Field(..., description="Route ID",alias = 'route_id')
    name: str = Field(..., description="Route name", alias='route_name')


class CompetitionResponse(BaseModel):
    """Response schema for competition data."""
    id:str = Field(..., description="record id", alias='record_id')
    competition_id: str = Field(..., description="Competition ID")
    competition_name: str = Field(..., description="Competition name")
    route_id: str = Field(..., description="Route ID")
    route_name: str = Field(..., description="Route name")


class QualificationResponse(BaseModel):
    """Response schema for qualification data."""

    id: str = Field(..., description="Qualification ID",alias = 'qualification_id')
    competition_id: str = Field(..., description="Competition ID")
    qualification_name: Optional[str] = None
    credit: int



class CreditLogResponse(BaseModel):
    """Response schema for credit history."""

    id: str = Field(..., description="Credit log ID",alias = 'record_id')
    user_id: str = Field(..., description="User ID")
    competition_id: str = Field(..., description="Competition ID")
    qual_id: str = Field(..., description="Qualification ID",alias='qualification_id')
    credit: int
    remark: Optional[str] = Field(None, description="学分发放备注")
    batch_id: Optional[str] = Field(None, description="批次ID")
    created_at: Optional[datetime] = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(..., description="更新时间")


class CompetitionUserResponse(BaseModel):
    """Response schema for competition user data."""

    user_id: str = Field(..., description="User ID")
    username: Optional[str] = None
    email: Optional[str] = None
    registration_date: Optional[datetime] = None
    identity: Optional[str] = None
    competition_type: Optional[str] = None
    
class AdminLogsResponse(BaseModel):
    """Response schema for admin logs."""
    
    record_id: str = Field(..., description="Admin log ID",alias = 'record_id') 
    action_type: int = Field(..., description="Action type")        
    action_related_id: Optional[str] = None
    remark: Optional[str] = None  
    created_at: Optional[datetime] = None


# List Response Schemas
class RouteListResponse(BaseDataListResponse):
    """Response schema for route list."""

    data: List[RouteResponse] = Field(..., description="Route list")


class CompetitionListResponse(BaseDataListResponse):
    """Response schema for competition list."""

    data: List[CompetitionResponse] = Field(..., description="Competition list")


class QualificationListResponse(BaseDataListResponse):
    """Response schema for qualification list."""

    data: List[QualificationResponse] = Field(..., description="Qualification list")


class CompetitionUserListResponse(BaseDataListResponse):
    """Response schema for competition user list."""

    data: List[CompetitionUserResponse] = Field(..., description="Competition user list")


class CreditLogListResponse(BaseDataListResponse):
    """Response schema for credit history list."""

    data: List[CreditLogResponse] = Field(..., description="Credit log list")
 
    
class AdminLogsListResponse(BaseDataListResponse):
    
    data: List[AdminLogsResponse] = Field(..., description="Admin log list")
