#!/usr/bin/env python3
"""
Gateway API Server Startup Script.

Runs the FastAPI server with the new service layer architecture.
Access Swagger docs at: http://localhost:8000/docs
"""

import uvicorn
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def main():
    """Start the FastAPI server."""
    print("Starting Community Services Gateway API...")
    print("Swagger docs will be available at: http://localhost:8000/docs")
    print("ReDoc will be available at: http://localhost:8000/redoc")
    print("Health check at: http://localhost:8000/api/v1/health")
    print("Competition endpoints at: http://localhost:8000/api/v1/competitions/")
    print()

    # Run the server
    uvicorn.run(
        "gateway.main_router:app",
        host="0.0.0.0",
        port=8005,
        reload=True,  # Enable auto-reload for development
        log_level="info",
        access_log=True,
    )


if __name__ == "__main__":
    main()
