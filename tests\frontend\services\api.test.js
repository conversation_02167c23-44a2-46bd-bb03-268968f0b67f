import { describe, it, expect, beforeEach, vi } from 'vitest'
import axios from 'axios'

// Mock axios
vi.mock('axios')

describe('API Service', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('creates axios instance with correct configuration', async () => {
    const mockCreate = vi.fn().mockReturnValue({
      interceptors: {
        request: { use: vi.fn() },
        response: { use: vi.fn() }
      }
    })
    axios.create = mockCreate

    await import('../../../frontend/src/services/api')

    expect(mockCreate).toHaveBeenCalledWith({
      baseURL: expect.any(String),
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    })
  })

  it('sets up request interceptor for authentication', async () => {
    const mockInterceptors = {
      request: { use: vi.fn() },
      response: { use: vi.fn() }
    }
    
    const mockInstance = {
      interceptors: mockInterceptors
    }

    axios.create = vi.fn().mockReturnValue(mockInstance)

    await import('../../../frontend/src/services/api')

    expect(mockInterceptors.request.use).toHaveBeenCalled()
    expect(mockInterceptors.response.use).toHaveBeenCalled()
  })

  it('request interceptor adds authorization header when token exists', async () => {
    const mockConfig = {
      headers: {}
    }

    // Mock Cookies.get to return a token
    vi.doMock('js-cookie', () => ({
      default: {
        get: vi.fn().mockReturnValue('mock-token')
      }
    }))

    const mockInterceptors = {
      request: { use: vi.fn() },
      response: { use: vi.fn() }
    }
    
    const mockInstance = {
      interceptors: mockInterceptors
    }

    axios.create = vi.fn().mockReturnValue(mockInstance)

    await import('../../../frontend/src/services/api')

    // Get the request interceptor function
    const requestInterceptor = mockInterceptors.request.use.mock.calls[0][0]
    const result = requestInterceptor(mockConfig)

    expect(result.headers.Authorization).toBe('Bearer mock-token')
  })

  it('response interceptor handles errors correctly', async () => {
    const mockError = {
      response: {
        status: 401,
        data: { message: 'Unauthorized' }
      }
    }

    const mockInterceptors = {
      request: { use: vi.fn() },
      response: { use: vi.fn() }
    }
    
    const mockInstance = {
      interceptors: mockInterceptors
    }

    axios.create = vi.fn().mockReturnValue(mockInstance)

    await import('../../../frontend/src/services/api')

    // Get the response error interceptor function
    const errorInterceptor = mockInterceptors.response.use.mock.calls[0][1]
    
    try {
      await errorInterceptor(mockError)
    } catch (error) {
      expect(error).toBe(mockError)
    }
  })
})
