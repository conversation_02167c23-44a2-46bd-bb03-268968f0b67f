WITH UserCredits AS (
    SELECT
        u.user_id,
        u.user_name,
        SUM(u.credit) AS total_credits
    FROM
        qualified_users_triggered u
    GROUP BY
        u.user_id, u.user_name
),
RouteCredits AS (
    SELECT
        u.user_id,
        c.route_id,
        SUM(u.credit) AS route_credits
    FROM
        qualified_users_triggered u
    JOIN
        competitions c ON u.competition_id = c.competition_id
    GROUP BY
        u.user_id, c.route_id
),
RankedUser AS (
    SELECT
        uc.*,
        ROW_NUMBER() OVER (ORDER BY total_credits DESC, user_name ASC) AS row_num
    FROM
        UserCredits uc
),
TotalCount AS (
    SELECT COUNT(*) AS total_count FROM RankedUser
),
RouteCreditPivot AS (
    SELECT
        user_id,
        {{case_statements}} -- Updated placeholder syntax
    FROM
        RouteCredits
    GROUP BY
        user_id
)
SELECT
    ru.user_name,
    RIGHT(ru.user_id, 4) AS user_id, -- Consider user_id exposure
    ru.total_credits,
    tc.total_count,
    {{select_statements}} -- Updated placeholder syntax
FROM
    RankedUser ru
JOIN
    TotalCount tc ON 1=1
JOIN
    RouteCreditPivot rc ON ru.user_id = rc.user_id
ORDER BY
    ru.row_num ASC
LIMIT {{page_size}} OFFSET {{offset}}; -- Updated placeholder syntax 