"""
API Gateway Service components.

This module contains the main API service components including
endpoints, business logic services, external integrations,
and MCP server implementations.
"""

# Export the layered service container for dependency injection
# from core.container import (
#     get_credit_admin,
#     get_shence_integration,
# )

__all__ = [
    # "RequestHandlerContainer",
    # "get_layered_container",
    # "initialize_container",
    "get_credit_admin",
    "get_shence_integration",
]
