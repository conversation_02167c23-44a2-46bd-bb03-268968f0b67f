/**
 * Mock Services Index
 * Central export for all mock API services
 */

export { authService } from './authService.js'
export { competitionService } from './competitionService.js'
export { creditService } from './creditService.js'
export { schoolService } from './schoolService.js'
export { initializeMockData } from './mockData.js'

// Mock service configuration
export const mockConfig = {
  enabled: true,
  delay: 500, // Default delay in milliseconds
  errorRate: 0.05, // 5% chance of random errors for testing
  
  // Feature flags for different mock services
  features: {
    competitions: true,
    credits: true,
    schools: true,
    users: true,
    analytics: true
  }
}

// Helper function to simulate network errors
export const simulateError = (errorRate = mockConfig.errorRate) => {
  if (Math.random() < errorRate) {
    throw new Error('Simulated network error')
  }
}

// Helper function to add random delay
export const addDelay = (ms = mockConfig.delay) => {
  return new Promise(resolve => setTimeout(resolve, ms))
}
