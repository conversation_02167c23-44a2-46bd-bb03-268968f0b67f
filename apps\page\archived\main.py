import streamlit as st
from streamlit_cookies_manager import EncryptedCookie<PERSON>anager
from datetime import datetime, timedelta
from libs.utils import load_page
from pages.admin_home import get_user_name

# Main function to run the app
# Initialize the cookie manager
cookies = EncryptedCookieManager(
    prefix="myapp/",
    password="your_secret_password",  # replace with a secure password
)
expiration_time = datetime.now() + timedelta(days=365)

st.session_state.logged_in = (
    False
    if st.session_state.get("logged_in", None) is None
    else st.session_state.logged_in
)
st.session_state.username = (
    "" if st.session_state.get("username", None) is None else st.session_state.username
)
st.session_state.expired_at = expiration_time.timestamp()
st.session_state.cookies = cookies
st.session_state.expire_cookie = st.session_state.get("expire_cookie", False)
if not cookies.ready():
    st.stop()

# checks if current session contains cookies
now = datetime.now().timestamp()
has_cookie = (
    cookies.get("username", None)
    and get_user_name(cookie_user_name=cookies.get("username", None)) != "Anonymous"
)
session_expires = False
if st.session_state.get("expire_cookie", False):
    has_cookie = False

if not has_cookie:
    st.session_state.logged_in = False
else:
    session_expires = now >= int(float(cookies["expired_at"]))
    st.session_state.logged_in = not session_expires
    st.session_state.username = (
        cookies.get("username", "") if st.session_state.logged_in else ""
    )

if not st.session_state.logged_in and not has_cookie:
    load_page("auth")

# # elif st.session_state.get('page_loaded',None) is None or not st.session_state.page_loaded:
# elif session_expires:
#     with st.empty():
#         msg = st.container()
#         msg.error("登录已过期，请重新登录！")
#         relogin = st.container()
#         relogin.page_link("pages/login.py",label="session 过期，请重新登录",icon="😀")
else:
    load_page("home")
    # cookies['expired_at'] = str(expiration_time.timestamp())
