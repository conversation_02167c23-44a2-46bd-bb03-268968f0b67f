# -*- coding:utf-8 -*-
"""
Database models for the data pipeline service.

This module contains all Tortoise ORM models used throughout the application.
Models are organized by functionality and include proper relationships,
validation, and type hints for better maintainability.
"""

import datetime
from zoneinfo import ZoneInfo
from bson import ObjectId
from tortoise.fields.data import Datetime<PERSON><PERSON>
from tortoise import fields
from tortoise.timezone import localtime, make_naive, get_default_timezone


class NaiveDatetimeField(fields.DatetimeField):
    """
    Naive datetime field for storing naive datetime objects.
    This field automatically converts aware datetime objects to naive objects.
    Example:
    ```python
    class MyModel(Model):
        created_at = NaiveDatetimeField(default=datetime.datetime.now)
    ```
    In the database (postgres), you will see something like this "2025-06-25 10:21:06.508567"
    """
    def to_python_value(self, value):
        # delegate parsing/awareness to Tortoise
        dt = super().to_python_value(value)
        if dt is None:
            return None
        # convert to local zone and strip tzinfo
        return dt.astimezone(ZoneInfo("Asia/Shanghai")).replace(tzinfo=None)

    def to_db_value(self, value: datetime, instance):
        if value is None:
            return None
        # assume it's already naive or a datetime; format to string
        return value


try:
    from tortoise import fields
    from tortoise.models import Model
except ImportError:
    # Tortoise ORM not installed, create placeholder classes
    class Model:
        class Meta:
            abstract = True
            table = ""
            table_description = ""
        
        def __init__(self, *args, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)
        
        async def save(self):
            pass
        
        @classmethod
        async def filter(cls, **kwargs):
            return cls.QuerySet()
        
        @classmethod
        async def all(cls):
            return cls.QuerySet()
        
        @classmethod
        async def get_or_create(cls, defaults=None, **kwargs):
            return cls(), True
        
        @classmethod
        async def update_or_create(cls, defaults=None, **kwargs):
            return cls(), True
        
        @classmethod
        async def bulk_create(cls, objects, ignore_conflicts=False):
            pass
        
        @classmethod 
        async def exists(cls):
            return False
        
        class QuerySet:
            async def exists(self):
                return False
            
            def filter(self, **kwargs):
                return self
            
            async def all(self):
                return []
            
            async def first(self):
                return None
            
            def annotate(self, **kwargs):
                return self
            
            async def values(self, *args):
                return []
    
    class fields:
        @staticmethod
        def CharField(max_length=None, pk=False, unique=False, db_index=False, null=False, description="", **kwargs):
            return None
        
        @staticmethod
        def IntField(description="", default=None, **kwargs):
            return None
        
        @staticmethod
        def FloatField(null=False, description="", **kwargs):
            return None
        
        @staticmethod
        def DatetimeField(auto_now_add=False, auto_now=False, description="", **kwargs):
            return None
        
        @staticmethod
        def TextField(null=False, description="", **kwargs):
            return None
        
        @staticmethod
        def ForeignKeyField(model, related_name=None, to_field=None, on_delete=None, db_index=False, description="", **kwargs):
            return None
        
        @staticmethod
        def BooleanField(default=False, description="", **kwargs):
            return None
        
        CASCADE = "CASCADE"



class TimestampMixin(Model):
    """
    Base mixin providing created and updated timestamp fields.
    
    This mixin should be inherited by all models that need automatic
    timestamp tracking for creation and modification times.
    """

    created_at = NaiveDatetimeField(default=datetime.datetime.now, description="创建时间")
    updated_at = NaiveDatetimeField(default=datetime.datetime.now, description="更新时间")

    class Meta:
        abstract = True

class VisibilityMixin(Model):
    """
    Base mixin providing visibility fields.
    
    This mixin should be inherited by all models that need visibility tracking.
    """
    is_hidden = fields.BooleanField(default=False, description="是否隐藏")
    is_deleted = fields.BooleanField(default=False, description="是否删除")

    class Meta:
        abstract = True
    
class UserBasedMixin(Model):
    """
    Base mixin providing user-based fields.
    
    This mixin should be inherited by all models that need user-based tracking.
    """
    user_id = fields.CharField(
        max_length=255, description="用户 ID",db_index=True
    )
    user_name = fields.CharField(
        max_length=255, description="用户名称",db_index=True,null=True
    )
    class Meta:
        abstract = True

class SchoolBasedMixin(Model):
    """
    Base mixin providing school-based fields.
    
    This mixin should be inherited by all models that need school-based tracking.
    """
    university = fields.CharField(
        max_length=255, description="学校名称",db_index=True,null=True
    )
    class Meta:
        abstract = True

class StatisticsMixin(TimestampMixin,VisibilityMixin):
    """
    Statistics model for analytics and reporting.
    
    Stores various statistical data points and metrics
    for competitions, users, and system performance.
    """
    statistics_name = fields.CharField(
        max_length=255, description="统计名称",db_index=True,null=True
    )
    statistics_value = fields.FloatField(
        description="统计值",null=True,
    )
    is_inflated = fields.BooleanField(
        default=False, description="是否注水",db_index=True
    )
    inflation_id = fields.CharField(
        max_length=255, null=True, description="注水 ID: 该 ID 指向一系列相关行为",db_index=True
    )
    remark = fields.TextField(
        null=True, description="备注"
    )
    class Meta:
        abstract = True

class QualificationMixin(TimestampMixin,VisibilityMixin):
    """
    Qualification model for defining achievement criteria.
    
    Specifies requirements and thresholds for earning
    qualifications in competitions.
    """ 
    qualification_id = fields.CharField(
        max_length=255, description="学分规则 ID",db_index=True
    )
    qualification_name = fields.CharField(
        max_length=255, description="学分规则名称",db_index=True
    )
    competition_id = fields.CharField(
        max_length=255, description="活动 ID",db_index=True
    )
    competition_name = fields.CharField(
        max_length=255, description="活动名称",db_index=True
    )
    task_id = fields.CharField(
        max_length=255, description="任务 ID",null=True
    )
    class Meta:   
        abstract = True



