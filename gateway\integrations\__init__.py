"""
Integration layer for external API calls with graceful error handling.

Provides HTTP client, retry logic, and external service integrations.
"""

from .base import BaseIntegration, HTTPResponse, RetryConfig
from .shence import ShenceIntegration
from .heywhale import HeyWhaleIntegrationService
from .webhook import WebhookIntegration
from .routes import router
from . import schemas
from . import dependencies

__all__ = [
    "BaseIntegration",
    "HTTPResponse",
    "RetryConfig",
    "ShenceIntegration",
    "HeyWhaleIntegrationService",
    "WebhookIntegration",
    "router",
    "schemas",
    "dependencies",
]
