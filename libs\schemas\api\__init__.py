"""
API schemas and response utilities.

This module provides standardized schemas and response utilities
for all API endpoints. Domain-specific schemas have been moved
to their respective domains.
"""

from .responses import (
    BaseResponse,
    SuccessResponse,
    ErrorResponse,
    AntdTableResponse,
    base_response,
    success,
    fail,
    res_antd,
)
from .common import (
    EventTracksResponse,
)

__all__ = [
    # Response models
    "BaseResponse",
    "SuccessResponse",
    "ErrorResponse",
    "AntdTableResponse",
    # Response utilities
    "base_response",
    "success",
    "fail",
    "res_antd",
    # Common models
    "EventTracksResponse",
]
