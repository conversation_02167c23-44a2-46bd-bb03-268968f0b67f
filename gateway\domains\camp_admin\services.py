"""
Competition business logic and data access services.

Contains all business logic for competitions including data access,
validation, and integration with external services.
"""
import logging

logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s %(levelname)s %(name)s: %(message)s",
    datefmt="%H:%M:%S"
)
# Explicitly bump <PERSON><PERSON><PERSON>’s loggers to DEBUG
for name in ("tortoise", "tortoise.transactions", "tortoise.backends.asyncpg"):
    logging.getLogger(name).setLevel(logging.DEBUG)


from typing import Optional
from bson import ObjectId
from tortoise import BaseDBAsyncClient, Tortoise
from tortoise.transactions import atomic
from core.database.mongo_db import mongo_manager
from libs.models.base_models import (
    Route as TortoiseRoute,
    Competition as TortoiseCompetition,
    Qualification as TortoiseQualification,
    CreditHistory as TortoiseCreditHistory,
    AdminLogs as TortoiseAdminLogs,
    User as TortoiseUser,
)
from .schemas import (
    RouteListResponse,
    CompetitionListResponse,
    QualificationListResponse,
    CreditLogListResponse,
    QualificationCreateRequest,
    CreditAwardRequest,
    AdminLogsCreateRequest,
    AdminLogsListResponse,
)
from libs.schemas.api.base import BaseDataPostResponse
from libs.exceptions import BusinessLogicError, ServiceException, PostgreSQLError, DatabaseConnectionError
from libs.errors import CampAdminDomainErrors, ErrorCode


class CampAdminServices:
    """Business logic services for competition operations."""

    def __init__(self):
        """Initialize competition services."""
        self.mongo_db = mongo_manager

    # ——— Route Management： 赛道管理 ———
    async def get_routes(
        self, 
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        db_session: Optional[BaseDBAsyncClient] = None  
    ) -> RouteListResponse:
        """Get list of competition routes."""
        query = TortoiseRoute.all(using_db=db_session)
        
        # check if the pagination is valid
        if limit * offset < 0:
            raise ServiceException(
                code=ErrorCode.VALIDATION_ERROR,
                errmsg=CampAdminDomainErrors.invalid_pagination(limit, offset),
                service="camp-admin",
            )
        
        if offset is not None and offset >= 0:
            query = query.offset(offset)
        if limit is not None and limit > 0:
            query = query.limit(limit)
        
        # get the routes
        try:
            routes = await query
            return RouteListResponse(
                data=[{"route_id": r.route_id, "route_name": r.route_name} for r in routes],
                total=len(routes),
                limit=limit if limit is not None and limit > 0 else None,
                offset=offset if offset is not None and offset >= 0 else None,
            )
        except PostgreSQLError as e:
            raise ServiceException(
                code=ErrorCode.DATABASE_ERROR,
                errmsg=CampAdminDomainErrors.database_error(e),
                service="camp-admin",
            )


    # ——— Competition Management： 比赛管理 ———
    async def get_competitions(
        self,
        route_id: Optional[str] = None,
        status: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        db_session: Optional[BaseDBAsyncClient] = None
    ) -> CompetitionListResponse:
        """Get list of competitions with optional filtering."""
        
        # check if the route exists
        if route_id is not None:
            route_exists = await TortoiseRoute.exists(route_id=route_id,using_db=db_session)
            if not route_exists:
                raise BusinessLogicError(
                    rule="route_not_found",
                    message=f"Route {route_id} not found",
                    context={"route_id": route_id},
                )
        # check if the pagination is valid
        if limit * offset < 0:
            raise ServiceException(
                code=ErrorCode.VALIDATION_ERROR,
                errmsg=CampAdminDomainErrors.invalid_pagination(limit, offset),
                service="camp-admin",
            )
        
        # get the competitions
        query = TortoiseCompetition.all(using_db=db_session)
        if route_id is not None:
            query = query.filter(route_id=route_id)
        if status is not None:
            query = query.filter(status=status)
        if offset is not None and offset >= 0:
            query = query.offset(offset)
        if limit is not None and limit > 0:
            query = query.limit(limit)
        
        try:
            # return the competitions
            competitions = await query
            return CompetitionListResponse(
                data=[
                    {
                        "record_id": c.record_id,
                        "competition_id": c.competition_id,
                        "competition_name": c.competition_name,
                        "route_id": c.route_id,
                        "route_name": c.route_name,
                    }
                    for c in competitions
                ],
                total=len(competitions),
                limit=limit if limit is not None and limit > 0 else None,
                offset=offset if offset is not None and offset >= 0 else None,
            )
        except PostgreSQLError as e:
            raise ServiceException(
                code=ErrorCode.DATABASE_ERROR,
                errmsg=CampAdminDomainErrors.database_error(e),
                service="camp-admin",
            )

    # ——— Qualification Management： 晋级逻辑管理 ———
    async def get_qualifications(
        self,
        competition_id: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        db_session: Optional[BaseDBAsyncClient] = None
    ) -> QualificationListResponse:
        """Get qualifications for a specific competition."""
        
        # check if the competition exists
        if competition_id is not None:
            competition_exists = await TortoiseCompetition.exists(competition_id=competition_id,using_db=db_session)
            if not competition_exists:
                raise BusinessLogicError(
                    rule="competition_not_found",
                    message=f"Competition {competition_id} not found",
                    context={"competition_id": competition_id},
                )
                
        if limit * offset < 0:
            raise ServiceException(
                code=ErrorCode.VALIDATION_ERROR,
                errmsg=CampAdminDomainErrors.invalid_pagination(limit, offset),
                service="camp-admin",
            )

        # get the qualifications
        query = TortoiseQualification.all(using_db=db_session)
        if competition_id is not None:
            query = query.filter(competition_id=competition_id)
        if offset is not None and offset >= 0:
            query = query.offset(offset)
        if limit is not None and limit > 0:
            query = query.limit(limit)

        try:
            # return the qualifications
            qualifications = await query
            return QualificationListResponse(
                data=[
                    {
                        "qualification_id": q.qualification_id,
                        "qualification_name": q.qualification_name,
                        "credit": q.credit,
                        "competition_id": q.competition_id,
                    }
                    for q in qualifications
                ],
                total=len(qualifications),
                limit=limit if limit is not None and limit > 0 else None,
                offset=offset if offset is not None and offset >= 0 else None,
                )
        except PostgreSQLError as e:
            raise ServiceException(
                code=ErrorCode.DATABASE_ERROR,
                errmsg=CampAdminDomainErrors.database_error(e),
                service="camp-admin",
            )
    async def create_qualification(
        self,
        qualification_data: QualificationCreateRequest,
        db_session: Optional[BaseDBAsyncClient] = None
    ) -> BaseDataPostResponse:
        """Create a new qualification."""

        qualification_id = str(ObjectId())
        # check if the qualification already exists
        qualification_exists = await TortoiseQualification.exists(
            competition_id=qualification_data.competition_id,
            qualification_name=qualification_data.qualification_name,
            related_task_id=qualification_data.related_task_id,
            # using_db=db_session
        )
        if qualification_exists:
            raise BusinessLogicError(
                rule="qualification_already_exists",
                message=f"Qualification for competition {qualification_data.competition_id} already exists",
                context={"competition_id": qualification_data.competition_id},
            )
        try:
            qualification = await TortoiseQualification.create(
                qualification_id=qualification_id,
                competition_id=qualification_data.competition_id,
                competition_name=qualification_data.competition_name,
                route_name=qualification_data.route_name,
                credit=qualification_data.credit,
                qualification_name=qualification_data.qualification_name,
                related_task_id=qualification_data.related_task_id,
                score_threshold=qualification_data.score_threshold,
                using_db=db_session
            )

            return BaseDataPostResponse(
                id = qualification_id,
                success = True,
                message = f"Qualification {qualification_data.qualification_name} created successfully",
                )
        except PostgreSQLError as e:
            raise ServiceException(
                code=ErrorCode.DATABASE_ERROR,
                errmsg=CampAdminDomainErrors.database_error(e),
                    service="camp-admin",
            )   

    # ——— Credit Management： 学分管理 ———
    async def get_credit_logs(
        self,
        competition_id: Optional[str] = None,
        user_id: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        db_session: Optional[BaseDBAsyncClient] = None
    ) -> CreditLogListResponse:
        """Get credit transaction history."""
        # check if the pagination is valid
        if limit * offset < 0:
            raise ServiceException(
                code=ErrorCode.VALIDATION_ERROR,
                errmsg=CampAdminDomainErrors.invalid_pagination(limit, offset),
                service="camp-admin",
            )
        
        # get the credit logs
        query = TortoiseCreditHistory.all(using_db=db_session)
        if competition_id is not None:
            query = query.filter(competition_id=competition_id, using_db=db_session)
        if user_id is not None:
            query = query.filter(user_id=user_id, using_db=db_session)
        if offset is not None and offset >= 0:
            query = query.offset(offset)        
        if limit is not None and limit > 0:
            query = query.limit(limit)
        
        query = query.filter(deleted=False,)

        try:
            credit_logs = await query
            return CreditLogListResponse(
                data=[
                    {
                        "record_id": c.record_id,
                        "user_id": c.user_id,
                        "competition_id": c.competition_id,
                        "credit": c.credit,
                        "remark": c.remark,
                        "batch_id": c.batch_id,
                        "qualification_id": c.qualification_id,
                        "created_at": c.created_at,
                        "updated_at": c.updated_at,
                    }
                    for c in credit_logs
                ],
                total=len(credit_logs),
                limit=limit if limit is not None and limit > 0 else None,
                offset=offset if offset is not None and offset >= 0 else None,
            )
        except PostgreSQLError as e:
            raise ServiceException(
                code=ErrorCode.DATABASE_ERROR,
                errmsg=CampAdminDomainErrors.database_error(e),
                service="camp-admin",
            )

    async def award_credits(
        self,
        credit_log_data: CreditAwardRequest,
        db_session: Optional[BaseDBAsyncClient] = None
    ) -> BaseDataPostResponse:
        """Award credits to a user."""
        
        user_ids = credit_log_data.user_ids or []
        competition_id = credit_log_data.competition_id
        credit = credit_log_data.credit
        qualification_id = credit_log_data.qual_id
        remark = credit_log_data.remark
        batch_id = str(ObjectId())
        
        # check if the qualification exists
        qualification_exists = await TortoiseQualification.exists(
            qualification_id=qualification_id,
            competition_id=competition_id,
            using_db=db_session
        )
        if not qualification_exists:
            raise BusinessLogicError(
                rule="qualification_not_found",
                message=f"Qualification {qualification_id} not found",
                context={"qualification_id": qualification_id},
            )
        
        bulk_records = []
        for user_id in user_ids:
            # check if the user exists
            user_exists = await TortoiseUser.exists(
                user_id=user_id,
                using_db=db_session
            )
            if not user_exists:
                raise BusinessLogicError(
                    rule="user_not_found",
                    message=f"User {user_id} not found",
                    context={"user_id": user_id},
                )
                
            # check if user in the competition
            user_in_competition = await TortoiseUser.exists(
                competition_id=competition_id,
                user_id=user_id,
                using_db=db_session
            )
            if not user_in_competition:
                raise BusinessLogicError(
                    rule="user_not_in_competition",
                    message=f"User {user_id} not in competition {competition_id}",
                    context={"user_id": user_id, "competition_id": competition_id},
                )

            # check if the credit log already exists
            credit_log_exists = await TortoiseCreditHistory.exists(
                user_id=user_id,
                competition_id=competition_id,
                remark=remark,
                using_db=db_session
            )  
        
            if credit_log_exists:
                raise BusinessLogicError(
                    rule="credit_log_already_exists",
                    message=f"Credit log for user {user_id} and competition {competition_id} already exists",
                    context={"user_id": user_id, "competition_id": competition_id},
                )
            record_id = str(ObjectId())
            bulk_records.append(
                TortoiseCreditHistory(
                    record_id=record_id,
                    user_id=user_id,
                    competition_id=competition_id,
                    credit=credit,
                    batch_id=batch_id, 
                    qualification_id=qualification_id,
                    remark=remark,
                    using_db=db_session
                )
            )

        try:
            await TortoiseCreditHistory.bulk_create(
                bulk_records,
                using_db=db_session
            )
            return BaseDataPostResponse(
                id = record_id,
                    success = True,
                    message = f"Credit {credit} awarded to user {user_id} for competition {competition_id}",
                )
        except PostgreSQLError as e:
            raise ServiceException(
                code=ErrorCode.DATABASE_ERROR,
                errmsg=CampAdminDomainErrors.database_error(e),
                service="camp-admin",
            )
    
    async def revoke_credits(self, record_id: str, reason: str, db_session: Optional[BaseDBAsyncClient] = None) -> BaseDataPostResponse:
        """Revoke a credit record."""
        # check if the credit log exists
        credit_log_exists = await TortoiseCreditHistory.exists(
            record_id=record_id,
            deleted=False,
            using_db=db_session
        )
        if not credit_log_exists:
            raise BusinessLogicError(
                rule="credit_log_not_found",
                message=f"Credit log {record_id} not found",
                context={"record_id": record_id},
            )

        await TortoiseCreditHistory.update(
            record_id=record_id,
            deleted=True,
            remark=reason,
            using_db=db_session
        )
        return BaseDataPostResponse(
            record_id=record_id,
            remark=reason,
        )

    # ——— Admin Logs Management： 管理员日志管理 ———
    async def get_admin_logs(
        self,
        operation: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        db_session: Optional[BaseDBAsyncClient] = None
    ) -> AdminLogsListResponse:
        """Get a list of admin logs."""
        
        if limit * offset < 0:
            raise ServiceException(
                code=ErrorCode.VALIDATION_ERROR,
                errmsg=CampAdminDomainErrors.invalid_pagination(limit, offset),
                service="camp-admin",
            )   
        query = TortoiseAdminLogs.all(using_db=db_session)
        if operation is not None:
            query = query.filter(action_type=operation, using_db=db_session)
        if offset is not None and offset >= 0:
            query = query.offset(offset)
        if limit is not None and limit > 0:
            query = query.limit(limit)

        try:
            admin_logs = await query        
            return AdminLogsListResponse(
                data=[
                    {
                        "record_id": a.record_id,
                        "action_type": a.action_type,
                        "action_related_id": a.action_related_id,
                        "remark": a.remark,
                        "created_at": a.created_at,
                    }
                    for a in admin_logs
                ],
                total=len(admin_logs),
                limit=limit if limit is not None and limit > 0 else None,
                offset=offset if offset is not None and offset >= 0 else None,
                )
        except PostgreSQLError as e:
            raise ServiceException(
                code=ErrorCode.DATABASE_ERROR,
                errmsg=CampAdminDomainErrors.database_error(e),
                service="camp-admin",
            )

    # ——— Admin Logs Management： 管理员日志管理 ———
    async def create_admin_log(
        self,
        log_data: AdminLogsCreateRequest,
        db_session: Optional[BaseDBAsyncClient] = None,
    ) -> BaseDataPostResponse:
        """Create a new admin log."""
        record_id = str(ObjectId())
        # check if the admin log already exists
        try:
            await TortoiseAdminLogs.create(
            record_id=record_id,
            action_type=log_data.action_type,
            action_related_id=log_data.action_related_id,
            remark=log_data.remark,
            admin_email_id=log_data.admin_email_id,
            using_db=db_session
            )
            return BaseDataPostResponse(
                id = record_id,
                success = True,
                message = f"Admin log {log_data.action_type} created successfully:{log_data.remark}",
                )
        except PostgreSQLError as e:
            raise ServiceException(
                code=ErrorCode.DATABASE_ERROR,
                errmsg=CampAdminDomainErrors.database_error(e),
                service="camp-admin",
            )

# Global services instance
camp_admin_services = CampAdminServices()
