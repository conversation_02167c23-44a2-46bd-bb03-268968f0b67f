"""
Tests for the improved database infrastructure.

This module tests the SSH tunnel manager and MongoDB connection manager
with proper mocking to avoid requiring actual connections during testing.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from core.config.tunnel_manager import SSHTunnelManager
from core.database.mongo_db import MongoDBManager


class TestSSHTunnelManager:
    """Test the SSH tunnel manager."""

    @pytest.fixture
    def tunnel_mgr(self):
        """Create a fresh tunnel manager instance for testing."""
        # Reset singleton for testing
        SSHTunnelManager._instance = None
        return SSHTunnelManager()

    @pytest.fixture
    def mock_settings(self):
        """Mock settings for testing."""
        with patch("core.config.tunnel_manager.settings") as mock:
            mock.ssh_tunnel.enabled = True
            mock.ssh_tunnel.host = "test.example.com"
            mock.ssh_tunnel.port = 22
            mock.ssh_tunnel.username = "testuser"
            mock.ssh_tunnel.private_key_path = "/tmp/test_key"
            mock.database.mongo_host = "mongo.example.com"
            mock.database.mongo_port = 27017
            yield mock

    def test_singleton_pattern(self, tunnel_mgr):
        """Test that tunnel manager follows singleton pattern."""
        mgr1 = SSHTunnelManager()
        mgr2 = SSHTunnelManager()
        assert mgr1 is mgr2

    @pytest.mark.asyncio
    async def test_tunnel_disabled(self, tunnel_mgr, mock_settings):
        """Test behavior when tunnel is disabled."""
        mock_settings.ssh_tunnel.enabled = False

        await tunnel_mgr.start()
        assert not tunnel_mgr.is_active

        tunnel = await tunnel_mgr.get_tunnel()
        assert tunnel is None

    @pytest.mark.asyncio
    async def test_configuration_validation(self, tunnel_mgr, mock_settings):
        """Test configuration validation."""
        # Test missing host
        mock_settings.ssh_tunnel.host = None
        with pytest.raises(ValueError, match="SSH_HOST is required"):
            await tunnel_mgr.start()

        # Test missing username
        mock_settings.ssh_tunnel.host = "test.example.com"
        mock_settings.ssh_tunnel.username = None
        with pytest.raises(ValueError, match="SSH_USERNAME is required"):
            await tunnel_mgr.start()

        # Test missing authentication
        mock_settings.ssh_tunnel.username = "testuser"
        mock_settings.ssh_tunnel.private_key_path = None
        mock_settings.ssh_tunnel.password = None
        with pytest.raises(
            ValueError, match="Either SSH_PASSWORD or SSH_PRIVATE_KEY_PATH"
        ):
            await tunnel_mgr.start()

    @pytest.mark.asyncio
    async def test_private_key_validation(self, tunnel_mgr, mock_settings):
        """Test private key file validation."""
        # Test non-existent key file
        mock_settings.ssh_tunnel.private_key_path = "/nonexistent/key"

        with pytest.raises(FileNotFoundError, match="SSH private key file not found"):
            await tunnel_mgr.start()

    @pytest.mark.asyncio
    @patch("core.config.tunnel_manager.SSHTunnelForwarder")
    @patch("core.config.tunnel_manager.Path")
    async def test_successful_tunnel_creation(
        self, mock_path, mock_forwarder, tunnel_mgr, mock_settings
    ):
        """Test successful tunnel creation."""
        # Mock path exists
        mock_path.return_value.exists.return_value = True
        mock_path.return_value.is_file.return_value = True

        # Mock tunnel forwarder
        mock_tunnel = MagicMock()
        mock_tunnel.is_active = True
        mock_tunnel.local_bind_port = 12345
        mock_forwarder.return_value = mock_tunnel

        await tunnel_mgr.start()

        assert tunnel_mgr.is_active
        assert tunnel_mgr.local_bind_port == 12345
        mock_tunnel.start.assert_called_once()

    @pytest.mark.asyncio
    async def test_health_check(self, tunnel_mgr):
        """Test tunnel health check."""
        # Test when not active
        assert not await tunnel_mgr.health_check()

        # Test when active
        tunnel_mgr._tunnel = MagicMock()
        tunnel_mgr._tunnel.is_active = True
        assert await tunnel_mgr.health_check()

    @pytest.mark.asyncio
    async def test_context_manager(self, tunnel_mgr, mock_settings):
        """Test tunnel context manager."""
        mock_settings.ssh_tunnel.enabled = False

        async with tunnel_mgr.tunnel_context() as tunnel:
            assert tunnel is None


class TestMongoDBManager:
    """Test the MongoDB manager."""

    @pytest.fixture
    def mongo_mgr(self):
        """Create a fresh MongoDB manager instance for testing."""
        return MongoDBManager()

    @pytest.fixture
    def mock_settings(self):
        """Mock settings for testing."""
        with patch("core.database.mongo_db.settings") as mock:
            mock.ssh_tunnel.enabled = False
            mock.database.mongo_host = "localhost"
            mock.database.mongo_port = 27017
            mock.database.mongo_user = "testuser"
            mock.database.mongo_password = "testpass"
            mock.database.mongo_database = "testdb"
            yield mock

    @pytest.mark.asyncio
    async def test_connection_without_tunnel(self, mongo_mgr, mock_settings):
        """Test MongoDB connection without SSH tunnel."""
        with patch("core.database.mongo_db.AsyncIOMotorClient") as mock_client:
            mock_instance = AsyncMock()
            mock_client.return_value = mock_instance

            await mongo_mgr.connect()

            assert mongo_mgr.is_connected
            mock_client.assert_called_once()
            mock_instance.admin.command.assert_called_once_with("ping")

    @pytest.mark.asyncio
    async def test_connection_with_tunnel(self, mongo_mgr, mock_settings):
        """Test MongoDB connection with SSH tunnel."""
        mock_settings.ssh_tunnel.enabled = True

        with (
            patch("core.database.mongo_db.tunnel_manager") as mock_tunnel_mgr,
            patch("core.database.mongo_db.AsyncIOMotorClient") as mock_client,
        ):
            # Mock tunnel
            mock_tunnel = MagicMock()
            mock_tunnel.is_active = True
            mock_tunnel.local_bind_port = 12345
            mock_tunnel_mgr.get_tunnel.return_value = mock_tunnel

            # Mock client
            mock_instance = AsyncMock()
            mock_client.return_value = mock_instance

            await mongo_mgr.connect()

            assert mongo_mgr.is_connected
            mock_client.assert_called_once()
            # Verify connection uses tunnel port
            call_args = mock_client.call_args[1]
            assert call_args["host"] == "localhost"
            assert call_args["port"] == 12345

    @pytest.mark.asyncio
    async def test_connection_failure(self, mongo_mgr, mock_settings):
        """Test MongoDB connection failure handling."""
        with patch("core.database.mongo_db.AsyncIOMotorClient") as mock_client:
            mock_instance = AsyncMock()
            mock_instance.admin.command.side_effect = Exception("Connection failed")
            mock_client.return_value = mock_instance

            with pytest.raises(ConnectionError, match="MongoDB connection failed"):
                await mongo_mgr.connect()

            assert not mongo_mgr.is_connected

    @pytest.mark.asyncio
    async def test_get_client_not_connected(self, mongo_mgr):
        """Test getting client when not connected."""
        with pytest.raises(RuntimeError, match="MongoDB client not initialized"):
            mongo_mgr.get_client()

    @pytest.mark.asyncio
    async def test_get_database(self, mongo_mgr, mock_settings):
        """Test getting database instance."""
        with patch("core.database.mongo_db.AsyncIOMotorClient") as mock_client:
            mock_instance = AsyncMock()
            mock_client.return_value = mock_instance

            await mongo_mgr.connect()

            # Test with default database name
            db = mongo_mgr.get_database()
            assert db is not None

            # Test with custom database name
            db = mongo_mgr.get_database("custom_db")
            assert db is not None

    @pytest.mark.asyncio
    async def test_health_check(self, mongo_mgr, mock_settings):
        """Test MongoDB health check."""
        # Test when not connected
        assert not await mongo_mgr.health_check()

        # Test when connected and healthy
        with patch("core.database.mongo_db.AsyncIOMotorClient") as mock_client:
            mock_instance = AsyncMock()
            mock_client.return_value = mock_instance

            await mongo_mgr.connect()
            assert await mongo_mgr.health_check()

            # Test when connected but unhealthy
            mock_instance.admin.command.side_effect = Exception("Health check failed")
            assert not await mongo_mgr.health_check()

    @pytest.mark.asyncio
    async def test_disconnect(self, mongo_mgr, mock_settings):
        """Test MongoDB disconnection."""
        with patch("core.database.mongo_db.AsyncIOMotorClient") as mock_client:
            mock_instance = AsyncMock()
            mock_client.return_value = mock_instance

            await mongo_mgr.connect()
            assert mongo_mgr.is_connected

            await mongo_mgr.disconnect()
            assert not mongo_mgr.is_connected
            mock_instance.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_context_managers(self, mongo_mgr, mock_settings):
        """Test MongoDB context managers."""
        with patch("core.database.mongo_db.AsyncIOMotorClient") as mock_client:
            mock_instance = AsyncMock()
            mock_client.return_value = mock_instance

            # Test connection context
            async with mongo_mgr.connection_context() as client:
                assert client is mock_instance

            # Test database context
            async with mongo_mgr.database_context("testdb") as db:
                assert db is not None


class TestLegacyCompatibility:
    """Test legacy function compatibility."""

    @pytest.mark.asyncio
    async def test_legacy_functions_work(self):
        """Test that legacy functions still work but show deprecation warnings."""
        from core.database.mongo_db import (
            connect_to_mongo,
            close_mongo_connection,
            get_mongo_db_client,
            get_mongo_database,
        )

        with patch("core.database.mongo_db.mongo_manager") as mock_manager:
            mock_manager.connect = AsyncMock()
            mock_manager.disconnect = AsyncMock()
            mock_manager.get_client.return_value = MagicMock()
            mock_manager.get_database.return_value = MagicMock()

            # Test legacy functions
            await connect_to_mongo()
            mock_manager.connect.assert_called_once()

            await close_mongo_connection()
            mock_manager.disconnect.assert_called_once()

            get_mongo_db_client()
            mock_manager.get_client.assert_called_once()

            get_mongo_database("testdb")
            mock_manager.get_database.assert_called_once_with("testdb")


@pytest.mark.asyncio
async def test_integration_example():
    """Test integration between tunnel manager and MongoDB manager."""
    with (
        patch("core.config.tunnel_manager.settings") as mock_tunnel_settings,
        patch("core.database.mongo_db.settings") as mock_mongo_settings,
        patch("core.config.tunnel_manager.SSHTunnelForwarder") as mock_forwarder,
        patch("core.database.mongo_db.AsyncIOMotorClient") as mock_client,
        patch("core.config.tunnel_manager.Path") as mock_path,
    ):
        # Setup mocks
        mock_tunnel_settings.ssh_tunnel.enabled = True
        mock_tunnel_settings.ssh_tunnel.host = "test.example.com"
        mock_tunnel_settings.ssh_tunnel.username = "testuser"
        mock_tunnel_settings.ssh_tunnel.private_key_path = "/tmp/test_key"
        mock_tunnel_settings.database.mongo_host = "mongo.example.com"
        mock_tunnel_settings.database.mongo_port = 27017

        mock_mongo_settings.ssh_tunnel.enabled = True
        mock_mongo_settings.database.mongo_host = "mongo.example.com"
        mock_mongo_settings.database.mongo_port = 27017
        mock_mongo_settings.database.mongo_database = "testdb"

        mock_path.return_value.exists.return_value = True
        mock_path.return_value.is_file.return_value = True

        mock_tunnel = MagicMock()
        mock_tunnel.is_active = True
        mock_tunnel.local_bind_port = 12345
        mock_forwarder.return_value = mock_tunnel

        mock_mongo_client = AsyncMock()
        mock_client.return_value = mock_mongo_client

        # Test integration
        tunnel_mgr = SSHTunnelManager()
        mongo_mgr = MongoDBManager()

        # Start tunnel
        await tunnel_mgr.start()
        assert tunnel_mgr.is_active

        # Connect to MongoDB through tunnel
        await mongo_mgr.connect()
        assert mongo_mgr.is_connected

        # Verify MongoDB uses tunnel port
        call_args = mock_client.call_args[1]
        assert call_args["host"] == "localhost"
        assert call_args["port"] == 12345

        # Cleanup
        await mongo_mgr.disconnect()
        await tunnel_mgr.stop()
