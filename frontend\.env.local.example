# Local Development Environment Variables
# Copy this file to .env.local and customize for your local setup

# API Configuration
VITE_API_BASE_URL=http://localhost:8000/api/v1

# Mock API Configuration (set to false to use real API)
VITE_MOCK_API=true
VITE_MOCK_DELAY=300

# Application Configuration
VITE_APP_TITLE=Community Services Admin (Local)
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=local

# Debug Configuration
VITE_DEBUG=true
VITE_LOG_LEVEL=debug

# Feature Flags
VITE_FEATURE_ANALYTICS=true
VITE_FEATURE_COMPETITIONS=true
VITE_FEATURE_CREDITS=true
VITE_FEATURE_SCHOOLS=true

# Optional: Override specific API endpoints
# VITE_AUTH_API_URL=http://localhost:8001/auth
# VITE_ANALYTICS_API_URL=http://localhost:8002/analytics
