from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import SQLAlchemyError
from functools import wraps
from dotenv import load_dotenv

load_dotenv()

engine_uri = "mysql+pymysql://developer:dick902815@129.211.170.79:3306/heywhale"
# MySQL connection string
mysql_engine = create_engine(engine_uri)
mysql_session = sessionmaker(bind=mysql_engine)


def mysql_operation(messages=None):
    if messages is None:
        messages = {}

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            session = mysql_session()
            try:
                result = func(session, *args, **kwargs)
                session.commit()
                return result
            except SQLAlchemyError as e:
                session.rollback()
                error_type = type(e).__name__
                custom_message = messages.get(error_type, f"Error in {func.__name__}")
                raise Exception(f"{custom_message}: {e}")
            finally:
                session.close()

        return wrapper

    return decorator


# def admin_logger(admin=None,action_type=None):
#     if admin is None:
#         raise Exception('Requires admin')
#     def decorator(func):
#         @wraps(func)
#         def wrapper(*args, **kwargs):
#             result = func(*args,**kwargs)
#             entry = {"record_id":str(ObjectId()),
#                      "action_type":action_type,
#                      "admin":admin}
#             return result
#         return wrapper
#     return decorator
