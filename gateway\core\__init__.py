"""
Gateway Core Infrastructure.

Backward compatibility layer for gateway.core imports.
All core infrastructure has been moved to the root /core directory.
"""

# Import from the new core location for backward compatibility
from core.database import (
    get_db_session,
    get_async_db_session,
    get_read_only_session,
    get_transaction,
    get_transaction_session,
    get_redis_connection,
    get_cache_client,
    get_connection_pool,
    get_database_service,
    get_event_tracking_service,
    check_database_health,
    check_redis_health,
    DatabasePool,
)

from core.monitoring import (
    get_system_metrics,
    get_comprehensive_health_check,
    check_external_services_health,
)

from core import messaging
from core import mcp_server

__all__ = [
    # Database
    "get_db_session",
    "get_async_db_session",
    "get_read_only_session",
    "get_transaction",
    "get_transaction_session",
    "get_redis_connection",
    "get_cache_client",
    "get_connection_pool",
    "get_database_service",
    "get_event_tracking_service",
    "check_database_health",
    "check_redis_health",
    "DatabasePool",
    # Monitoring
    "get_system_metrics",
    "get_comprehensive_health_check",
    "check_external_services_health",
    # Messaging
    "messaging",
    # MCP Server
    "mcp_server",
]
