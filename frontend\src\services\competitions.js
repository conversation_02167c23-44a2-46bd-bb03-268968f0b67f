import api from './api'

/**
 * Competition API service
 * Handles all competition-related API calls
 */
export const competitionService = {
  /**
   * Get list of competitions
   * @param {Object} params - Query parameters
   * @returns {Promise} API response
   */
  async getCompetitions(params = {}) {
    try {
      const response = await api.get('/v1/community/qualification/competitions/list', {
        params
      })
      return response.data
    } catch (error) {
      console.error('Error fetching competitions:', error)
      throw error
    }
  },

  /**
   * Get competition details by ID
   * @param {string|number} id - Competition ID
   * @returns {Promise} API response
   */
  async getCompetition(id) {
    try {
      const response = await api.get(`/v1/community/qualification/competitions/${id}`)
      return response.data
    } catch (error) {
      console.error('Error fetching competition:', error)
      throw error
    }
  },

  /**
   * Create a new competition
   * @param {Object} competitionData - Competition data
   * @returns {Promise} API response
   */
  async createCompetition(competitionData) {
    try {
      const response = await api.post('/v1/community/qualification/competitions/add', competitionData)
      return response.data
    } catch (error) {
      console.error('Error creating competition:', error)
      throw error
    }
  },

  /**
   * Update competition
   * @param {string|number} id - Competition ID
   * @param {Object} competitionData - Updated competition data
   * @returns {Promise} API response
   */
  async updateCompetition(id, competitionData) {
    try {
      const response = await api.put(`/v1/community/qualification/competitions/${id}`, competitionData)
      return response.data
    } catch (error) {
      console.error('Error updating competition:', error)
      throw error
    }
  },

  /**
   * Delete competition
   * @param {string|number} id - Competition ID
   * @returns {Promise} API response
   */
  async deleteCompetition(id) {
    try {
      const response = await api.delete(`/v1/community/qualification/competitions/${id}`)
      return response.data
    } catch (error) {
      console.error('Error deleting competition:', error)
      throw error
    }
  },

  /**
   * Get qualifications for a competition
   * @param {string|number} competitionId - Competition ID
   * @returns {Promise} API response
   */
  async getQualifications(competitionId) {
    try {
      const response = await api.get('/v1/community/qualification/competitions/qualifications', {
        params: { competition_id: competitionId }
      })
      return response.data
    } catch (error) {
      console.error('Error fetching qualifications:', error)
      throw error
    }
  },

  /**
   * Add qualification rules to a competition
   * @param {Object} qualificationData - Qualification data
   * @returns {Promise} API response
   */
  async addQualification(qualificationData) {
    try {
      const response = await api.post('/v1/community/qualification/competitions/add', qualificationData)
      return response.data
    } catch (error) {
      console.error('Error adding qualification:', error)
      throw error
    }
  },

  /**
   * Get available routes for competitions
   * @returns {Promise} API response
   */
  async getRoutes() {
    try {
      const response = await api.get('/v1/community/qualification/competitions/routes')
      return response.data
    } catch (error) {
      console.error('Error fetching routes:', error)
      throw error
    }
  },

  /**
   * Get available tasks for competitions
   * @param {Object} params - Query parameters
   * @returns {Promise} API response
   */
  async getTasks(params = {}) {
    try {
      const response = await api.get('/v1/community/qualification/competitions/tasks', {
        params
      })
      return response.data
    } catch (error) {
      console.error('Error fetching tasks:', error)
      throw error
    }
  },

  /**
   * Get competition statistics
   * @param {string|number} competitionId - Competition ID
   * @returns {Promise} API response
   */
  async getCompetitionStats(competitionId) {
    try {
      const response = await api.get(`/v1/community/qualification/competitions/${competitionId}/stats`)
      return response.data
    } catch (error) {
      console.error('Error fetching competition stats:', error)
      throw error
    }
  }
}

export default competitionService
