from sqlalchemy.orm import DeclarativeBase
from sqlalchemy import Column, String, TIMESTAMP, Integer
from bson import ObjectId


class Base(DeclarativeBase):
    pass


class Adminstrators(Base):
    __tablename__ = "administrators"

    email_address = Column(String(255), primary_key=True)
    password = Column(String(255), nullable=False)
    user_name = Column(String(255), nullable=True)
    last_login_ts = Column(TIMESTAMP(3), nullable=True)


class AdminLogs(Base):
    __tablename__ = "admin_logs"
    record_id = Column(String(255), primary_key=True, default=str(ObjectId()))
    admin = Column(String(255), nullable=False)
    action_type = Column(Integer, nullable=False)
    action_key = Column(
        String(255),
    )
    action_value = Column(
        String(255),
    )
    create_ts = Column(
        TIMESTAMP(3),
        nullable=False,
    )
    update_ts = Column(
        TIMESTAMP(3),
        nullable=False,
    )
