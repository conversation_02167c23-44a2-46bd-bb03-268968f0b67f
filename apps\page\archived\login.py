import streamlit as st
from libs.utils import verify_login
from datetime import datetime, timedelta
from pages.register import register_pg


def app():
    cookies = st.session_state.cookies
    st.title("社区管理员系统-夏令营")
    login_form = st.form("login_form")

    email = login_form.text_input("email", type="default", placeholder="登录邮箱")
    password = login_form.text_input(
        "Password", type="password", placeholder="登录密码"
    )
    login_button = login_form.form_submit_button("登录", use_container_width=True)
    st.page_link(register_pg, label="没有账号？点击注册", icon="🤔")

    if login_button:
        message, is_valid = verify_login(email, password)
        if not is_valid:
            st.error(message)
        else:
            st.success("登录成功！")
            st.session_state.logged_in = True
            st.session_state.username = email
            st.session_state.logged_in = True
            cookies["logged_in"] = "true"
            cookies["username"] = email
            expiration_time = datetime.now() + timedelta(days=365)
            cookies["expired_at"] = str(expiration_time.timestamp())
            cookies.save()

            st.rerun()
    st.session_state.cookies = cookies


login_pg = st.Page(app, title="登录页面", url_path="logins")
