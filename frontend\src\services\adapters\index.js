/**
 * API Response Adapters
 * Transform API responses to match frontend data models
 */

// Import base adapters
export { BaseAdapter, GenericAdapter, AdapterRegistry } from './BaseAdapter.js'

// Import all adapters
export { universityAdapter } from './universityAdapter.js'
export { competitionAdapter } from './competitionAdapter.js'
export { creditAdapter } from './creditAdapter.js'
export { userAdapter } from './userAdapter.js'
export { analyticsAdapter } from './analyticsAdapter.js'
// Auto-register adapters when they're imported
// This will be done in each adapter file
