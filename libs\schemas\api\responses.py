"""
Standardized API response schemas and utilities.
API 层级的响应格式

This module provides consistent response formats for all API endpoints:
- Success responses
- Error responses
- Ant Design table compatible responses
"""

from typing import List, Any, Dict, Optional
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field


class BaseResponse(BaseModel):
    """Base response model for all API responses."""

    code: int = Field(description="Response status code")
    msg: str = Field(description="Response message")
    data: Any = Field(description="Response data")


class SuccessResponse(BaseResponse):
    """Success response model."""

    code: int = Field(default=200, description="Success status code")
    msg: str = Field(default="", description="Success message")


class ErrorResponse(BaseResponse):
    """Error response model."""

    code: int = Field(description="Error status code")
    msg: str = Field(description="Error message")
    data: Optional[str | Dict[str,Any]] = Field(default="", description="Error details")
    error: Optional[str] = Field(default="", description="Error details")


class AntdTableResponse(BaseModel):
    """Ant Design table compatible response format."""

    success: bool = Field(description="Operation success status")
    data: List[Any] = Field(description="Table data")
    total: int = Field(description="Total record count")


def base_response(code: int, msg: str, data: Any = None) -> dict:
    """
    Create a base response dictionary.

    Args:
        code: Response status code
        msg: Response message
        data: Response data (defaults to empty list if None)

    Returns:
        Dictionary with standardized response format
    """
    if data is None:
        data = []
    return {"code": code, "msg": msg, "data": data}


def success(data: Any = None, msg: str = "") -> dict:
    """
    Create a success response.

    Args:
        data: Response data
        msg: Success message

    Returns:
        Success response dictionary
    """
    return base_response(200, msg, data)


def fail(
    code: int = -1, msg: str = "", data: str = "", status_code: int = 500
) -> JSONResponse:
    """
    Create a failure response.

    Args:
        code: Error code
        msg: Error message
        data: Error details (default empty string for 500 errors)
        status_code: HTTP status code

    Returns:
        JSONResponse with error details
    """
    response_data = base_response(code, msg, data)
    return JSONResponse(response_data, status_code=status_code)


def res_antd(
    data: List[Any] = None, total: int = 0, success_status: bool = True
) -> dict:
    """
    Create an Ant Design table compatible response.

    Args:
        data: Table data
        total: Total record count
        success_status: Operation success status

    Returns:
        Ant Design compatible response dictionary
    """
    if data is None:
        data = []
    return {"success": success_status, "data": data, "total": total}
