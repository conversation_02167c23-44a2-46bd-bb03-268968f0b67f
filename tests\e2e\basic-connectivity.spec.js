/**
 * Basic connectivity test for localhost:3000
 * This test checks if the service is running and accessible
 */

import { test, expect } from '@playwright/test'

test.describe('Basic Connectivity Tests', () => {
  
  test('should be able to connect to localhost:3000', async ({ page }) => {
    try {
      // Try to navigate to the base URL
      const response = await page.goto('http://localhost:3000', { 
        waitUntil: 'networkidle',
        timeout: 10000 
      })
      
      // Check if we got a response
      if (response) {
        console.log(`Response status: ${response.status()}`)
        console.log(`Response URL: ${response.url()}`)
        
        // Take a screenshot regardless of status
        await page.screenshot({ 
          path: 'tests/screenshots/connectivity-test.png',
          fullPage: true 
        })
        
        // Check if the page loaded (any 2xx or 3xx status is acceptable)
        expect(response.status()).toBeLessThan(400)
        
        // Check if we have some content
        const bodyText = await page.textContent('body')
        expect(bodyText).toBeTruthy()
        
        console.log('✅ Successfully connected to localhost:3000')
        console.log(`Page title: ${await page.title()}`)
        
      } else {
        throw new Error('No response received')
      }
      
    } catch (error) {
      console.log('❌ Failed to connect to localhost:3000')
      console.log(`Error: ${error.message}`)
      
      // Take a screenshot of the error state
      await page.screenshot({ 
        path: 'tests/screenshots/connection-error.png',
        fullPage: true 
      })
      
      // Re-throw the error to fail the test
      throw error
    }
  })

  test('should check if Vue app is mounted', async ({ page }) => {
    try {
      await page.goto('http://localhost:3000', { timeout: 10000 })
      
      // Wait a bit for Vue to mount
      await page.waitForTimeout(2000)
      
      // Check for Vue app container
      const appContainer = page.locator('#app')
      
      if (await appContainer.count() > 0) {
        await expect(appContainer).toBeVisible()
        console.log('✅ Vue app container found and visible')
        
        // Check if there's any content in the app
        const appContent = await appContainer.textContent()
        if (appContent && appContent.trim().length > 0) {
          console.log('✅ Vue app has content')
          console.log(`Content preview: ${appContent.substring(0, 100)}...`)
        } else {
          console.log('⚠️ Vue app container is empty')
        }
      } else {
        console.log('❌ Vue app container (#app) not found')
      }
      
      await page.screenshot({ path: 'tests/screenshots/vue-app-check.png' })
      
    } catch (error) {
      console.log(`❌ Error checking Vue app: ${error.message}`)
      await page.screenshot({ path: 'tests/screenshots/vue-app-error.png' })
      throw error
    }
  })

  test('should check for Element Plus components', async ({ page }) => {
    try {
      await page.goto('http://localhost:3000', { timeout: 10000 })
      await page.waitForTimeout(3000)
      
      // Look for common Element Plus class names
      const elementPlusSelectors = [
        '.el-button',
        '.el-card',
        '.el-input',
        '.el-menu',
        '.el-header',
        '.el-main',
        '.el-container'
      ]
      
      let foundComponents = 0
      const foundSelectors = []
      
      for (const selector of elementPlusSelectors) {
        const elements = page.locator(selector)
        const count = await elements.count()
        if (count > 0) {
          foundComponents++
          foundSelectors.push(`${selector} (${count})`)
          console.log(`✅ Found ${count} ${selector} elements`)
        }
      }
      
      console.log(`Found ${foundComponents} different Element Plus component types`)
      console.log(`Components found: ${foundSelectors.join(', ')}`)
      
      await page.screenshot({ path: 'tests/screenshots/element-plus-check.png' })
      
      // We expect at least some Element Plus components if the app is working
      if (foundComponents > 0) {
        console.log('✅ Element Plus components detected')
      } else {
        console.log('⚠️ No Element Plus components found - app might not be fully loaded')
      }
      
    } catch (error) {
      console.log(`❌ Error checking Element Plus: ${error.message}`)
      await page.screenshot({ path: 'tests/screenshots/element-plus-error.png' })
      // Don't throw error here as this is more of an informational test
    }
  })

  test('should check network requests', async ({ page }) => {
    const requests = []
    const responses = []
    
    // Monitor network activity
    page.on('request', request => {
      requests.push({
        url: request.url(),
        method: request.method(),
        resourceType: request.resourceType()
      })
    })
    
    page.on('response', response => {
      responses.push({
        url: response.url(),
        status: response.status(),
        statusText: response.statusText()
      })
    })
    
    try {
      await page.goto('http://localhost:3000', { timeout: 10000 })
      await page.waitForTimeout(3000)
      
      console.log(`📊 Network Activity Summary:`)
      console.log(`Total requests: ${requests.length}`)
      console.log(`Total responses: ${responses.length}`)
      
      // Check for failed requests
      const failedResponses = responses.filter(r => r.status >= 400)
      if (failedResponses.length > 0) {
        console.log(`❌ Failed requests (${failedResponses.length}):`)
        failedResponses.forEach(r => {
          console.log(`  ${r.status} ${r.statusText}: ${r.url}`)
        })
      } else {
        console.log('✅ No failed requests detected')
      }
      
      // Check for API calls
      const apiRequests = requests.filter(r => 
        r.url.includes('/api/') || 
        r.url.includes('localhost:5000') ||
        r.url.includes('localhost:8000')
      )
      
      if (apiRequests.length > 0) {
        console.log(`🔗 API requests detected (${apiRequests.length}):`)
        apiRequests.forEach(r => {
          console.log(`  ${r.method} ${r.url}`)
        })
      } else {
        console.log('ℹ️ No API requests detected')
      }
      
      await page.screenshot({ path: 'tests/screenshots/network-activity.png' })
      
    } catch (error) {
      console.log(`❌ Error monitoring network: ${error.message}`)
      await page.screenshot({ path: 'tests/screenshots/network-error.png' })
      throw error
    }
  })

  test('should provide diagnostic information', async ({ page }) => {
    try {
      console.log('🔍 Running diagnostic checks...')
      
      // Check if port 3000 is accessible
      const response = await page.goto('http://localhost:3000', { 
        waitUntil: 'domcontentloaded',
        timeout: 5000 
      })
      
      if (response) {
        console.log(`✅ Port 3000 is accessible`)
        console.log(`Response status: ${response.status()}`)
        console.log(`Content type: ${response.headers()['content-type'] || 'unknown'}`)
        
        // Get page info
        const title = await page.title()
        const url = page.url()
        
        console.log(`Page title: "${title}"`)
        console.log(`Final URL: ${url}`)
        
        // Check for JavaScript errors
        const errors = []
        page.on('pageerror', error => {
          errors.push(error.message)
        })
        
        await page.waitForTimeout(2000)
        
        if (errors.length > 0) {
          console.log(`❌ JavaScript errors detected (${errors.length}):`)
          errors.forEach(error => console.log(`  ${error}`))
        } else {
          console.log('✅ No JavaScript errors detected')
        }
        
        await page.screenshot({ 
          path: 'tests/screenshots/diagnostic-info.png',
          fullPage: true 
        })
        
      } else {
        console.log('❌ Port 3000 is not accessible')
      }
      
    } catch (error) {
      console.log(`❌ Diagnostic failed: ${error.message}`)
      
      // Check if it's a connection error
      if (error.message.includes('ECONNREFUSED') || error.message.includes('net::ERR_CONNECTION_REFUSED')) {
        console.log('💡 Suggestion: Make sure the development server is running with "npm run dev"')
      }
      
      await page.screenshot({ path: 'tests/screenshots/diagnostic-error.png' })
      throw error
    }
  })
})
