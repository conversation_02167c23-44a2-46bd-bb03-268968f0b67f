"""
Analytics domain-specific models and data structures.

Contains models and data classes specific to the analytics domain
that are not shared across the application.
"""

from typing import Optional, Dict, Any
from dataclasses import dataclass
from datetime import date
from enum import Enum


class RankingType(str, Enum):
    """Ranking type enumeration."""

    TOTAL = "total"
    ROUTE_SPECIFIC = "route_specific"
    SCHOOL = "school"
    MONTHLY = "monthly"
    WEEKLY = "weekly"


class StatisticsPeriod(str, Enum):
    """Statistics period enumeration."""

    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    YEARLY = "yearly"
    ALL_TIME = "all_time"


@dataclass
class RankingQuery:
    """Query parameters for ranking operations."""

    user_id: Optional[str] = None
    route_id: Optional[str] = None
    ranking_type: RankingType = RankingType.TOTAL
    limit: int = 100
    offset: int = 0


@dataclass
class DateRange:
    """Date range for analytics queries."""

    start_date: date
    end_date: date

    def __post_init__(self):
        if self.start_date > self.end_date:
            raise ValueError("Start date must be before end date")


@dataclass
class RankingResult:
    """Ranking result data structure."""

    user_id: str
    rank: int
    score: float
    route_id: Optional[str] = None
    school_name: Optional[str] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class StatisticsQuery:
    """Query parameters for statistics operations."""

    user_id: Optional[str] = None
    period: StatisticsPeriod = StatisticsPeriod.ALL_TIME
    date_range: Optional[DateRange] = None
    include_details: bool = False


# TODO: Add other domain-specific models as needed
