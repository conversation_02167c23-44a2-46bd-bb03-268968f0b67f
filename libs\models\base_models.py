# -*- coding:utf-8 -*-
from tortoise import fields
from tortoise.models import Model


class TimestampMixin(Model):
    """
    创建时间和更新时间的model, 专用于继承
    """

    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        abstract = True


class QualifiedUser(Model):  # Modified existing model based on CS Ranking
    user_id = fields.CharField(
        max_length=255, db_index=True
    )  # Made db_index based on CS
    qualification_id = fields.Char<PERSON><PERSON>(
        max_length=255, db_index=True
    )  # Made db_index based on CS

    qualification_name = fields.Char<PERSON><PERSON>(
        max_length=255, null=True
    )  # Made nullable from CS
    credit = fields.IntField(default=0)  # Added default from CS
    task_id = fields.CharField(max_length=255)
    competition_id = fields.Char<PERSON><PERSON>(max_length=255)  # Consider FK if appropriate
    team_id = fields.Cha<PERSON><PERSON><PERSON>(max_length=255)
    qualified_date = fields.DatetimeField()
    user_name = fields.Char<PERSON><PERSON>(max_length=255)
    university = fields.Char<PERSON><PERSON>(max_length=255, null=True, default="")  # From CS
    update_ts = fields.DatetimeField(
        auto_now=True
    )  # Changed from simple DatetimeField to auto_now for updates

    class Meta:
        table = "qualified_users_triggered"
        table_description = "View of users who met qualification criteria"  # Added
        # Tortoise ORM needs a field designated as PK for its operations,
        # even if the underlying DB object is a view without a traditional PK.
        # If no single field is a PK, this model might be read-only or require careful handling.
        # For now, no explicit pk=True is set, Tortoise might default to an 'id' field if it generates schema for this.
        # If this table is actually a table and not a view, one of these should be pk=True or a new id pk added.
        # Adding unique_together as per CS composite PK
        unique_together = (("user_id", "qualification_id"),)


class User(Model):
    register_id = fields.CharField(
        max_length=255, pk=True
    )  # Was PK in CS, now unique field
    user_id = fields.CharField(
        max_length=255,db_index=True
    )  # Confirmed PK, added index

    user_name = fields.CharField(
        max_length=255, null=True
    )  # From SC User and CS UserInfos, made nullable
    university = fields.CharField(
        max_length=255, null=True, db_index=True, default=""
    )  # Merged properties
    competition_id = fields.CharField(
        max_length=255, null=True, db_index=True
    )  # Merged properties (kept nullable)

    # Fields from CS UserInfos
    phone = fields.CharField(max_length=255, null=True)
    email = fields.CharField(
        max_length=255, null=True, unique=True
    )  # Email should ideally be unique

    class Meta:
        table = "users"
        table_description = "User accounts"  # Added


class Qualification(Model):
    qualification_id = fields.CharField(max_length=255, pk=True)
    qualification_name = fields.CharField(max_length=255, null=True)
    competition_id = fields.CharField(max_length=255, db_index=True)
    qualification_type = fields.IntField(null=True, default=1)
    qualification_logic = fields.IntField(null=True, default=1)
    related_task_id = fields.CharField(max_length=255, null=True, db_index=True)
    score_threshold = fields.FloatField(null=True)
    deleted = fields.BooleanField(null=True, default=False, db_index=True)
    credit = fields.IntField(null=True)

    class Meta:
        table = "qualifications"
        table_description = "Qualification criteria for competitions"


class Competition(Model):
    record_id = fields.CharField(max_length=255, pk=True)
    competition_id = fields.CharField(max_length=255, db_index=True, unique=True)
    route = fields.ForeignKeyField(
        "models.Route",
        related_name="competitions",
        to_field="route_id",
        on_delete=fields.RESTRICT,
        db_index=True,
    )
    route_name = fields.CharField(max_length=255)
    competition_name = fields.CharField(max_length=255, null=True)

    class Meta:
        table = "competitions"
        table_description = "Competition details"


class Route(Model):
    route_id = fields.CharField(max_length=255, pk=True)
    route_name = fields.CharField(max_length=255, null=True)
    info = fields.CharField(max_length=255, null=True)

    class Meta:
        table = "routes"
        table_description = "Competition routes or tracks"


class Submission(Model):
    record_id = fields.CharField(max_length=255, pk=True)
    submission_ts = fields.DatetimeField()
    score = fields.IntField(null=True)

    team = fields.CharField(
        max_length=255,
    )  # Does not involve the actual team model
    task_id = fields.CharField(max_length=255, db_index=True)

    competition = fields.ForeignKeyField(
        "models.Competition",
        related_name="submissions",
        to_field="competition_id",
        on_delete=fields.CASCADE,
        db_index=True,
    )

    class Meta:
        table = "submissions"
        table_description = "User submissions for tasks/competitions"


class Teams(Model):
    membership_id = fields.CharField(max_length=255, pk=True)
    team_id = fields.CharField(max_length=255, db_index=True)
    user_id = fields.CharField(max_length=255, db_index=True)
    competition_id = fields.CharField(max_length=255, db_index=True)

    update_ts = fields.DatetimeField(null=True)

    # class Meta:
    #     table = "teams"
    #     table_description = "Team memberships"


class AccessLog(TimestampMixin):
    user_id = fields.CharField(max_length=255, description="用户ID")
    target_url = fields.CharField(null=True, description="访问的url", max_length=255)
    user_agent = fields.CharField(null=True, description="访问UA", max_length=255)
    request_params = fields.JSONField(null=True, description="请求参数get|post")
    ip = fields.CharField(null=True, max_length=32, description="访问IP")
    note = fields.CharField(null=True, max_length=255, description="备注")

    class Meta:
        table_description = "用户操作记录表"
        table = "access_log"


# TODO: Add translated SQLAlchemy models from community-services here

# Models translated from community-services/src/models/admins.py


class Administrators(Model):  # Corrected typo from Adminstrators
    email_address = fields.CharField(max_length=255, pk=True)
    password = fields.CharField(max_length=255)
    user_name = fields.CharField(max_length=255, null=True)
    last_login_ts = fields.DatetimeField(null=True)  # DatetimeField in Tortoise

    class Meta:
        table = "administrators"
        table_description = "Site administrators"


class AdminLogs(
    TimestampMixin, Model
):  # Inherit TimestampMixin for create_ts, update_ts
    # record_id = fields.CharField(max_length=255, pk=True) # Original
    record_id = fields.CharField(
        max_length=255,
        pk=True
    )  # Using UUID as a more standard PK for new records
    # admin_email = fields.ForeignKeyField(  # Assuming 'admin' field was email
    #     "models.Administrators",
    #     related_name="logs",
    #     to_field="email_address",
    #     on_delete=fields.CASCADE,
    # )       
    admin_email_id = fields.CharField(max_length=255, null=True)
    action_type = fields.IntField()
    action_related_id = fields.CharField(max_length=255, null=True)
    remark = fields.CharField(max_length=255, null=True)
    # create_ts and update_ts are handled by TimestampMixin

    class Meta:
        table = "admin_logs"
        table_description = "Logs of administrator actions"


# Model translated from community-services/src/models/credit_history.py
class CreditHistory(TimestampMixin, Model):  # Inherits create_time, update_time
    record_id = fields.CharField(max_length=255, pk=True)  # Changed from String(255) PK
    credit = fields.IntField(default=0)

    user = fields.ForeignKeyField(
        "models.User",
        related_name="credit_history",
        on_delete=fields.CASCADE,
        db_index=True,
    )
    # Storing competition_id directly as per original, or could link to Competition model
    # If linking:
    # competition = fields.ForeignKeyField(
    #     "models.Competition", related_name="credit_history", to_field="competition_id", on_delete=fields.CASCADE, db_index=True
    # )
    # For now, keeping it as CharField to match original schema more closely if competition_id might not always exist in competitions table:
    competition_id = fields.CharField(max_length=255, db_index=True)

    # Storing qualification_id directly or link:
    # If linking:
    # qualification = fields.ForeignKeyField(
    #     "models.Qualification", related_name="credit_history", to_field="qualification_id", null=True, on_delete=fields.SET_NULL, db_index=True
    # )
    qualification_id = fields.CharField(max_length=255, null=True, db_index=True)

    remark = fields.CharField(max_length=255, null=True)
    batch_id = fields.CharField(max_length=255, null=True, db_index=True)
    deleted = fields.BooleanField(default=False, db_index=True)

    # current_ts from original maps to create_time from TimestampMixin
    # update_ts from original maps to update_time from TimestampMixin

    class Meta:
        table = "credit_history"
        table_description = "Record of user credit changes"


# Model translated from community-services/src/models/statistics.py (Statistics part)
class Statistics(TimestampMixin, Model):
    record_id = fields.UUIDField(pk=True)
    statistics_name = fields.CharField(max_length=255, db_index=True)
    statistics_value = fields.IntField(null=True, default=0)
    statistics_related_id = fields.CharField(max_length=255, null=True, db_index=True)
    remark = fields.CharField(max_length=255, null=True)
    # create_ts and update_ts from TimestampMixin

    class Meta:
        table = "statistics"
        table_description = "General statistics records"


# Model for user rankings in competitions
class Ranking(TimestampMixin, Model):
    record_id = fields.UUIDField(pk=True)
    user_id = fields.CharField(max_length=255, db_index=True)
    competition_id = fields.CharField(max_length=255, db_index=True)
    qualification_id = fields.CharField(max_length=255, db_index=True, null=True)
    route_id = fields.CharField(max_length=255, db_index=True, null=True)
    credit = fields.IntField(default=0)
    rank = fields.IntField(null=True)
    timestamp = fields.DatetimeField(auto_now_add=True)

    class Meta:
        table = "rankings"
        table_description = "User rankings in competitions"
        unique_together = (("user_id", "competition_id", "qualification_id"),)


class University(TimestampMixin):
    id = fields.IntField(pk=True, description="学校ID")
    name = fields.CharField(max_length=255, unique=True, description="学校名称")
    # Add other relevant fields like location, type, etc. if needed

    class Meta:
        table = "universities"
        ordering = ["name"]

    def __str__(self):
        return self.name


class Major(TimestampMixin):
    id = fields.IntField(pk=True, description="专业ID")
    name = fields.CharField(max_length=255, unique=True, description="专业名称")
    # Add other relevant fields like description, faculty, etc. if needed

    class Meta:
        table = "majors"
        ordering = ["name"]

    def __str__(self):
        return self.name


# Ensure all your models are listed in __all__ if you have one, or are discoverable.
# For example, if you have a base file that imports all models:
# from .user import User
# from .competition import Competition
# etc.
#
# Or, if Aerich is configured to find models in this file directly, ensure they are defined here.
