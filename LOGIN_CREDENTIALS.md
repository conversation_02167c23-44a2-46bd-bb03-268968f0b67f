# 🔐 Admin Login Credentials

## Admin Test Accounts for Development

The Community Services Administrative System is an **ADMIN-ONLY** platform. Only administrators can access this system.

### Available Admin Test Accounts

| Role | Email | Password | Access Level |
|------|-------|----------|--------------|
| **Admin** | `<EMAIL>` | `admin123` | Full administrative access |
| **Super Admin** | `<EMAIL>` | `superadmin123` | Super admin with user management |

### Quick Login

1. **Open the application**: Navigate to `http://localhost:3000`
2. **Auto-redirect**: You'll be redirected to `/login` due to authentication guards
3. **Use test credentials**: Click any credential card on the login page to auto-fill
4. **Or manually enter**: Copy any email/password combination from the table above

### Recommended Test Flow

1. **Start with Admin**: Use `<EMAIL>` / `admin123` for full administrative access
2. **Test Super Admin**: Use `<EMAIL>` / `superadmin123` for user management features
3. **Explore Admin Features**: Navigate through competitions, credits, schools, and admin tools

### Important Notes

- ⚠️ **This is an ADMIN-ONLY system** - no public user registration
- 🔒 **Admin accounts only** - community users exist in separate systems
- 👥 **New admin accounts** can only be created by super administrators
- 🛡️ **Secure by design** - all access requires admin authentication

### Features Available After Login

- **Dashboard**: Overview with statistics and quick actions
- **Competition Management**: Create, edit, and manage competitions
- **Credit Distribution**: Manage credit allocation and distribution
- **School Statistics**: View and analyze school performance data
- **Admin Tools**: User management and system configuration (admin only)

### Development Notes

- **Mock API**: All authentication is handled by mock services
- **Persistent Login**: Sessions persist across browser refreshes
- **Role-based Access**: Different roles have different permissions
- **Real-time Updates**: Mock data updates in real-time during development

### Troubleshooting

If login fails:
1. Check that the development server is running on `localhost:3000`
2. Verify mock API is enabled (should show test credentials on login page)
3. Clear browser cookies and localStorage if needed
4. Check browser console for any JavaScript errors

### Security Note

⚠️ **These are development-only credentials**. In production, these test accounts should be removed and replaced with proper user management and authentication systems.
