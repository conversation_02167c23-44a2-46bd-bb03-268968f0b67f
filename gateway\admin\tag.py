"""
Tag Admin - CRUD operations for tag management.

Handles tag creation, updates, deletion, and user tag assignments with audit logging and transaction support.
"""

from typing import Dict, Any, Optional, List
import logging
from datetime import datetime
from bson import ObjectId

from gateway.admin.base import BaseAdmin
from libs.auth.context import AuthContext, require_auth
from libs.errors import AdminResult, AdminError

logger = logging.getLogger(__name__)


class TagAdmin(BaseAdmin):
    """Admin service for tag management operations."""

    def __init__(self):
        """Initialize tag admin service."""
        super().__init__()

    @require_auth
    async def create_tag(
        self, auth_context: AuthContext, tag_data: Dict[str, Any]
    ) -> AdminResult[Dict[str, Any]]:
        """
        Create a new tag with audit logging.

        Args:
            auth_context: Authentication context
            tag_data: Tag data including name, category, description, etc.

        Returns:
            AdminResult containing the created tag data
        """
        try:
            # Validate required fields
            required_fields = ["tag_name"]
            missing_fields = [
                field
                for field in required_fields
                if field not in tag_data or not tag_data[field]
            ]

            if missing_fields:
                return AdminError.validation_failed(
                    "required_fields",
                    f"Missing required fields: {', '.join(missing_fields)}",
                )

            # Validate tag name format
            tag_name = tag_data["tag_name"].strip()
            if len(tag_name) < 2 or len(tag_name) > 50:
                return AdminError.validation_failed(
                    "tag_name", "Tag name must be between 2 and 50 characters"
                )

            async with self.transaction_context() as session:
                # Check if tag already exists
                existing_tag = await self.db["tags"].find_one(
                    {"tag_name": tag_name, "deleted": {"$ne": True}}, session=session
                )

                if existing_tag:
                    return AdminError.validation_failed(
                        "tag_name", "Tag already exists"
                    )

                # Generate tag ID
                tag_id = str(ObjectId())

                # Prepare tag document
                tag_document = {
                    "tag_id": tag_id,
                    "tag_name": tag_name,
                    "category": tag_data.get("category", "general"),
                    "description": tag_data.get("description"),
                    "metadata": tag_data.get("metadata", {}),
                    "usage_count": 0,
                    "is_active": tag_data.get("is_active", True),
                    "deleted": False,
                    "created_at": datetime.now(),
                    "created_by": auth_context.user_id,
                    "updated_at": datetime.now(),
                    "updated_by": auth_context.user_id,
                }

                # Insert tag
                await self.db["tags"].insert_one(tag_document, session=session)

                # Log audit
                await self._log_audit(
                    auth_context,
                    "CREATE",
                    "tags",
                    tag_id,
                    {"tag_data": tag_data},
                    session,
                )

                logger.info(
                    f"Created tag {tag_id} ({tag_name}) by {auth_context.user_id}"
                )
                return AdminResult.success(tag_document)

        except Exception as e:
            logger.error(f"Failed to create tag: {e}")
            return AdminError.transaction_failed("create tag")

    @require_auth
    async def update_tag(
        self, auth_context: AuthContext, tag_id: str, updates: Dict[str, Any]
    ) -> AdminResult[Dict[str, Any]]:
        """
        Update an existing tag with audit logging.

        Args:
            auth_context: Authentication context
            tag_id: ID of the tag to update
            updates: Fields to update

        Returns:
            AdminResult containing the updated tag data
        """
        try:
            if not tag_id or not tag_id.strip():
                return AdminError.validation_failed("tag_id", "Tag ID is required")

            if not updates:
                return AdminError.validation_failed("updates", "No updates provided")

            # Validate tag name if provided
            if "tag_name" in updates:
                tag_name = updates["tag_name"].strip()
                if len(tag_name) < 2 or len(tag_name) > 50:
                    return AdminError.validation_failed(
                        "tag_name", "Tag name must be between 2 and 50 characters"
                    )

            async with self.transaction_context() as session:
                # Check if tag exists
                existing_tag = await self.db["tags"].find_one(
                    {"tag_id": tag_id, "deleted": {"$ne": True}}, session=session
                )

                if not existing_tag:
                    return AdminError.validation_failed("tag_id", "Tag not found")

                # Check for name conflicts if updating tag_name
                if "tag_name" in updates:
                    name_conflict = await self.db["tags"].find_one(
                        {
                            "tag_name": updates["tag_name"].strip(),
                            "tag_id": {"$ne": tag_id},
                            "deleted": {"$ne": True},
                        },
                        session=session,
                    )

                    if name_conflict:
                        return AdminError.validation_failed(
                            "tag_name", "Tag name already exists"
                        )

                # Prepare update data
                update_data = {}
                allowed_fields = [
                    "tag_name",
                    "category",
                    "description",
                    "metadata",
                    "is_active",
                ]

                for field, value in updates.items():
                    if field in allowed_fields:
                        if field == "tag_name":
                            update_data[field] = value.strip()
                        else:
                            update_data[field] = value

                if not update_data:
                    return AdminError.validation_failed(
                        "updates", "No valid fields to update"
                    )

                # Add metadata
                update_data["updated_at"] = datetime.now()
                update_data["updated_by"] = auth_context.user_id

                # Update tag
                result = await self.db["tags"].update_one(
                    {"tag_id": tag_id}, {"$set": update_data}, session=session
                )

                if result.modified_count == 0:
                    return AdminError.transaction_failed("update tag - no changes made")

                # Get updated tag
                updated_tag = await self.db["tags"].find_one(
                    {"tag_id": tag_id}, session=session
                )

                # Log audit
                await self._log_audit(
                    auth_context,
                    "UPDATE",
                    "tags",
                    tag_id,
                    {"updates": updates, "applied_updates": update_data},
                    session,
                )

                logger.info(f"Updated tag {tag_id} by {auth_context.user_id}")
                return AdminResult.success(updated_tag)

        except Exception as e:
            logger.error(f"Failed to update tag {tag_id}: {e}")
            return AdminError.transaction_failed("update tag")

    @require_auth
    async def delete_tag(
        self, auth_context: AuthContext, tag_id: str, soft_delete: bool = True
    ) -> AdminResult[bool]:
        """
        Delete a tag (soft delete by default).

        Args:
            auth_context: Authentication context
            tag_id: ID of the tag to delete
            soft_delete: Whether to soft delete or hard delete

        Returns:
            AdminResult indicating success or failure
        """
        try:
            if not tag_id or not tag_id.strip():
                return AdminError.validation_failed("tag_id", "Tag ID is required")

            async with self.transaction_context() as session:
                # Check if tag exists
                existing_tag = await self.db["tags"].find_one(
                    {"tag_id": tag_id}, session=session
                )

                if not existing_tag:
                    return AdminError.validation_failed("tag_id", "Tag not found")

                # Check if tag is in use
                usage_count = await self.db["user_tags"].count_documents(
                    {"tag_id": tag_id}, session=session
                )

                if usage_count > 0 and not soft_delete:
                    return AdminError.validation_failed(
                        "tag_id",
                        f"Cannot hard delete tag: {usage_count} users have this tag",
                    )

                if soft_delete:
                    # Soft delete - mark as deleted
                    result = await self.db["tags"].update_one(
                        {"tag_id": tag_id},
                        {
                            "$set": {
                                "deleted": True,
                                "deleted_at": datetime.now(),
                                "deleted_by": auth_context.user_id,
                                "updated_at": datetime.now(),
                                "updated_by": auth_context.user_id,
                            }
                        },
                        session=session,
                    )

                    operation = "SOFT_DELETE"
                    success = result.modified_count > 0
                else:
                    # Hard delete - remove from database and all user associations
                    await self.db["user_tags"].delete_many(
                        {"tag_id": tag_id}, session=session
                    )

                    result = await self.db["tags"].delete_one(
                        {"tag_id": tag_id}, session=session
                    )

                    operation = "DELETE"
                    success = result.deleted_count > 0

                if not success:
                    return AdminError.transaction_failed(f"{operation.lower()} tag")

                # Log audit
                await self._log_audit(
                    auth_context,
                    operation,
                    "tags",
                    tag_id,
                    {"soft_delete": soft_delete, "usage_count": usage_count},
                    session,
                )

                logger.info(f"{operation} tag {tag_id} by {auth_context.user_id}")
                return AdminResult.success(True)

        except Exception as e:
            logger.error(f"Failed to delete tag {tag_id}: {e}")
            return AdminError.transaction_failed("delete tag")

    @require_auth
    async def assign_tags_to_user(
        self, auth_context: AuthContext, user_id: str, tag_ids: List[str]
    ) -> AdminResult[Dict[str, Any]]:
        """
        Assign tags to a user.

        Args:
            auth_context: Authentication context
            user_id: ID of the user to assign tags to
            tag_ids: List of tag IDs to assign

        Returns:
            AdminResult containing assignment summary
        """
        try:
            if not user_id or not user_id.strip():
                return AdminError.validation_failed("user_id", "User ID is required")

            if not tag_ids:
                return AdminError.validation_failed(
                    "tag_ids", "At least one tag ID is required"
                )

            # Remove duplicates
            tag_ids = list(set(tag_ids))

            async with self.transaction_context() as session:
                # Verify user exists
                user = await self.db["users"].find_one(
                    {"user_id": user_id}, session=session
                )

                if not user:
                    return AdminError.validation_failed("user_id", "User not found")

                # Verify all tags exist and are active
                valid_tags = (
                    await self.db["tags"]
                    .find(
                        {
                            "tag_id": {"$in": tag_ids},
                            "deleted": {"$ne": True},
                            "is_active": True,
                        },
                        session=session,
                    )
                    .to_list(length=None)
                )

                valid_tag_ids = [tag["tag_id"] for tag in valid_tags]
                invalid_tag_ids = [
                    tag_id for tag_id in tag_ids if tag_id not in valid_tag_ids
                ]

                if invalid_tag_ids:
                    return AdminError.validation_failed(
                        "tag_ids",
                        f"Invalid or inactive tags: {', '.join(invalid_tag_ids)}",
                    )

                # Get existing user tags
                existing_assignments = (
                    await self.db["user_tags"]
                    .find(
                        {"user_id": user_id, "tag_id": {"$in": tag_ids}},
                        session=session,
                    )
                    .to_list(length=None)
                )

                existing_tag_ids = [
                    assignment["tag_id"] for assignment in existing_assignments
                ]
                new_tag_ids = [
                    tag_id for tag_id in tag_ids if tag_id not in existing_tag_ids
                ]

                if not new_tag_ids:
                    return AdminError.validation_failed(
                        "tag_ids", "All tags are already assigned to this user"
                    )

                # Create new assignments
                assignments = []
                for tag_id in new_tag_ids:
                    assignment = {
                        "assignment_id": str(ObjectId()),
                        "user_id": user_id,
                        "tag_id": tag_id,
                        "assigned_at": datetime.now(),
                        "assigned_by": auth_context.user_id,
                    }
                    assignments.append(assignment)

                # Insert assignments
                await self.db["user_tags"].insert_many(assignments, session=session)

                # Update usage count for tags
                await self.db["tags"].update_many(
                    {"tag_id": {"$in": new_tag_ids}},
                    {
                        "$inc": {"usage_count": 1},
                        "$set": {"updated_at": datetime.now()},
                    },
                    session=session,
                )

                # Log audit
                await self._log_audit(
                    auth_context,
                    "ASSIGN_TAGS",
                    "user_tags",
                    user_id,
                    {
                        "user_id": user_id,
                        "assigned_tag_ids": new_tag_ids,
                        "skipped_tag_ids": existing_tag_ids,
                    },
                    session,
                )

                result_data = {
                    "user_id": user_id,
                    "total_requested": len(tag_ids),
                    "newly_assigned": len(new_tag_ids),
                    "already_assigned": len(existing_tag_ids),
                    "assigned_tag_ids": new_tag_ids,
                    "skipped_tag_ids": existing_tag_ids,
                }

                logger.info(
                    f"Assigned {len(new_tag_ids)} tags to user {user_id} by {auth_context.user_id}"
                )
                return AdminResult.success(result_data)

        except Exception as e:
            logger.error(f"Failed to assign tags to user {user_id}: {e}")
            return AdminError.transaction_failed("assign tags to user")

    @require_auth
    async def remove_tags_from_user(
        self, auth_context: AuthContext, user_id: str, tag_ids: List[str]
    ) -> AdminResult[Dict[str, Any]]:
        """
        Remove tags from a user.

        Args:
            auth_context: Authentication context
            user_id: ID of the user to remove tags from
            tag_ids: List of tag IDs to remove

        Returns:
            AdminResult containing removal summary
        """
        try:
            if not user_id or not user_id.strip():
                return AdminError.validation_failed("user_id", "User ID is required")

            if not tag_ids:
                return AdminError.validation_failed(
                    "tag_ids", "At least one tag ID is required"
                )

            # Remove duplicates
            tag_ids = list(set(tag_ids))

            async with self.transaction_context() as session:
                # Verify user exists
                user = await self.db["users"].find_one(
                    {"user_id": user_id}, session=session
                )

                if not user:
                    return AdminError.validation_failed("user_id", "User not found")

                # Get existing assignments
                existing_assignments = (
                    await self.db["user_tags"]
                    .find(
                        {"user_id": user_id, "tag_id": {"$in": tag_ids}},
                        session=session,
                    )
                    .to_list(length=None)
                )

                existing_tag_ids = [
                    assignment["tag_id"] for assignment in existing_assignments
                ]
                non_existing_tag_ids = [
                    tag_id for tag_id in tag_ids if tag_id not in existing_tag_ids
                ]

                if not existing_tag_ids:
                    return AdminError.validation_failed(
                        "tag_ids",
                        "None of the specified tags are assigned to this user",
                    )

                # Remove assignments
                result = await self.db["user_tags"].delete_many(
                    {"user_id": user_id, "tag_id": {"$in": existing_tag_ids}},
                    session=session,
                )

                # Update usage count for tags
                await self.db["tags"].update_many(
                    {"tag_id": {"$in": existing_tag_ids}},
                    {
                        "$inc": {"usage_count": -1},
                        "$set": {"updated_at": datetime.now()},
                    },
                    session=session,
                )

                # Log audit
                await self._log_audit(
                    auth_context,
                    "REMOVE_TAGS",
                    "user_tags",
                    user_id,
                    {
                        "user_id": user_id,
                        "removed_tag_ids": existing_tag_ids,
                        "not_assigned_tag_ids": non_existing_tag_ids,
                    },
                    session,
                )

                result_data = {
                    "user_id": user_id,
                    "total_requested": len(tag_ids),
                    "removed": result.deleted_count,
                    "not_assigned": len(non_existing_tag_ids),
                    "removed_tag_ids": existing_tag_ids,
                    "not_assigned_tag_ids": non_existing_tag_ids,
                }

                logger.info(
                    f"Removed {result.deleted_count} tags from user {user_id} by {auth_context.user_id}"
                )
                return AdminResult.success(result_data)

        except Exception as e:
            logger.error(f"Failed to remove tags from user {user_id}: {e}")
            return AdminError.transaction_failed("remove tags from user")

    @require_auth
    async def bulk_assign_tags(
        self, auth_context: AuthContext, assignments: List[Dict[str, Any]]
    ) -> AdminResult[Dict[str, Any]]:
        """
        Assign tags to multiple users in a single transaction.

        Args:
            auth_context: Authentication context
            assignments: List of assignments, each containing user_id and tag_ids

        Returns:
            AdminResult containing summary of bulk assignment operation
        """
        try:
            if not assignments:
                return AdminError.validation_failed(
                    "assignments", "No tag assignments provided"
                )

            # Validate each assignment entry
            for i, assignment in enumerate(assignments):
                if "user_id" not in assignment or "tag_ids" not in assignment:
                    return AdminError.validation_failed(
                        "assignments",
                        f"Entry {i} missing required fields: user_id, tag_ids",
                    )

                if not assignment["tag_ids"]:
                    return AdminError.validation_failed(
                        "assignments", f"Entry {i} has empty tag_ids list"
                    )

            async with self.transaction_context() as session:
                assigned_count = 0
                failed_assignments = []
                batch_id = str(ObjectId())  # Common batch ID for all assignments

                for assignment in assignments:
                    try:
                        user_id = assignment["user_id"]
                        tag_ids = list(set(assignment["tag_ids"]))  # Remove duplicates

                        # Verify user exists
                        user = await self.db["users"].find_one(
                            {"user_id": user_id}, session=session
                        )

                        if not user:
                            failed_assignments.append(
                                {"user_id": user_id, "reason": "User not found"}
                            )
                            continue

                        # Verify all tags exist and are active
                        valid_tags = (
                            await self.db["tags"]
                            .find(
                                {
                                    "tag_id": {"$in": tag_ids},
                                    "deleted": {"$ne": True},
                                    "is_active": True,
                                },
                                session=session,
                            )
                            .to_list(length=None)
                        )

                        valid_tag_ids = [tag["tag_id"] for tag in valid_tags]

                        if not valid_tag_ids:
                            failed_assignments.append(
                                {"user_id": user_id, "reason": "No valid tags found"}
                            )
                            continue

                        # Get existing user tags
                        existing_assignments = (
                            await self.db["user_tags"]
                            .find(
                                {"user_id": user_id, "tag_id": {"$in": valid_tag_ids}},
                                session=session,
                            )
                            .to_list(length=None)
                        )

                        existing_tag_ids = [
                            assignment["tag_id"] for assignment in existing_assignments
                        ]
                        new_tag_ids = [
                            tag_id
                            for tag_id in valid_tag_ids
                            if tag_id not in existing_tag_ids
                        ]

                        if not new_tag_ids:
                            failed_assignments.append(
                                {
                                    "user_id": user_id,
                                    "reason": "All tags already assigned",
                                }
                            )
                            continue

                        # Create new assignments
                        new_assignments = []
                        for tag_id in new_tag_ids:
                            new_assignment = {
                                "assignment_id": str(ObjectId()),
                                "user_id": user_id,
                                "tag_id": tag_id,
                                "assigned_at": datetime.now(),
                                "assigned_by": auth_context.user_id,
                                "batch_id": batch_id,
                            }
                            new_assignments.append(new_assignment)

                        # Insert assignments
                        await self.db["user_tags"].insert_many(
                            new_assignments, session=session
                        )

                        # Update usage count for tags
                        await self.db["tags"].update_many(
                            {"tag_id": {"$in": new_tag_ids}},
                            {
                                "$inc": {"usage_count": 1},
                                "$set": {"updated_at": datetime.now()},
                            },
                            session=session,
                        )

                        assigned_count += 1

                        # Log audit for each successful assignment
                        await self._log_audit(
                            auth_context,
                            "BULK_ASSIGN_TAGS",
                            "user_tags",
                            user_id,
                            {
                                "user_id": user_id,
                                "assigned_tag_ids": new_tag_ids,
                                "batch_id": batch_id,
                            },
                            session,
                        )

                    except Exception as e:
                        failed_assignments.append(
                            {
                                "user_id": assignment.get("user_id", "unknown"),
                                "reason": str(e),
                            }
                        )

                # Log bulk operation audit
                await self._log_audit(
                    auth_context,
                    "BULK_ASSIGN_OPERATION",
                    "user_tags",
                    "bulk",
                    {
                        "batch_id": batch_id,
                        "total_requested": len(assignments),
                        "successful_assignments": assigned_count,
                        "failed_assignments": len(failed_assignments),
                    },
                    session,
                )

                result_data = {
                    "batch_id": batch_id,
                    "total_requested": len(assignments),
                    "successful_assignments": assigned_count,
                    "failed_assignments": failed_assignments,
                }

                logger.info(
                    f"Bulk assigned tags to {assigned_count}/{len(assignments)} users by {auth_context.user_id}"
                )
                return AdminResult.success(result_data)

        except Exception as e:
            logger.error(f"Failed to bulk assign tags: {e}")
            return AdminError.transaction_failed("bulk assign tags")

    @require_auth
    async def recalculate_tag_usage(
        self, auth_context: AuthContext, tag_id: Optional[str] = None
    ) -> AdminResult[Dict[str, Any]]:
        """
        Recalculate usage count for tags.

        Args:
            auth_context: Authentication context
            tag_id: Optional specific tag ID to recalculate (if None, recalculates all tags)

        Returns:
            AdminResult containing recalculation summary
        """
        try:
            async with self.transaction_context() as session:
                if tag_id:
                    # Recalculate specific tag
                    tag = await self.db["tags"].find_one(
                        {"tag_id": tag_id}, session=session
                    )

                    if not tag:
                        return AdminError.validation_failed("tag_id", "Tag not found")

                    # Count actual usage
                    actual_count = await self.db["user_tags"].count_documents(
                        {"tag_id": tag_id}, session=session
                    )

                    current_count = tag.get("usage_count", 0)

                    # Update if different
                    if actual_count != current_count:
                        await self.db["tags"].update_one(
                            {"tag_id": tag_id},
                            {
                                "$set": {
                                    "usage_count": actual_count,
                                    "usage_recalculated_at": datetime.now(),
                                    "usage_recalculated_by": auth_context.user_id,
                                    "updated_at": datetime.now(),
                                }
                            },
                            session=session,
                        )

                    result_data = {
                        "tag_id": tag_id,
                        "previous_count": current_count,
                        "actual_count": actual_count,
                        "difference": actual_count - current_count,
                        "updated": actual_count != current_count,
                    }
                else:
                    # Recalculate all tags
                    pipeline = [
                        {
                            "$lookup": {
                                "from": "user_tags",
                                "localField": "tag_id",
                                "foreignField": "tag_id",
                                "as": "assignments",
                            }
                        },
                        {
                            "$project": {
                                "tag_id": 1,
                                "usage_count": 1,
                                "actual_count": {"$size": "$assignments"},
                            }
                        },
                        {
                            "$match": {
                                "$expr": {"$ne": ["$usage_count", "$actual_count"]}
                            }
                        },
                    ]

                    tags_to_update = (
                        await self.db["tags"]
                        .aggregate(pipeline, session=session)
                        .to_list(length=None)
                    )

                    updated_count = 0
                    for tag in tags_to_update:
                        await self.db["tags"].update_one(
                            {"tag_id": tag["tag_id"]},
                            {
                                "$set": {
                                    "usage_count": tag["actual_count"],
                                    "usage_recalculated_at": datetime.now(),
                                    "usage_recalculated_by": auth_context.user_id,
                                    "updated_at": datetime.now(),
                                }
                            },
                            session=session,
                        )
                        updated_count += 1

                    result_data = {
                        "total_tags_checked": await self.db["tags"].count_documents(
                            {}, session=session
                        ),
                        "tags_updated": updated_count,
                        "updated_tag_ids": [tag["tag_id"] for tag in tags_to_update],
                    }

                # Log audit
                await self._log_audit(
                    auth_context,
                    "RECALCULATE_TAG_USAGE",
                    "tags",
                    tag_id or "all",
                    result_data,
                    session,
                )

                logger.info(
                    f"Recalculated tag usage for {tag_id or 'all tags'} by {auth_context.user_id}"
                )
                return AdminResult.success(result_data)

        except Exception as e:
            logger.error(f"Failed to recalculate tag usage: {e}")
            return AdminError.transaction_failed("recalculate tag usage")
