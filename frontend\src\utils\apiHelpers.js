/**
 * API Helper Utilities
 * Common utilities for API interactions
 */

/**
 * Build query string from parameters object
 * @param {Object} params - Parameters object
 * @returns {string} Query string
 */
export const buildQueryString = (params) => {
  if (!params || Object.keys(params).length === 0) {
    return ''
  }
  
  const searchParams = new URLSearchParams()
  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '') {
      searchParams.append(key, value)
    }
  })
  
  const queryString = searchParams.toString()
  return queryString ? `?${queryString}` : ''
}

/**
 * Handle API response and extract data
 * @param {Object} response - Axios response object
 * @returns {Object} Processed response data
 */
export const handleApiResponse = (response) => {
  // Handle different response structures
  if (response.data) {
    // Standard axios response
    return {
      data: response.data,
      status: response.status,
      headers: response.headers,
      success: response.status >= 200 && response.status < 300
    }
  }
  
  // Direct data response
  return {
    data: response,
    success: true
  }
}

/**
 * Handle API errors and normalize error structure
 * @param {Error} error - Error object
 * @returns {Object} Normalized error object
 */
export const handleApiError = (error) => {
  const normalizedError = {
    success: false,
    error: {
      code: 'UNKNOWN_ERROR',
      message: 'An unexpected error occurred',
      details: null
    },
    status: 500
  }

  if (error.response) {
    // Server responded with error status
    normalizedError.status = error.response.status
    normalizedError.error.code = error.response.data?.error?.code || `HTTP_${error.response.status}`
    normalizedError.error.message = error.response.data?.error?.message || error.response.statusText
    normalizedError.error.details = error.response.data?.error?.details || null
  } else if (error.request) {
    // Network error
    normalizedError.error.code = 'NETWORK_ERROR'
    normalizedError.error.message = 'Network error - please check your connection'
    normalizedError.status = 0
  } else {
    // Other error
    normalizedError.error.message = error.message || 'An unexpected error occurred'
  }

  return normalizedError
}

/**
 * Retry function for failed API calls
 * @param {Function} apiCall - Function that returns a Promise
 * @param {number} maxRetries - Maximum number of retries
 * @param {number} delay - Delay between retries in milliseconds
 * @returns {Promise} Promise that resolves with the API response
 */
export const retryApiCall = async (apiCall, maxRetries = 3, delay = 1000) => {
  let lastError
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const result = await apiCall()
      return result
    } catch (error) {
      lastError = error
      
      // Don't retry on client errors (4xx) except 408, 429
      if (error.response?.status >= 400 && error.response?.status < 500) {
        if (error.response.status !== 408 && error.response.status !== 429) {
          throw error
        }
      }
      
      // Don't retry on last attempt
      if (attempt === maxRetries) {
        break
      }
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, attempt)))
    }
  }
  
  throw lastError
}

/**
 * Create pagination parameters
 * @param {number} page - Page number (1-based)
 * @param {number} limit - Items per page
 * @param {Object} additionalParams - Additional query parameters
 * @returns {Object} Pagination parameters
 */
export const createPaginationParams = (page = 1, limit = 20, additionalParams = {}) => {
  return {
    page: Math.max(1, page),
    limit: Math.max(1, Math.min(100, limit)), // Cap at 100 items per page
    ...additionalParams
  }
}

/**
 * Transform mock data to match real API structure
 * @param {Object} mockData - Mock data object
 * @param {string} type - Data type for transformation
 * @returns {Object} Transformed data
 */
export const transformMockData = (mockData, type) => {
  switch (type) {
    case 'university':
      return {
        university_id: mockData.id,
        university_name: mockData.name,
        country: mockData.country || 'China',
        province: mockData.province || 'Unknown'
      }
    
    case 'competition':
      return {
        id: mockData.id,
        competition_id: mockData.id,
        competition_name: mockData.title,
        route_id: mockData.routeId || `route_${mockData.id}`
      }
    
    default:
      return mockData
  }
}

/**
 * Check if we're in development mode
 * @returns {boolean} True if in development mode
 */
export const isDevelopment = () => {
  return import.meta.env.DEV || import.meta.env.VITE_APP_ENV === 'development'
}

/**
 * Check if mock API is enabled
 * @returns {boolean} True if mock API is enabled
 */
export const isMockApiEnabled = () => {
  return import.meta.env.VITE_MOCK_API !== 'false'
}

/**
 * Log API calls in development
 * @param {string} method - HTTP method
 * @param {string} url - Request URL
 * @param {Object} data - Request data
 * @param {Object} response - Response data
 */
export const logApiCall = (method, url, data = null, response = null) => {
  if (isDevelopment() && import.meta.env.VITE_DEBUG === 'true') {
    console.group(`🌐 API Call: ${method.toUpperCase()} ${url}`)
    if (data) console.log('📤 Request:', data)
    if (response) console.log('📥 Response:', response)
    console.groupEnd()
  }
}
