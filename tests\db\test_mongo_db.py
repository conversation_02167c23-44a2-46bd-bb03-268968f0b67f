import pytest
from unittest.mock import patch, MagicMock, AsyncMock

from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
import app.db.mongo_db as mongo_db_module


# Fixture to ensure mongo_client is reset before and after each test
@pytest.fixture(autouse=True)
async def manage_mongo_client_state():
    original_client = mongo_db_module.mongo_client
    mongo_db_module.mongo_client = None

    yield

    if mongo_db_module.mongo_client:
        mongo_db_module.mongo_client.close()
    mongo_db_module.mongo_client = original_client


@pytest.mark.asyncio
async def test_connect_to_mongo_successful():
    # Mock dependencies
    mock_tunnel = MagicMock()
    mock_tunnel.is_active = True
    mock_tunnel.local_bind_port = 27018  # Example port

    mock_motor_client_instance = MagicMock(spec=AsyncIOMotorClient)
    # Mock the admin attribute and its command method
    mock_admin = MagicMock()
    mock_admin.command = (
        AsyncMock()
    )  # This will be used for client.admin.command('ping')
    mock_motor_client_instance.admin = mock_admin

    # Patch os.getenv for MongoDB credentials
    env_vars = {
        "MONGO_USER": "testuser",
        "MONGO_PASSWORD": "testpassword",
        "MONGO_DB": "testdb",
    }

    with (
        patch(
            "app.db.mongo_db.tunnel_manager.get_tunnel", return_value=mock_tunnel
        ) as mock_get_tunnel,
        patch("app.db.mongo_db.os.getenv", side_effect=lambda k: env_vars.get(k)),
        patch(
            "app.db.mongo_db.AsyncIOMotorClient",
            return_value=mock_motor_client_instance,
        ) as mock_motor_client_constructor,
    ):
        await mongo_db_module.connect_to_mongo()

        # Assertions
        mock_get_tunnel.assert_called_once()

        # Check that AsyncIOMotorClient was called with expected parameters
        mock_motor_client_constructor.assert_called_once_with(
            host="localhost",
            port=mock_tunnel.local_bind_port,
            username="testuser",
            password="testpassword",
            authSource="testdb",
            authMechanism="SCRAM-SHA-1",
            serverSelectionTimeoutMS=5000,
        )

        # Assert that ping was called
        mock_motor_client_instance.admin.command.assert_called_once_with("ping")

        # Assert that the global client is set
        client = mongo_db_module.get_mongo_db_client()
        assert client is mock_motor_client_instance

        # Clean up (though fixture should also handle this)
        await mongo_db_module.close_mongo_connection()
        assert mongo_db_module.mongo_client is None


@pytest.mark.asyncio
async def test_connect_to_mongo_tunnel_not_active():
    with (
        patch(
            "app.db.mongo_db.tunnel_manager.get_tunnel", return_value=None
        ) as mock_get_tunnel,
        pytest.raises(
            Exception, match="SSH Tunnel not active, cannot connect to MongoDB"
        ),
    ):
        await mongo_db_module.connect_to_mongo()
    mock_get_tunnel.assert_called_once()
    assert mongo_db_module.mongo_client is None


@pytest.mark.asyncio
async def test_connect_to_mongo_connection_error():
    mock_tunnel = MagicMock()
    mock_tunnel.is_active = True
    mock_tunnel.local_bind_port = 27018

    env_vars = {
        "MONGO_USER": "testuser",
        "MONGO_PASSWORD": "testpassword",
        "MONGO_DB": "testdb",
    }

    # Simulate AsyncIOMotorClient raising an exception (e.g., connection timeout)
    with (
        patch("app.db.mongo_db.tunnel_manager.get_tunnel", return_value=mock_tunnel),
        patch("app.db.mongo_db.os.getenv", side_effect=lambda k: env_vars.get(k)),
        patch(
            "app.db.mongo_db.AsyncIOMotorClient",
            side_effect=Exception("Simulated DB connection error"),
        ) as mock_motor_constructor,
        pytest.raises(Exception, match="Simulated DB connection error"),
    ):
        await mongo_db_module.connect_to_mongo()

    mock_motor_constructor.assert_called_once()
    assert mongo_db_module.mongo_client is None


@pytest.mark.asyncio
async def test_get_mongo_db_client_not_initialized():
    # Ensure client is None (fixture should handle this, but explicit for clarity)
    mongo_db_module.mongo_client = None
    with pytest.raises(Exception, match="MongoDB client not initialized"):
        mongo_db_module.get_mongo_db_client()


@pytest.mark.asyncio
async def test_get_mongo_database_successful():
    # First, establish a connection
    mock_tunnel = MagicMock()
    mock_tunnel.is_active = True
    mock_tunnel.local_bind_port = 27018
    mock_motor_client_instance = MagicMock(spec=AsyncIOMotorClient)
    # Mock the admin attribute and its command method
    mock_admin = MagicMock()
    mock_admin.command = AsyncMock()  # For ping
    mock_motor_client_instance.admin = mock_admin
    mock_db_instance = MagicMock(spec=AsyncIOMotorDatabase)
    mock_motor_client_instance.__getitem__.return_value = (
        mock_db_instance  # db = client['dbname']
    )

    env_vars = {
        "MONGO_USER": "testuser",
        "MONGO_PASSWORD": "testpassword",
        "MONGO_DB": "testdb_default",
    }

    with (
        patch("app.db.mongo_db.tunnel_manager.get_tunnel", return_value=mock_tunnel),
        patch(
            "app.db.mongo_db.os.getenv", side_effect=lambda k: env_vars.get(k)
        ) as mock_os_getenv,
        patch(
            "app.db.mongo_db.AsyncIOMotorClient",
            return_value=mock_motor_client_instance,
        ),
    ):
        await mongo_db_module.connect_to_mongo()

        # Test with specific db_name
        db_specific = mongo_db_module.get_mongo_database(db_name="specific_db")
        assert db_specific is mock_db_instance
        mock_motor_client_instance.__getitem__.assert_called_with("specific_db")

        # Test with default db_name from env
        db_default = mongo_db_module.get_mongo_database()
        assert db_default is mock_db_instance
        mock_motor_client_instance.__getitem__.assert_called_with("testdb_default")

        # Test os.getenv was called for the default MONGO_DB
        found_mongo_db_call = False
        for call_args in mock_os_getenv.call_args_list:
            # Ensure we are checking calls made for MONGO_DB, specifically the one for the default case
            # The side_effect lambda k: env_vars.get(k) will be called multiple times.
            # We need to ensure one of those calls was to retrieve "MONGO_DB"
            if (
                call_args[0][0] == "MONGO_DB"
            ):  # Check if the first argument of the call was "MONGO_DB"
                found_mongo_db_call = True
                break
        assert found_mongo_db_call, (
            "os.getenv('MONGO_DB') was not called for default database name"
        )

    await mongo_db_module.close_mongo_connection()


@pytest.mark.asyncio
async def test_get_mongo_database_name_error():
    # Establish connection first
    mock_tunnel = MagicMock()
    mock_tunnel.is_active = True
    mock_tunnel.local_bind_port = 27018
    mock_motor_client_instance = MagicMock(spec=AsyncIOMotorClient)
    # Mock the admin attribute and its command method
    mock_admin = MagicMock()
    mock_admin.command = AsyncMock()
    mock_motor_client_instance.admin = mock_admin

    # MONGO_DB will not be in env_vars for this test
    env_vars_no_db = {
        "MONGO_USER": "testuser",
        "MONGO_PASSWORD": "testpassword",
        # "MONGO_DB": "..." // Intentionally absent
    }

    with (
        patch("app.db.mongo_db.tunnel_manager.get_tunnel", return_value=mock_tunnel),
        patch(
            "app.db.mongo_db.os.getenv", side_effect=lambda k: env_vars_no_db.get(k)
        ) as mock_os_getenv,
        patch(
            "app.db.mongo_db.AsyncIOMotorClient",
            return_value=mock_motor_client_instance,
        ),
    ):
        await mongo_db_module.connect_to_mongo()

        with pytest.raises(
            ValueError,
            match="Database name must be provided or MONGO_DB must be set in .env",
        ):
            mongo_db_module.get_mongo_database()  # No db_name and MONGO_DB not in env

        # Check that os.getenv("MONGO_DB") was indeed called and returned None
        # Iterate through calls made to os.getenv. The last relevant call to 'MONGO_DB'
        # before raising ValueError in get_mongo_database() is what we care about.
        # The connect_to_mongo also calls os.getenv("MONGO_DB").
        # We are interested in the call from get_mongo_database().

        # Resetting call count for mock_os_getenv might be too complex if it's already used by connect_to_mongo
        # A simpler check: verify that 'MONGO_DB' was among the keys requested.
        # And given it's not in env_vars_no_db, its return value would be None.

        # The critical check is that `pytest.raises` caught the ValueError.
        # The below check is for thoroughness to ensure os.getenv was involved as expected.

        # Let's check the call args more carefully. We expect os.getenv("MONGO_DB") to be called
        # within the get_mongo_database function.
        # We can look at the call history of mock_os_getenv.
        # The last call related to "MONGO_DB" that resulted in None and led to the error.

        # A simple way to check if "MONGO_DB" was *ever* an argument to os.getenv
        # (which includes the call from connect_to_mongo and the one from get_mongo_database)
        # This doesn't isolate the call from get_mongo_database but confirms it was queried.
        keys_requested = [call[0][0] for call in mock_os_getenv.call_args_list]
        assert "MONGO_DB" in keys_requested, (
            "os.getenv was not called with 'MONGO_DB' as an argument"
        )

    await mongo_db_module.close_mongo_connection()


@pytest.mark.asyncio
async def test_close_mongo_connection():
    # Establish a connection
    mock_tunnel = MagicMock()
    mock_tunnel.is_active = True
    mock_tunnel.local_bind_port = 27018
    mock_motor_client_instance = MagicMock(spec=AsyncIOMotorClient)
    # Mock the admin attribute and its command method
    mock_admin = MagicMock()
    mock_admin.command = AsyncMock()  # For ping
    mock_motor_client_instance.admin = mock_admin
    mock_motor_client_instance.close = MagicMock()  # To check if close is called

    env_vars = {
        "MONGO_USER": "testuser",
        "MONGO_PASSWORD": "testpassword",
        "MONGO_DB": "testdb",
    }
    with (
        patch("app.db.mongo_db.tunnel_manager.get_tunnel", return_value=mock_tunnel),
        patch("app.db.mongo_db.os.getenv", side_effect=lambda k: env_vars.get(k)),
        patch(
            "app.db.mongo_db.AsyncIOMotorClient",
            return_value=mock_motor_client_instance,
        ),
    ):
        await mongo_db_module.connect_to_mongo()

    assert (
        mongo_db_module.mongo_client is mock_motor_client_instance
    )  # Ensure client was set

    await mongo_db_module.close_mongo_connection()

    assert mongo_db_module.mongo_client is None
    mock_motor_client_instance.close.assert_called_once()
