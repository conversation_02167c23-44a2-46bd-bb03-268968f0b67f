"""
Credit Admin - CRUD operations for credit management.

Handles credit adjustments, history management, and batch operations with audit logging and transaction support.
"""

from typing import Dict, Any, List
import logging
from datetime import datetime
from bson import ObjectId

from gateway.admin.base import BaseAdmin
from libs.auth.context import AuthContext, require_auth
from libs.errors import AdminResult, AdminError

logger = logging.getLogger(__name__)


class CreditAdmin(BaseAdmin):
    """Admin service for credit management operations."""

    def __init__(self):
        """Initialize credit admin service."""
        super().__init__()

    @require_auth
    async def adjust_credits(
        self, auth_context: AuthContext, adjustment_data: Dict[str, Any]
    ) -> AdminResult[str]:
        """
        Adjust user credits (positive or negative adjustment).

        Args:
            auth_context: Authentication context
            adjustment_data: Credit adjustment data including user_id, amount, reason

        Returns:
            AdminResult containing the credit record ID
        """
        try:
            # Validate required fields
            required_fields = ["user_id", "credit_adjustment", "reason"]
            missing_fields = [
                field
                for field in required_fields
                if field not in adjustment_data or adjustment_data[field] is None
            ]

            if missing_fields:
                return AdminError.validation_failed(
                    "required_fields",
                    f"Missing required fields: {', '.join(missing_fields)}",
                )

            # Validate credit adjustment amount
            credit_adjustment = adjustment_data["credit_adjustment"]
            if not isinstance(credit_adjustment, int) or credit_adjustment == 0:
                return AdminError.validation_failed(
                    "credit_adjustment", "Credit adjustment must be a non-zero integer"
                )

            async with self.transaction_context() as session:
                # Verify user exists
                user = await self.db["users"].find_one(
                    {"user_id": adjustment_data["user_id"]}, session=session
                )

                if not user:
                    return AdminError.validation_failed("user_id", "User not found")

                # Check if adjustment would result in negative total credits
                current_credits = user.get("total_credits", 0)
                if current_credits + credit_adjustment < 0:
                    return AdminError.validation_failed(
                        "credit_adjustment",
                        f"Adjustment would result in negative credits: {current_credits} + {credit_adjustment} = {current_credits + credit_adjustment}",
                    )

                # Generate credit record ID
                credit_record_id = str(ObjectId())

                # Prepare credit adjustment record
                credit_record = {
                    "credit_id": credit_record_id,
                    "user_id": adjustment_data["user_id"],
                    "competition_id": adjustment_data.get(
                        "competition_id", "ADMIN_ADJUSTMENT"
                    ),
                    "qualification_id": adjustment_data.get(
                        "qualification_id", "ADMIN_ADJUSTMENT"
                    ),
                    "credit": credit_adjustment,
                    "reason": adjustment_data["reason"],
                    "adjustment_type": "MANUAL_ADJUSTMENT",
                    "related_object_id": adjustment_data.get("related_object_id"),
                    "related_object_type": adjustment_data.get("related_object_type"),
                    "admin_id": auth_context.user_id,
                    "created_at": datetime.now(),
                    "revoked_at": None,
                }

                # Insert credit record
                await self.db["credit_logs"].insert_one(credit_record, session=session)

                # Update user's total credits
                await self.db["users"].update_one(
                    {"user_id": adjustment_data["user_id"]},
                    {
                        "$inc": {"total_credits": credit_adjustment},
                        "$set": {"updated_at": datetime.now()},
                    },
                    session=session,
                )

                # Log audit
                await self._log_audit(
                    auth_context,
                    "ADJUST_CREDITS",
                    "credit_logs",
                    credit_record_id,
                    {
                        "user_id": adjustment_data["user_id"],
                        "credit_adjustment": credit_adjustment,
                        "reason": adjustment_data["reason"],
                    },
                    session,
                )

                logger.info(
                    f"Adjusted {credit_adjustment} credits for user {adjustment_data['user_id']} by {auth_context.user_id}"
                )
                return AdminResult.success(credit_record_id)

        except Exception as e:
            logger.error(f"Failed to adjust credits: {e}")
            return AdminError.transaction_failed("adjust credits")

    @require_auth
    async def transfer_credits(
        self, auth_context: AuthContext, transfer_data: Dict[str, Any]
    ) -> AdminResult[Dict[str, str]]:
        """
        Transfer credits from one user to another.

        Args:
            auth_context: Authentication context
            transfer_data: Transfer data including from_user_id, to_user_id, amount, reason

        Returns:
            AdminResult containing both credit record IDs
        """
        try:
            # Validate required fields
            required_fields = ["from_user_id", "to_user_id", "credit_amount", "reason"]
            missing_fields = [
                field
                for field in required_fields
                if field not in transfer_data or not transfer_data[field]
            ]

            if missing_fields:
                return AdminError.validation_failed(
                    "required_fields",
                    f"Missing required fields: {', '.join(missing_fields)}",
                )

            # Validate credit amount
            credit_amount = transfer_data["credit_amount"]
            if not isinstance(credit_amount, int) or credit_amount <= 0:
                return AdminError.validation_failed(
                    "credit_amount", "Credit amount must be a positive integer"
                )

            # Validate users are different
            if transfer_data["from_user_id"] == transfer_data["to_user_id"]:
                return AdminError.validation_failed(
                    "user_ids", "Cannot transfer credits to the same user"
                )

            async with self.transaction_context() as session:
                # Verify both users exist
                from_user = await self.db["users"].find_one(
                    {"user_id": transfer_data["from_user_id"]}, session=session
                )

                if not from_user:
                    return AdminError.validation_failed(
                        "from_user_id", "Source user not found"
                    )

                to_user = await self.db["users"].find_one(
                    {"user_id": transfer_data["to_user_id"]}, session=session
                )

                if not to_user:
                    return AdminError.validation_failed(
                        "to_user_id", "Destination user not found"
                    )

                # Check if source user has enough credits
                current_credits = from_user.get("total_credits", 0)
                if current_credits < credit_amount:
                    return AdminError.validation_failed(
                        "credit_amount",
                        f"Insufficient credits: user has {current_credits}, requested {credit_amount}",
                    )

                # Generate transfer ID for linking records
                transfer_id = str(ObjectId())

                # Generate credit record IDs
                debit_record_id = str(ObjectId())
                credit_record_id = str(ObjectId())

                # Prepare debit record (from user)
                debit_record = {
                    "credit_id": debit_record_id,
                    "user_id": transfer_data["from_user_id"],
                    "competition_id": "CREDIT_TRANSFER",
                    "qualification_id": "CREDIT_TRANSFER",
                    "credit": -credit_amount,
                    "reason": f"Transfer to {transfer_data['to_user_id']}: {transfer_data['reason']}",
                    "adjustment_type": "TRANSFER_DEBIT",
                    "transfer_id": transfer_id,
                    "related_user_id": transfer_data["to_user_id"],
                    "admin_id": auth_context.user_id,
                    "created_at": datetime.now(),
                    "revoked_at": None,
                }

                # Prepare credit record (to user)
                credit_record = {
                    "credit_id": credit_record_id,
                    "user_id": transfer_data["to_user_id"],
                    "competition_id": "CREDIT_TRANSFER",
                    "qualification_id": "CREDIT_TRANSFER",
                    "credit": credit_amount,
                    "reason": f"Transfer from {transfer_data['from_user_id']}: {transfer_data['reason']}",
                    "adjustment_type": "TRANSFER_CREDIT",
                    "transfer_id": transfer_id,
                    "related_user_id": transfer_data["from_user_id"],
                    "admin_id": auth_context.user_id,
                    "created_at": datetime.now(),
                    "revoked_at": None,
                }

                # Insert both credit records
                await self.db["credit_logs"].insert_many(
                    [debit_record, credit_record], session=session
                )

                # Update both users' total credits
                await self.db["users"].update_one(
                    {"user_id": transfer_data["from_user_id"]},
                    {
                        "$inc": {"total_credits": -credit_amount},
                        "$set": {"updated_at": datetime.now()},
                    },
                    session=session,
                )

                await self.db["users"].update_one(
                    {"user_id": transfer_data["to_user_id"]},
                    {
                        "$inc": {"total_credits": credit_amount},
                        "$set": {"updated_at": datetime.now()},
                    },
                    session=session,
                )

                # Log audit
                await self._log_audit(
                    auth_context,
                    "TRANSFER_CREDITS",
                    "credit_logs",
                    transfer_id,
                    {
                        "from_user_id": transfer_data["from_user_id"],
                        "to_user_id": transfer_data["to_user_id"],
                        "credit_amount": credit_amount,
                        "reason": transfer_data["reason"],
                    },
                    session,
                )

                result_data = {
                    "transfer_id": transfer_id,
                    "debit_record_id": debit_record_id,
                    "credit_record_id": credit_record_id,
                }

                logger.info(
                    f"Transferred {credit_amount} credits from {transfer_data['from_user_id']} to {transfer_data['to_user_id']} by {auth_context.user_id}"
                )
                return AdminResult.success(result_data)

        except Exception as e:
            logger.error(f"Failed to transfer credits: {e}")
            return AdminError.transaction_failed("transfer credits")

    @require_auth
    async def freeze_credits(
        self, auth_context: AuthContext, user_id: str, reason: str
    ) -> AdminResult[bool]:
        """
        Freeze a user's credits (prevent further credit operations).

        Args:
            auth_context: Authentication context
            user_id: ID of the user whose credits to freeze
            reason: Reason for freezing credits

        Returns:
            AdminResult indicating success or failure
        """
        try:
            if not user_id or not user_id.strip():
                return AdminError.validation_failed("user_id", "User ID is required")

            if not reason or not reason.strip():
                return AdminError.validation_failed(
                    "reason", "Freeze reason is required"
                )

            async with self.transaction_context() as session:
                # Check if user exists
                user = await self.db["users"].find_one(
                    {"user_id": user_id}, session=session
                )

                if not user:
                    return AdminError.validation_failed("user_id", "User not found")

                if user.get("credits_frozen", False):
                    return AdminError.validation_failed(
                        "user_id", "User credits are already frozen"
                    )

                # Freeze credits
                result = await self.db["users"].update_one(
                    {"user_id": user_id},
                    {
                        "$set": {
                            "credits_frozen": True,
                            "credits_frozen_at": datetime.now(),
                            "credits_frozen_by": auth_context.user_id,
                            "credits_freeze_reason": reason,
                            "updated_at": datetime.now(),
                        }
                    },
                    session=session,
                )

                if result.modified_count == 0:
                    return AdminError.transaction_failed("freeze credits")

                # Log audit
                await self._log_audit(
                    auth_context,
                    "FREEZE_CREDITS",
                    "users",
                    user_id,
                    {"reason": reason},
                    session,
                )

                logger.info(
                    f"Froze credits for user {user_id} by {auth_context.user_id}"
                )
                return AdminResult.success(True)

        except Exception as e:
            logger.error(f"Failed to freeze credits for user {user_id}: {e}")
            return AdminError.transaction_failed("freeze credits")

    @require_auth
    async def unfreeze_credits(
        self, auth_context: AuthContext, user_id: str, reason: str
    ) -> AdminResult[bool]:
        """
        Unfreeze a user's credits.

        Args:
            auth_context: Authentication context
            user_id: ID of the user whose credits to unfreeze
            reason: Reason for unfreezing credits

        Returns:
            AdminResult indicating success or failure
        """
        try:
            if not user_id or not user_id.strip():
                return AdminError.validation_failed("user_id", "User ID is required")

            if not reason or not reason.strip():
                return AdminError.validation_failed(
                    "reason", "Unfreeze reason is required"
                )

            async with self.transaction_context() as session:
                # Check if user exists
                user = await self.db["users"].find_one(
                    {"user_id": user_id}, session=session
                )

                if not user:
                    return AdminError.validation_failed("user_id", "User not found")

                if not user.get("credits_frozen", False):
                    return AdminError.validation_failed(
                        "user_id", "User credits are not frozen"
                    )

                # Unfreeze credits
                result = await self.db["users"].update_one(
                    {"user_id": user_id},
                    {
                        "$set": {
                            "credits_frozen": False,
                            "credits_unfrozen_at": datetime.now(),
                            "credits_unfrozen_by": auth_context.user_id,
                            "credits_unfreeze_reason": reason,
                            "updated_at": datetime.now(),
                        },
                        "$unset": {
                            "credits_frozen_at": "",
                            "credits_frozen_by": "",
                            "credits_freeze_reason": "",
                        },
                    },
                    session=session,
                )

                if result.modified_count == 0:
                    return AdminError.transaction_failed("unfreeze credits")

                # Log audit
                await self._log_audit(
                    auth_context,
                    "UNFREEZE_CREDITS",
                    "users",
                    user_id,
                    {"reason": reason},
                    session,
                )

                logger.info(
                    f"Unfroze credits for user {user_id} by {auth_context.user_id}"
                )
                return AdminResult.success(True)

        except Exception as e:
            logger.error(f"Failed to unfreeze credits for user {user_id}: {e}")
            return AdminError.transaction_failed("unfreeze credits")

    @require_auth
    async def bulk_credit_adjustment(
        self, auth_context: AuthContext, adjustments: List[Dict[str, Any]]
    ) -> AdminResult[Dict[str, Any]]:
        """
        Apply credit adjustments to multiple users in a single transaction.

        Args:
            auth_context: Authentication context
            adjustments: List of credit adjustments, each containing user_id, credit_adjustment, reason

        Returns:
            AdminResult containing summary of bulk adjustment operation
        """
        try:
            if not adjustments:
                return AdminError.validation_failed(
                    "adjustments", "No credit adjustments provided"
                )

            # Validate each adjustment entry
            for i, adjustment in enumerate(adjustments):
                required_fields = ["user_id", "credit_adjustment", "reason"]
                missing_fields = [
                    field
                    for field in required_fields
                    if field not in adjustment or adjustment[field] is None
                ]

                if missing_fields:
                    return AdminError.validation_failed(
                        "adjustments",
                        f"Entry {i} missing required fields: {', '.join(missing_fields)}",
                    )

                if (
                    not isinstance(adjustment["credit_adjustment"], int)
                    or adjustment["credit_adjustment"] == 0
                ):
                    return AdminError.validation_failed(
                        "adjustments",
                        f"Entry {i} has invalid credit_adjustment: must be non-zero integer",
                    )

            async with self.transaction_context() as session:
                adjusted_count = 0
                failed_adjustments = []
                batch_id = str(ObjectId())  # Common batch ID for all adjustments

                for adjustment in adjustments:
                    try:
                        user_id = adjustment["user_id"]
                        credit_adjustment = adjustment["credit_adjustment"]
                        reason = adjustment["reason"]

                        # Verify user exists
                        user = await self.db["users"].find_one(
                            {"user_id": user_id}, session=session
                        )

                        if not user:
                            failed_adjustments.append(
                                {"user_id": user_id, "reason": "User not found"}
                            )
                            continue

                        # Check if adjustment would result in negative total credits
                        current_credits = user.get("total_credits", 0)
                        if current_credits + credit_adjustment < 0:
                            failed_adjustments.append(
                                {
                                    "user_id": user_id,
                                    "reason": f"Would result in negative credits: {current_credits} + {credit_adjustment}",
                                }
                            )
                            continue

                        # Generate credit record ID
                        credit_record_id = str(ObjectId())

                        # Prepare credit adjustment record
                        credit_record = {
                            "credit_id": credit_record_id,
                            "user_id": user_id,
                            "competition_id": "BULK_ADJUSTMENT",
                            "qualification_id": "BULK_ADJUSTMENT",
                            "credit": credit_adjustment,
                            "reason": reason,
                            "adjustment_type": "BULK_ADJUSTMENT",
                            "batch_id": batch_id,
                            "admin_id": auth_context.user_id,
                            "created_at": datetime.now(),
                            "revoked_at": None,
                        }

                        # Insert credit record
                        await self.db["credit_logs"].insert_one(
                            credit_record, session=session
                        )

                        # Update user's total credits
                        await self.db["users"].update_one(
                            {"user_id": user_id},
                            {
                                "$inc": {"total_credits": credit_adjustment},
                                "$set": {"updated_at": datetime.now()},
                            },
                            session=session,
                        )

                        adjusted_count += 1

                        # Log audit for each successful adjustment
                        await self._log_audit(
                            auth_context,
                            "BULK_ADJUST_CREDITS",
                            "credit_logs",
                            credit_record_id,
                            {
                                "user_id": user_id,
                                "credit_adjustment": credit_adjustment,
                                "batch_id": batch_id,
                            },
                            session,
                        )

                    except Exception as e:
                        failed_adjustments.append(
                            {
                                "user_id": adjustment.get("user_id", "unknown"),
                                "reason": str(e),
                            }
                        )

                # Log bulk operation audit
                await self._log_audit(
                    auth_context,
                    "BULK_ADJUSTMENT_OPERATION",
                    "credit_logs",
                    "bulk",
                    {
                        "batch_id": batch_id,
                        "total_requested": len(adjustments),
                        "successful_adjustments": adjusted_count,
                        "failed_adjustments": len(failed_adjustments),
                    },
                    session,
                )

                result_data = {
                    "batch_id": batch_id,
                    "total_requested": len(adjustments),
                    "successful_adjustments": adjusted_count,
                    "failed_adjustments": failed_adjustments,
                }

                logger.info(
                    f"Bulk adjusted credits for {adjusted_count}/{len(adjustments)} users by {auth_context.user_id}"
                )
                return AdminResult.success(result_data)

        except Exception as e:
            logger.error(f"Failed to bulk adjust credits: {e}")
            return AdminError.transaction_failed("bulk adjust credits")

    @require_auth
    async def recalculate_user_credits(
        self, auth_context: AuthContext, user_id: str
    ) -> AdminResult[Dict[str, Any]]:
        """
        Recalculate a user's total credits from credit logs.

        Args:
            auth_context: Authentication context
            user_id: ID of the user whose credits to recalculate

        Returns:
            AdminResult containing recalculation summary
        """
        try:
            if not user_id or not user_id.strip():
                return AdminError.validation_failed("user_id", "User ID is required")

            async with self.transaction_context() as session:
                # Check if user exists
                user = await self.db["users"].find_one(
                    {"user_id": user_id}, session=session
                )

                if not user:
                    return AdminError.validation_failed("user_id", "User not found")

                # Calculate total credits from credit logs
                pipeline = [
                    {"$match": {"user_id": user_id, "revoked_at": None}},
                    {"$group": {"_id": None, "total_credits": {"$sum": "$credit"}}},
                ]

                result = (
                    await self.db["credit_logs"]
                    .aggregate(pipeline, session=session)
                    .to_list(length=1)
                )
                calculated_total = result[0]["total_credits"] if result else 0

                current_total = user.get("total_credits", 0)

                # Update user's total credits if different
                if calculated_total != current_total:
                    await self.db["users"].update_one(
                        {"user_id": user_id},
                        {
                            "$set": {
                                "total_credits": calculated_total,
                                "credits_recalculated_at": datetime.now(),
                                "credits_recalculated_by": auth_context.user_id,
                                "updated_at": datetime.now(),
                            }
                        },
                        session=session,
                    )

                    # Log audit
                    await self._log_audit(
                        auth_context,
                        "RECALCULATE_CREDITS",
                        "users",
                        user_id,
                        {
                            "previous_total": current_total,
                            "calculated_total": calculated_total,
                            "difference": calculated_total - current_total,
                        },
                        session,
                    )

                result_data = {
                    "user_id": user_id,
                    "previous_total": current_total,
                    "calculated_total": calculated_total,
                    "difference": calculated_total - current_total,
                    "updated": calculated_total != current_total,
                }

                logger.info(
                    f"Recalculated credits for user {user_id}: {current_total} -> {calculated_total} by {auth_context.user_id}"
                )
                return AdminResult.success(result_data)

        except Exception as e:
            logger.error(f"Failed to recalculate credits for user {user_id}: {e}")
            return AdminError.transaction_failed("recalculate credits")
