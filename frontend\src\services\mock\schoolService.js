/**
 * Mock School API Service
 */

import { initializeMockData } from './mockData.js'

// Initialize mock data
const mockData = initializeMockData()
let { schools, users } = mockData

// Helper functions
const delay = (ms = 500) => new Promise(resolve => setTimeout(resolve, ms))

const paginate = (data, page = 1, limit = 20) => {
  const offset = (page - 1) * limit
  const paginatedData = data.slice(offset, offset + limit)
  
  return {
    data: paginatedData,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: data.length,
      pages: Math.ceil(data.length / limit),
      hasNext: offset + limit < data.length,
      hasPrev: page > 1
    }
  }
}

const filterSchools = (schools, params) => {
  let filtered = [...schools]
  
  if (params.type) {
    filtered = filtered.filter(school => school.type === params.type)
  }
  
  if (params.level) {
    filtered = filtered.filter(school => school.level === params.level)
  }
  
  if (params.status) {
    filtered = filtered.filter(school => school.status === params.status)
  }
  
  if (params.country) {
    filtered = filtered.filter(school => school.country === params.country)
  }
  
  if (params.province) {
    filtered = filtered.filter(school => school.province === params.province)
  }
  
  if (params.city) {
    filtered = filtered.filter(school => school.city === params.city)
  }
  
  if (params.search) {
    const searchLower = params.search.toLowerCase()
    filtered = filtered.filter(school => 
      school.name.toLowerCase().includes(searchLower) ||
      school.shortName?.toLowerCase().includes(searchLower) ||
      school.description?.toLowerCase().includes(searchLower)
    )
  }
  
  if (params.isVerified !== undefined) {
    filtered = filtered.filter(school => school.isVerified === params.isVerified)
  }
  
  if (params.isPartner !== undefined) {
    filtered = filtered.filter(school => school.isPartner === params.isPartner)
  }
  
  // Sorting
  if (params.sortBy) {
    filtered.sort((a, b) => {
      let aVal = a[params.sortBy]
      let bVal = b[params.sortBy]
      
      if (params.sortBy === 'participationRate') {
        aVal = a.stats.participationRate
        bVal = b.stats.participationRate
      } else if (params.sortBy === 'averageScore') {
        aVal = a.stats.averageScore
        bVal = b.stats.averageScore
      }
      
      if (typeof aVal === 'string') {
        aVal = aVal.toLowerCase()
        bVal = bVal.toLowerCase()
      }
      
      if (params.sortOrder === 'desc') {
        return bVal > aVal ? 1 : -1
      }
      return aVal > bVal ? 1 : -1
    })
  }
  
  return filtered
}

// Mock API functions
export const schoolService = {
  // Get schools list
  async getSchools(params = {}) {
    await delay()
    
    try {
      const filtered = filterSchools(schools, params)
      const result = paginate(filtered, params.page, params.limit)
      
      return {
        success: true,
        ...result
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'FETCH_ERROR',
          message: 'Failed to fetch schools'
        }
      }
    }
  },

  // Get school by ID
  async getSchool(id) {
    await delay()
    
    try {
      const school = schools.find(s => s.id === id)
      
      if (!school) {
        return {
          success: false,
          error: {
            code: 'SCHOOL_NOT_FOUND',
            message: 'School not found'
          }
        }
      }
      
      return {
        success: true,
        data: school
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'FETCH_ERROR',
          message: 'Failed to fetch school'
        }
      }
    }
  },

  // Create school
  async createSchool(schoolData) {
    await delay()
    
    try {
      const newSchool = {
        id: `school_${schools.length + 1}`,
        ...schoolData,
        registeredAt: new Date().toISOString(),
        lastActiveAt: new Date().toISOString(),
        isVerified: false,
        isPartner: false,
        stats: {
          totalStudents: schoolData.studentCount || 0,
          activeStudents: 0,
          totalCompetitions: 0,
          totalSubmissions: 0,
          totalCreditsEarned: 0,
          averageScore: 0,
          participationRate: 0,
          completionRate: 0,
          winRate: 0
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      schools.push(newSchool)
      
      return {
        success: true,
        data: newSchool,
        message: 'School created successfully'
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'CREATE_ERROR',
          message: 'Failed to create school'
        }
      }
    }
  },

  // Get school statistics
  async getSchoolStats() {
    await delay()
    
    try {
      const totalSchools = schools.length
      const totalStudents = schools.reduce((sum, s) => sum + s.stats.totalStudents, 0)
      const averageParticipationRate = schools.reduce((sum, s) => sum + s.stats.participationRate, 0) / schools.length
      
      const topPerformingSchools = schools
        .sort((a, b) => b.stats.averageScore - a.stats.averageScore)
        .slice(0, 10)
        .map((school, index) => ({
          school,
          rank: index + 1,
          score: school.stats.averageScore,
          metrics: {
            participationRate: school.stats.participationRate,
            averageScore: school.stats.averageScore,
            totalCredits: school.stats.totalCreditsEarned
          }
        }))
      
      const geographicDistribution = schools.reduce((acc, s) => {
        acc[s.country] = (acc[s.country] || 0) + 1
        return acc
      }, {})
      
      const typeDistribution = schools.reduce((acc, s) => {
        acc[s.type] = (acc[s.type] || 0) + 1
        return acc
      }, {})
      
      const levelDistribution = schools.reduce((acc, s) => {
        acc[s.level] = (acc[s.level] || 0) + 1
        return acc
      }, {})
      
      const stats = {
        totalSchools,
        totalStudents,
        averageParticipationRate: Math.round(averageParticipationRate * 100) / 100,
        topPerformingSchools,
        geographicDistribution,
        typeDistribution,
        levelDistribution
      }
      
      return {
        success: true,
        data: stats
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'STATS_ERROR',
          message: 'Failed to fetch school statistics'
        }
      }
    }
  }
}
