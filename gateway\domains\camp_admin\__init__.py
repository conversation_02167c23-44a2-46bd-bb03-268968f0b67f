"""
Competitions domain module.

这里收录的是一般的活动或比赛的逻辑，夏令营的专属逻辑可以在 admin 或是 camp 中实现

Handles all competition-related business logic including:
- Routes (high-level categories)
- Competitions (events within routes)
- Qualifications (standards for earning credits)
- Credits (points earned through qualifications)
- Badges (rewards for accumulated credits)
"""

from .routes import router

__all__ = ["router"]
