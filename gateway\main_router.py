"""
Main Gateway Router.

Consolidates all domain routes into a single router for the gateway service.
"""

from contextlib import asynccontextmanager
from fastapi import APIRouter, FastAPI

# Import core infrastructure
from core.config import logger, settings, setup_logging
from core.config.tunnel_manager import tunnel_manager
from core.database.database import init_database, close_database
from core.database.mongo_db import mongo_manager
# from core.container import initialize_container

# Apply modern logging configuration
setup_logging()

# Import domain routers
from .domains.users.routes import router as users_router
from .domains.camp_admin.routes import router as camp_admin_router
from .domains.competitions.routes import router as competitions_router
from .domains.community.routes import router as community_router
from .domains.analytics.routes import router as analytics_router

# # Import integration and admin routers
# from .integrations.routes import router as integrations_router
# from .admin.router import router as admin_router

# Import exception handlers for registration
from libs.exceptions.handlers import (
    # Legacy exceptions (keeping for compatibility)
    UnicornException,
    Wrong<PERSON>ontentErr,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>rr,
    <PERSON><PERSON><PERSON><PERSON>,
    unicorn_exception_handler,
    wrong_content_handler,
    run_out_time_handler,
    oath_error_handler,
    
    # Database exceptions
    DatabaseConnectionError,
    MongoDBError,
    CacheError,
    PostgreSQLError,
    database_connection_handler,
    mongodb_error_handler,
    cache_error_handler,
    postgresql_error_handler,
    pg_does_not_exist_handler,
    pg_operational_error_handler,
    
    # Service exceptions
    ServiceException,
    ExternalServiceError,
    BusinessLogicError,
    service_exception_handler,
    external_service_error_handler,
    business_logic_error_handler,
    
    # Infrastructure exceptions
    ResourceExhaustionError,
    ConfigurationError,
    NetworkError,
    resource_exhaustion_handler,
    configuration_error_handler,
    network_error_handler,
    
    # Security exceptions
    AuthenticationError,
    AuthorizationError,
    RateLimitError,
    authentication_error_handler,
    authorization_error_handler,
    rate_limit_error_handler,
    
    # HTTP and validation handlers
    http_error_handler,
    http422_error_handler,
    timeout_error_handler,
    client_error_handler,
    general_handler,
)
from fastapi import HTTPException
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
from tortoise.exceptions import OperationalError, DoesNotExist
from aiohttp.client_exceptions import ServerTimeoutError, ClientError
import asyncio


# ——— Exception Handler Registration ———
def register_exception_handlers(app: FastAPI):
    """Register all custom exception handlers with FastAPI."""
    
    # Database exceptions (highest priority for database operations)
    app.add_exception_handler(DatabaseConnectionError, database_connection_handler)
    app.add_exception_handler(MongoDBError, mongodb_error_handler)
    app.add_exception_handler(CacheError, cache_error_handler)
    app.add_exception_handler(PostgreSQLError, postgresql_error_handler)
    app.add_exception_handler(DoesNotExist, pg_does_not_exist_handler)
    app.add_exception_handler(OperationalError, pg_operational_error_handler)
    
    # Service exceptions
    app.add_exception_handler(ServiceException, service_exception_handler)
    app.add_exception_handler(ExternalServiceError, external_service_error_handler)
    app.add_exception_handler(BusinessLogicError, business_logic_error_handler)
    
    # Infrastructure exceptions
    app.add_exception_handler(ResourceExhaustionError, resource_exhaustion_handler)
    app.add_exception_handler(ConfigurationError, configuration_error_handler)
    app.add_exception_handler(NetworkError, network_error_handler)
    
    # Security exceptions
    app.add_exception_handler(AuthenticationError, authentication_error_handler)
    app.add_exception_handler(AuthorizationError, authorization_error_handler)
    app.add_exception_handler(RateLimitError, rate_limit_error_handler)
    
    # Legacy application exceptions (for backward compatibility)
    app.add_exception_handler(UnicornException, unicorn_exception_handler)
    app.add_exception_handler(WrongContentErr, wrong_content_handler)
    app.add_exception_handler(RunOutTimeErr, run_out_time_handler)
    app.add_exception_handler(OathErr, oath_error_handler)
    
    # HTTP and validation exceptions
    app.add_exception_handler(HTTPException, http_error_handler)
    app.add_exception_handler(RequestValidationError, http422_error_handler)
    app.add_exception_handler(ValidationError, http422_error_handler)
    
    # Network and timeout exceptions
    app.add_exception_handler(ServerTimeoutError, timeout_error_handler)
    app.add_exception_handler(asyncio.TimeoutError, timeout_error_handler)
    app.add_exception_handler(ClientError, client_error_handler)
    
    # Catch-all exception handler (must be last)
    app.add_exception_handler(Exception, general_handler)
    
    logger.info("Comprehensive exception handlers registered successfully")
    logger.info("Registered exception types: Database, Service, Infrastructure, Security, HTTP, Legacy")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan context manager.
    
    Handles initialization and cleanup of:
    - SSH tunnel connections
    - MongoDB connections
    - Tortoise ORM (PostgreSQL)
    - Service container
    """
    logger.info("Gateway application starting up...")
    
    try:
        # ——— Startup sequence ———
        
        # 1. Start SSH tunnel if enabled
        if settings.ssh_tunnel_enabled:
            logger.info("Starting SSH tunnel...")
            await tunnel_manager.start()
            if tunnel_manager.is_active:
                logger.success(f"SSH tunnel active on port {tunnel_manager.local_bind_port}")
            else:
                logger.error("SSH tunnel failed to start")
                raise RuntimeError("SSH tunnel startup failed")
        else:
            logger.info("SSH tunnel disabled in configuration")

        # 2. Initialize MongoDB connection
        logger.info("Connecting to MongoDB...")
        await mongo_manager.connect()
        if mongo_manager.is_connected:
            logger.success("MongoDB connected successfully")
        else:
            logger.error("MongoDB connection failed")
            raise RuntimeError("MongoDB connection failed")

        # 3. Initialize Tortoise ORM
        logger.info("Initializing Tortoise ORM...")
        await init_database()
        logger.success("Tortoise ORM initialized successfully")

        # 4. Initialize service container
        # logger.info("Initializing service container...")
        # container = initialize_container()
        # logger.success("Service container initialized successfully")
        
        # Log registered services for debugging
        # services = container.list_services()
        # logger.info(f"Registered query services: {list(services['queries'].keys())}")
        # logger.info(f"Registered admin services: {list(services['admin'].keys())}")
        # logger.info(f"Registered integration services: {list(services['integrations'].keys())}")

        # 5. Perform health checks
        logger.info("Performing startup health checks...")
        
        # MongoDB health check
        mongo_healthy = await mongo_manager.health_check()
        if not mongo_healthy:
            logger.warning("MongoDB health check failed")
        
        # SSH tunnel health check
        tunnel_healthy = True
        if settings.ssh_tunnel_enabled:
            tunnel_healthy = await tunnel_manager.health_check()
            if not tunnel_healthy:
                logger.warning("SSH tunnel health check failed")
        
        logger.success("Gateway application startup completed successfully")
        
        # Yield control to the application
        yield
        
    except Exception as e:
        logger.error(f"Gateway application startup failed: {e}")
        # Ensure cleanup even on startup failure
        await _cleanup_resources()
        raise
    
    finally:
        # ——— Shutdown sequence ———
        logger.info("Gateway application shutting down...")
        await _cleanup_resources()
        logger.info("Gateway application shutdown completed")


async def _cleanup_resources():
    """Clean up all resources during shutdown."""
    cleanup_errors = []
    
    # 1. Close Tortoise ORM connections
    try:
        logger.info("Closing Tortoise ORM connections...")
        await close_database()
        logger.success("Tortoise ORM connections closed")
    except Exception as e:
        logger.error(f"Error closing Tortoise ORM: {e}")
        cleanup_errors.append(("Tortoise ORM", e))
    
    # 2. Close MongoDB connection
    try:
        logger.info("Closing MongoDB connection...")
        await mongo_manager.disconnect()
        logger.success("MongoDB connection closed")
    except Exception as e:
        logger.error(f"Error closing MongoDB: {e}")
        cleanup_errors.append(("MongoDB", e))
    
    # 3. Stop SSH tunnel if active
    if settings.ssh_tunnel_enabled:
        try:
            logger.info("Stopping SSH tunnel...")
            await tunnel_manager.stop()
            logger.success("SSH tunnel stopped")
        except Exception as e:
            logger.error(f"Error stopping SSH tunnel: {e}")
            cleanup_errors.append(("SSH tunnel", e))
    
    # Log any cleanup errors
    if cleanup_errors:
        error_summary = ", ".join([f"{service}: {error}" for service, error in cleanup_errors])
        logger.warning(f"Cleanup completed with errors: {error_summary}")
    else:
        logger.success("All resources cleaned up successfully")


# Create main router
router = APIRouter()

# Include all domain routes
router.include_router(users_router)
router.include_router(camp_admin_router)
router.include_router(competitions_router)
router.include_router(community_router)
router.include_router(analytics_router)

# # Include integration and admin routes
# router.include_router(integrations_router)
# router.include_router(admin_router, prefix="/admin")


# Health check endpoint for the gateway
@router.get("/health")
async def gateway_health():
    """Gateway service health check."""
    health_status = {
        "status": "healthy",
        "service": "gateway",
        "domains": ["users", "competitions", "community", "analytics"],
        "integrations": ["shence", "heywhale", "webhook"],
        "admin": True,
        "connections": {}
    }
    
    # Check MongoDB health
    try:
        mongo_healthy = await mongo_manager.health_check()
        health_status["connections"]["mongodb"] = "healthy" if mongo_healthy else "unhealthy"
    except Exception as e:
        health_status["connections"]["mongodb"] = f"error: {str(e)}"
    
    # Check SSH tunnel health if enabled
    if settings.ssh_tunnel_enabled:
        try:
            tunnel_healthy = await tunnel_manager.health_check()
            health_status["connections"]["ssh_tunnel"] = "healthy" if tunnel_healthy else "unhealthy"
        except Exception as e:
            health_status["connections"]["ssh_tunnel"] = f"error: {str(e)}"
    else:
        health_status["connections"]["ssh_tunnel"] = "disabled"
    
    # Determine overall status
    connection_statuses = [v for v in health_status["connections"].values() if isinstance(v, str)]
    if any("unhealthy" in status or "error" in status for status in connection_statuses):
        health_status["status"] = "degraded"
    
    return health_status


# Create FastAPI app with lifespan and proper configuration
app = FastAPI(
    title=settings.api_title,
    description=settings.api_description,
    version=settings.api_version,
    debug=settings.debug,
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/api/v1/openapi.json",  # Standardized OpenAPI URL
    lifespan=lifespan,
)

# Register all exception handlers
register_exception_handlers(app)

# Include the main router with API prefix
app.include_router(router, prefix="/api/v1")