import streamlit as st
from libs.utils import add_user
# from pages.login import login_pg


def app():
    st.title("社区管理员注册😀")

    register_form = st.form("register_form")
    email = register_form.text_input("Email", placeholder="输入你的邮箱")
    password = register_form.text_input(
        "Password", type="password", placeholder="密码长度至少为8位"
    )
    user_name = register_form.text_input(
        "User Name",
        placeholder="选填：输入你的用户名",
    )
    register_button = register_form.form_submit_button("注册", use_container_width=True)
    st.page_link("logins.py", label="返回登录")

    if register_button:
        message, status = add_user(email, password, user_name)
        if not status:
            st.error(message)
        else:
            st.success("Registration successful! Please log in.")


register_pg = st.Page(app, title="注册页面", url_path="registers")
