"""
Library utilities for the API Gateway Service.

This module contains pure utility functions, data structures, and classes
that are used for imports and references across the application.
These are passive utilities with no runtime side effects.
"""

# Data models and schemas
from .models.base_models import *
from .schemas.api.base import BaseResp, ResAntTable, WebsocketMessage
from .schemas.api.responses import success, fail, res_antd
from .schemas.api.common import *
from .schemas.events.base import BaseEvent, EventType, EventPriority

# Exception handling
from .exceptions.handlers import (
    UnicornException,
    WrongContentErr,
    RunOutTimeErr,
    OathErr,
    pg_does_not_exist_handler,
    pg_operational_error_handler,
    http_error_handler,
    unicorn_exception_handler,
    wrong_content_handler,
    run_out_time_handler,
    oath_error_handler,
    http422_error_handler,
    timeout_error_handler,
    general_handler,
)

# Authentication utilities
from .auth.context import AuthContext, require_auth, create_auth_context, validate_user_id
from .auth.authentication import (
    get_current_user_id,
    get_current_admin_id,
    get_current_user,
    require_authentication,
    require_admin_authentication,
    log_analytics_access,
    validate_jwt_token,
    get_api_key,
    require_api_key,
)
from .auth.permissions import (
    BasePermissions,
    ResourcePermissions,
    RolePermissions,
    require_permission,
    require_role,
    require_any_role,
    require_resource_access,
    require_resource_ownership,
    check_resource_access,
    check_user_role,
    permission_required,
)

# Validation utilities
from .validation import (
    validate_pagination,
    validate_date_format,
    validate_datetime_format,
    validate_email_format,
    validate_phone_format,
    validate_url_format,
    validate_uuid_format,
    validate_id_format,
    validate_sort_parameters,
    validate_filter_parameters,
    validate_search_query,
    validate_comma_separated_list,
    validate_date_range,
    validate_user_profile_data,
    validate_identifier_type,
    validate_discovery_criteria,
    validate_search_parameters,
)

# Error handling utilities
from .errors import (
    ErrorCode,
    ErrorCodeMapping,
    create_error_data,
    log_domain_error,
)

# SQL utilities
from .sql_utils import process_sql_template, execute_query, add_extra_braces, escape_backslashes

__all__ = [
    # Models and schemas
    "BaseResp",
    "ResAntTable", 
    "WebsocketMessage",
    "success",
    "fail",
    "res_antd",
    "BaseEvent",
    "EventType",
    "EventPriority",
    
    # Exception handling
    "UnicornException",
    "WrongContentErr",
    "RunOutTimeErr",
    "OathErr",
    "pg_does_not_exist_handler",
    "pg_operational_error_handler",
    "http_error_handler",
    "unicorn_exception_handler",
    "wrong_content_handler",
    "run_out_time_handler",
    "oath_error_handler",
    "http422_error_handler",
    "timeout_error_handler",
    "general_handler",
    
    # Authentication
    "AuthContext",
    "require_auth",
    "create_auth_context",
    "validate_user_id",
    "get_current_user_id",
    "get_current_admin_id",
    "get_current_user",
    "require_authentication",
    "require_admin_authentication",
    "log_analytics_access",
    "validate_jwt_token",
    "get_api_key",
    "require_api_key",
    "BasePermissions",
    "ResourcePermissions",
    "RolePermissions",
    "require_permission",
    "require_role",
    "require_any_role",
    "require_resource_access",
    "require_resource_ownership",
    "check_resource_access",
    "check_user_role",
    "permission_required",
    
    # Validation
    "validate_pagination",
    "validate_date_format",
    "validate_datetime_format",
    "validate_email_format",
    "validate_phone_format",
    "validate_url_format",
    "validate_uuid_format",
    "validate_id_format",
    "validate_sort_parameters",
    "validate_filter_parameters",
    "validate_search_query",
    "validate_comma_separated_list",
    "validate_date_range",
    "validate_user_profile_data",
    "validate_identifier_type",
    "validate_discovery_criteria",
    "validate_search_parameters",
    
    # Error handling
    "ErrorCode",
    "ErrorCodeMapping",
    "create_error_data",
    "log_domain_error",
    
    # SQL utilities
    "process_sql_template",
    "execute_query",
    "add_extra_braces",
    "escape_backslashes",
] 