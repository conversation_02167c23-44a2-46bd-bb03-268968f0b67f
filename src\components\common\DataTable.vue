<template>
  <div class="data-table">
    <!-- Table Header with Search and Filters -->
    <div class="table-header" v-if="showHeader">
      <div class="table-title">
        <h3 v-if="title">{{ title }}</h3>
        <p v-if="description" class="table-description">{{ description }}</p>
      </div>
      
      <div class="table-actions">
        <!-- Search -->
        <el-input
          v-if="searchable"
          v-model="searchQuery"
          placeholder="Search..."
          :prefix-icon="Search"
          clearable
          class="search-input"
          @input="handleSearch"
        />
        
        <!-- Custom actions slot -->
        <slot name="actions" />
        
        <!-- Refresh button -->
        <el-button
          v-if="refreshable"
          :icon="Refresh"
          @click="handleRefresh"
          :loading="loading"
        />
      </div>
    </div>

    <!-- Table -->
    <el-table
      :data="paginatedData"
      :loading="loading"
      v-bind="tableProps"
      @sort-change="handleSortChange"
      @selection-change="handleSelectionChange"
      class="main-table"
    >
      <!-- Selection column -->
      <el-table-column
        v-if="selectable"
        type="selection"
        width="55"
        :selectable="selectableFunction"
      />
      
      <!-- Index column -->
      <el-table-column
        v-if="showIndex"
        type="index"
        label="#"
        width="60"
        :index="getIndex"
      />
      
      <!-- Dynamic columns -->
      <el-table-column
        v-for="column in columns"
        :key="column.key"
        :prop="column.key"
        :label="column.label"
        :width="column.width"
        :min-width="column.minWidth"
        :sortable="column.sortable ? 'custom' : false"
        :align="column.align || 'left'"
        :show-overflow-tooltip="column.showOverflowTooltip !== false"
      >
        <template #default="{ row, column: tableColumn, $index }">
          <!-- Custom column slot -->
          <slot
            :name="`column-${column.key}`"
            :row="row"
            :column="column"
            :value="getColumnValue(row, column.key)"
            :index="$index"
          >
            <!-- Default column rendering -->
            <span v-if="column.formatter">
              {{ column.formatter(getColumnValue(row, column.key), row, $index) }}
            </span>
            <span v-else>
              {{ getColumnValue(row, column.key) }}
            </span>
          </slot>
        </template>
      </el-table-column>
      
      <!-- Actions column -->
      <el-table-column
        v-if="actions && actions.length > 0"
        label="Actions"
        :width="actionsWidth"
        align="center"
        fixed="right"
      >
        <template #default="{ row, $index }">
          <slot name="actions" :row="row" :index="$index">
            <div class="table-actions-cell">
              <el-button
                v-for="action in getRowActions(row)"
                :key="action.key"
                :type="action.type || 'primary'"
                :size="action.size || 'small'"
                :icon="action.icon"
                :disabled="action.disabled"
                @click="action.handler(row, $index)"
                link
              >
                {{ action.label }}
              </el-button>
            </div>
          </slot>
        </template>
      </el-table-column>
      
      <!-- Empty state -->
      <template #empty>
        <slot name="empty">
          <div class="empty-state">
            <el-empty :description="emptyText" />
          </div>
        </slot>
      </template>
    </el-table>

    <!-- Pagination -->
    <div class="table-footer" v-if="showPagination && pagination">
      <div class="pagination-info">
        <span>
          Showing {{ paginationInfo.start }} to {{ paginationInfo.end }} 
          of {{ paginationInfo.total }} entries
        </span>
      </div>
      
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="pageSizes"
        :total="pagination.total"
        :layout="paginationLayout"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { Search, Refresh } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  // Data
  data: {
    type: Array,
    default: () => []
  },
  columns: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  
  // Table configuration
  title: String,
  description: String,
  showHeader: {
    type: Boolean,
    default: true
  },
  searchable: {
    type: Boolean,
    default: true
  },
  refreshable: {
    type: Boolean,
    default: true
  },
  selectable: {
    type: Boolean,
    default: false
  },
  showIndex: {
    type: Boolean,
    default: false
  },
  
  // Pagination
  showPagination: {
    type: Boolean,
    default: true
  },
  pagination: {
    type: Object,
    default: null
  },
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100]
  },
  paginationLayout: {
    type: String,
    default: 'total, sizes, prev, pager, next, jumper'
  },
  
  // Actions
  actions: {
    type: Array,
    default: () => []
  },
  actionsWidth: {
    type: [String, Number],
    default: 150
  },
  
  // Table props
  tableProps: {
    type: Object,
    default: () => ({})
  },
  
  // Misc
  emptyText: {
    type: String,
    default: 'No data available'
  },
  selectableFunction: {
    type: Function,
    default: () => true
  }
})

// Emits
const emit = defineEmits([
  'search',
  'refresh',
  'sort-change',
  'selection-change',
  'page-change',
  'size-change'
])

// Reactive data
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(20)

// Computed
const paginatedData = computed(() => {
  if (props.pagination) {
    // Server-side pagination
    return props.data
  } else {
    // Client-side pagination
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    return filteredData.value.slice(start, end)
  }
})

const filteredData = computed(() => {
  if (!searchQuery.value) return props.data
  
  const query = searchQuery.value.toLowerCase()
  return props.data.filter(row => {
    return props.columns.some(column => {
      const value = getColumnValue(row, column.key)
      return String(value).toLowerCase().includes(query)
    })
  })
})

const paginationInfo = computed(() => {
  const total = props.pagination ? props.pagination.total : filteredData.value.length
  const start = total === 0 ? 0 : (currentPage.value - 1) * pageSize.value + 1
  const end = Math.min(currentPage.value * pageSize.value, total)
  
  return { start, end, total }
})

// Methods
const getColumnValue = (row, key) => {
  return key.split('.').reduce((obj, k) => obj?.[k], row)
}

const getIndex = (index) => {
  return (currentPage.value - 1) * pageSize.value + index + 1
}

const getRowActions = (row) => {
  return props.actions.filter(action => {
    if (typeof action.visible === 'function') {
      return action.visible(row)
    }
    return action.visible !== false
  })
}

const handleSearch = () => {
  currentPage.value = 1
  emit('search', searchQuery.value)
}

const handleRefresh = () => {
  emit('refresh')
}

const handleSortChange = (sortInfo) => {
  emit('sort-change', sortInfo)
}

const handleSelectionChange = (selection) => {
  emit('selection-change', selection)
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  emit('page-change', { page, pageSize: pageSize.value })
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  emit('size-change', { page: 1, pageSize: size })
}

// Watch for pagination changes
watch(() => props.pagination, (newPagination) => {
  if (newPagination) {
    currentPage.value = newPagination.page
    pageSize.value = newPagination.limit
  }
}, { immediate: true })

// Initialize
onMounted(() => {
  if (props.pagination) {
    currentPage.value = props.pagination.page || 1
    pageSize.value = props.pagination.limit || 20
  }
})
</script>

<style lang="scss" scoped>
.data-table {
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
    gap: 16px;
    
    .table-title {
      flex: 1;
      
      h3 {
        margin: 0 0 4px 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
      
      .table-description {
        margin: 0;
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }
    
    .table-actions {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .search-input {
        width: 250px;
      }
    }
  }
  
  .main-table {
    margin-bottom: 16px;
    
    .table-actions-cell {
      display: flex;
      gap: 8px;
      justify-content: center;
    }
  }
  
  .table-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .pagination-info {
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
  }
  
  .empty-state {
    padding: 40px 0;
  }
}

@media (max-width: 768px) {
  .data-table {
    .table-header {
      flex-direction: column;
      align-items: stretch;
      
      .table-actions {
        .search-input {
          width: 100%;
        }
      }
    }
    
    .table-footer {
      flex-direction: column;
      gap: 16px;
      align-items: center;
    }
  }
}
</style>
