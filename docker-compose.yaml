version: "2.0"
services:
  community-workers:
    image: registry.cn-shanghai.aliyuncs.com/kcr-3rd/community_ds:community_ds_v1.1.2
    restart: always
    environment:
      - MONGO_URI=mongodb://kesci_readonly:<EMAIL>:3717/kesci
      - MONGO_USER=kesci_readonly
      - MONGO_PASSWORD=IBRtH03CjQmRwenq
      - MONGO_HOST=dds-uf6830e70dcf5fa41.mongodb.rds.aliyuncs.com
      - MONGO_PORT=3717
      - MONGO_DB=kesci
      - MYSQL_PWD=VhUdQSBX8H3NmTA
      - MYSQL_USER=developer
      - SSH_HOST=**************
      - SSH_PORT=22
      - SSH_USER=kescidb
      - SSH_PK_PATH=src/libs/opensshkey
    ports:
      - 5000:8000
    volumes:
      - ./logs:/src/logs
  mysql-db:
    image: mysql
    command: --default-authentication-plugin=mysql_native_password
    restart: always
    security_opt:
      - seccomp:unconfined
    environment:
      - MYSQL_ROOT_PASSWORD=6ZkcfJxjEU34d1j
      - MYSQL_DATABASE=heywhale
      - MYSQL_USER=developer
      - MYSQL_PASSWORD=VhUdQSBX8H3NmTA
      - TZ=Asia/Shanghai
    volumes:
      - mysql-data:/var/lib/mysql
    ports:
      - 14306:3306

volumes:
  mysql-data: