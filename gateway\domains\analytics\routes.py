"""
Analytics Domain Router.

NOTE: 
- this route is designed specifically for events or activities that REQUIRE stats displayed on the frontend.
- this route is NOT designed for data analysis or reporting or dashboarding.

Handles all analytics-related endpoints organized by business function:
- Rankings (school and user rankings)
- Platform statistics
- Event tracking and processing
- ShenCe analytics integration (tag management)

"""

import json
import os
from fastapi import APIRouter, HTTPException, Request, Depends, Body
import logging
import aiofiles
from typing import Optional
from libs.schemas.api import (
    EventTracksResponse,
)
from .schemas import (
    SchoolRanking,
    SchoolRankingResp,
    UserRankingEveryRoute,
    UserRankingEveryRouteResp,
    UserRankingTotalRoute,
    UserRankingTotalRouteResp,
    SummaryStatisticsResp,
    AnalyticsHealthResponse,
    TopNByRoute,
    TopNByRouteResp,
    ShenceTagCreateRequest,
    ShenceFileUploadRequest,
    ShenceTagUpdateRequest,
    ShenceDirectoryResponse,
    ShenceTagMetaResponse,
    ShenceUploadResponse,
    ShenceTagResponse,
)
from libs.auth.permissions import (
    require_ranking_access,
    require_statistics_access,
    require_event_tracking_access,
    require_shence_access_permission,
)
from libs.auth.authentication import log_analytics_access, get_current_user_id
from core.database import get_event_tracking_service
from libs.schemas.api.responses import success, ErrorResponse, SuccessResponse

# Import domain services directly
from .services import AnalyticsServices

logger = logging.getLogger(__name__)

# Create router with prefix for analytics endpoints
router = APIRouter(
    prefix="/analytics",
    tags=["analytics"],
    responses={404: {"description": "Not found"}},
)

# Initialize domain service
analytics_service = AnalyticsServices()

# ——— Health check endpoint ———


@router.get(
    "/health",
    response_model=AnalyticsHealthResponse,
    summary="Analytics service health check",
    description="Check the health status of analytics services",
)
async def analytics_health():
    """Check analytics service health."""
    try:
        event_service = await get_event_tracking_service()

        return AnalyticsHealthResponse(
            status="healthy",
            rankings_available=analytics_service is not None,
            statistics_available=analytics_service is not None,
            event_tracking_available=event_service is not None,
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}", exc_info=True)
        return AnalyticsHealthResponse(
            status="unhealthy",
            rankings_available=False,
            statistics_available=False,
            event_tracking_available=False,
        )


# ——— Camp-Only: Statistics endpoints ———


@router.post(
    "/camp-only/statistics/summary",
    response_model=SuccessResponse | ErrorResponse,
    summary="返回夏令营的统计数据",
    description="返回夏令营的核心统计数据",
)
async def get_summary_statistics(
    request: Request,
    # user_id: Optional[str] = Depends(require_statistics_access),
):
    """Get platform summary statistics."""
    try:
        result_data = await analytics_service.get_summary_statistics()

        # await log_analytics_access(request, user_id, "summary_statistics")
        return success(data=result_data)

    except Exception as e:
        logger.error(f"Error getting summary statistics: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# ——— Camp-Only: School ranking endpoints ———


@router.post(
    "/camp-only/rankings/schools",
    response_model=SuccessResponse | ErrorResponse,
    summary="返回学校排名",
    description="返回学校排名",
)
async def get_school_rankings(
    request: Request,
    params: SchoolRanking = Body(...),
    # user_id: str = Depends(require_ranking_access),
):
    """Get school rankings by total credits."""
    try:
        result_data = await analytics_service.get_school_ranking(
            page_size=params.page_size, current_page=params.current_page
        )

        # await log_analytics_access(request, user_id, "school_rankings")
        return success(data=result_data)    

    except Exception as e:
        logger.error(f"Error getting school rankings: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# ——— Camp-Only: User ranking endpoints ———


@router.post(
    "/camp-only/rankings/users/by_route",
    response_model=SuccessResponse | ErrorResponse,
    summary="返回用户排名",
    description="返回用户排名",
)
async def get_user_rankings_by_route(
    request: Request,
    params: UserRankingEveryRoute = Body(...),
    user_id: str = Depends(require_ranking_access),
):
    """Get user rankings for each competition route."""
    try:
        result_data = await analytics_service.get_user_ranking_by_route(
            top_n=params.top_num
        )

        await log_analytics_access(request, user_id, "user_rankings_by_route")
        return success(data=result_data)

    except Exception as e:
        logger.error(f"Error getting user rankings by route: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post(
    "/camp-only/rankings/users/total",
    response_model=SuccessResponse | ErrorResponse,
    summary="返回用户总排名",
    description="返回用户总排名",
)
async def get_total_user_rankings(
    request: Request,
    params: UserRankingTotalRoute = Body(...),
    user_id: str = Depends(require_ranking_access),
):
    """Get user rankings by total credits across all routes."""
    try:
        result_data = await analytics_service.get_user_ranking_total(
            page_size=params.page_size, current_page=params.current_page
        )

        await log_analytics_access(request, user_id, "total_user_rankings")
        return success(data=result_data)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting total user rankings: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post(
    "/camp-only/rankings/top_by_route",
    response_model=SuccessResponse | ErrorResponse,
    summary="返回每个赛道的top N用户",
    description="返回每个赛道的top N用户",
)
async def get_top_users_by_route(
    request: Request,
    params: TopNByRoute = Body(...),
    user_id: str = Depends(require_ranking_access),
):
    """Get top N users by route with aggregated data."""
    try:
        result_data = await analytics_service.get_topn_each_route(top_num=params.top_num)

        await log_analytics_access(request, user_id, "top_users_by_route")
        return success(data=result_data)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting top users by route: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# ——— Event tracking endpoints ———


@router.get(
    "/events/tracks",
    response_model=EventTracksResponse,
    summary="Process and retrieve event tracks",
    description="Read event tracks from file, process them, and return filtered data",
)
async def get_event_tracks(
    request: Request,
    user_id: str = Depends(require_event_tracking_access),
    event_service=Depends(get_event_tracking_service),
):
    """Process and retrieve event tracks."""
    try:
        if not event_service:
            raise HTTPException(
                status_code=503, detail="Event tracking service unavailable"
            )

        # NOTE: Event tracking logic remains unchanged for now.
        # TODO(ranking_service,2025-01-17): Consider moving to dedicated EventService.

        # Read and process event tracks from file
        file_path = "app/data/event_tracks.json"

        if not os.path.exists(file_path):
            return success(data={"events": [], "total": 0})

        async with aiofiles.open(file_path, mode="r", encoding="utf-8") as f:
            content = await f.read()

        if not content.strip():
            return success(data={"events": [], "total": 0})

        # Parse JSON data
        try:
            data = json.loads(content)
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in event tracks file: {e}")
            raise HTTPException(status_code=500, detail="Invalid event data format")

        # Filter events (example: only events from last 30 days)
        # This is a placeholder - implement actual filtering logic as needed
        filtered_events = data.get("events", []) if isinstance(data, dict) else data

        await log_analytics_access(request, user_id, "event_tracks")

        return success(data={"events": filtered_events, "total": len(filtered_events)})

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing event tracks: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# ——— Legacy endpoints (deprecated) ———


# @router.post(
#     "/summary_statistics",
#     response_model=SummaryStatisticsResp,
#     summary="[LEGACY] Get summary statistics",
#     description="Legacy endpoint for summary statistics - use /statistics/summary instead",
#     deprecated=True,
# )
# async def legacy_summary_statistics(
#     request: Request,
#     user_id: str = Depends(require_statistics_access),
# ):
#     """Legacy endpoint - redirects to new summary statistics endpoint."""
#     return await get_summary_statistics(request, user_id)


# @router.post(
#     "/school_rank",
#     response_model=SchoolRankingResp,
#     summary="[LEGACY] Get school rankings",
#     description="Legacy endpoint for school rankings - use /rankings/schools instead",
#     deprecated=True,
# )
# async def legacy_school_rankings(
#     request: Request,
#     params: SchoolRanking = Body(...),
#     user_id: str = Depends(require_ranking_access),
# ):
#     """Legacy endpoint - redirects to new school rankings endpoint."""
#     return await get_school_rankings(request, params, user_id)


# @router.post(
#     "/user_rank_every_route",
#     response_model=UserRankingEveryRouteResp,
#     summary="[LEGACY] Get user rankings by route",
#     description="Legacy endpoint for user rankings by route - use /rankings/users/by_route instead",
#     deprecated=True,
# )
# async def legacy_user_rankings_by_route(
#     request: Request,
#     params: UserRankingEveryRoute = Body(...),
#     user_id: str = Depends(require_ranking_access),
# ):
#     """Legacy endpoint - redirects to new user rankings by route endpoint."""
#     return await get_user_rankings_by_route(request, params, user_id)


# @router.post(
#     "/user_rank_total",
#     response_model=UserRankingTotalRouteResp,
#     summary="[LEGACY] Get total user rankings",
#     description="Legacy endpoint for total user rankings - use /rankings/users/total instead",
#     deprecated=True,
# )
# async def legacy_total_user_rankings(
#     request: Request,
#     params: UserRankingTotalRoute = Body(...),
#     user_id: str = Depends(require_ranking_access),
# ):
#     """Legacy endpoint - redirects to new total user rankings endpoint."""
#     return await get_total_user_rankings(request, params, user_id)


# ——— ShenCe Integration endpoints ———


@router.get(
    "/integrations/shence/directories",
    response_model=ShenceDirectoryResponse,
    summary="Get ShenCe directories",
    description="Get ShenCe tag directory metadata for analytics",
)
async def get_shence_directories(
    user_id: str = Depends(require_shence_access_permission),
):
    """Get ShenCe tag directory metadata."""
    try:
        from gateway.integrations.dependencies import (
            get_shence_dir_meta,
            get_shence_service,
        )

        await get_shence_service()
        result = await get_shence_dir_meta()

        if result.get("error"):
            raise HTTPException(
                status_code=500, detail=f"ShenCe API error: {result['error']}"
            )

        # Transform result to match schema
        response_data = {
            "directories": result.get("data", [])
            if isinstance(result, dict)
            else result,
            "total_count": len(result.get("data", []))
            if isinstance(result, dict)
            else len(result or []),
            "project": "production",  # TODO: Get from config
        }

        return success(data=response_data)
    except Exception as e:
        logger.error(f"Error getting ShenCe directories: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get(
    "/integrations/shence/tags/{tag_id}/meta",
    response_model=ShenceTagMetaResponse,
    summary="Get ShenCe tag metadata",
    description="Get metadata for a specific ShenCe tag",
)
async def get_shence_tag_metadata(
    tag_id: str,
    user_id: str = Depends(require_shence_access_permission),
):
    """Get ShenCe tag metadata."""
    try:
        from gateway.integrations.dependencies import (
            get_shence_tag_meta,
            get_shence_service,
        )

        await get_shence_service()
        result = await get_shence_tag_meta(tag_id)

        if result.get("error"):
            raise HTTPException(
                status_code=404, detail=f"ShenCe tag not found: {result['error']}"
            )

        return success(data=result)
    except Exception as e:
        logger.error(f"Error getting ShenCe tag metadata: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post(
    "/integrations/shence/files/upload",
    response_model=ShenceUploadResponse,
    summary="Upload file to ShenCe",
    description="Upload CSV data to ShenCe for analytics tag processing",
)
async def upload_shence_file(
    upload_data: ShenceFileUploadRequest = Body(...),
    user_id: str = Depends(require_shence_access_permission),
):
    """Upload file to ShenCe for analytics."""
    try:
        from gateway.integrations.dependencies import (
            upload_file_to_shence,
            validate_shence_tag_data,
            get_shence_service,
        )

        validate_shence_tag_data(upload_data.csv_data)
        await get_shence_service()

        result = await upload_file_to_shence(
            upload_data.file_name_prefix, upload_data.csv_data
        )

        if result.get("error"):
            raise HTTPException(
                status_code=500, detail=f"ShenCe upload failed: {result['error']}"
            )

        response_data = {
            "success": True,
            "file_name": result.get("file_name", ""),
            "file_size": len(str(upload_data.csv_data)),
            "upload_id": result.get("upload_id"),
            "message": "File uploaded successfully",
        }

        return success(data=response_data)
    except Exception as e:
        logger.error(f"Error uploading file to ShenCe: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post(
    "/integrations/shence/tags",
    response_model=ShenceTagResponse,
    summary="Create or update ShenCe tags",
    description="Create new ShenCe analytics tags or update existing ones",
)
async def create_shence_tags(
    tag_data: ShenceTagCreateRequest = Body(...),
    user_id: str = Depends(require_shence_access_permission),
):
    """Create or update ShenCe analytics tags."""
    try:
        from gateway.integrations.dependencies import (
            upload_file_to_shence,
            update_shence_tags,
            validate_shence_tag_data,
            get_shence_service,
        )
        from datetime import datetime

        validate_shence_tag_data(tag_data.data)
        await get_shence_service()

        # Step 1: Upload file to ShenCe
        time_info = datetime.now().strftime("%Y-%m-%d_%H:%M:%S")
        file_name_prefix = f"{tag_data.dir_id}_{time_info}"

        upload_result = await upload_file_to_shence(file_name_prefix, tag_data.data)

        if upload_result.get("error"):
            raise HTTPException(
                status_code=500,
                detail=f"ShenCe file upload failed: {upload_result['error']}",
            )

        # Step 2: Create/update tag
        update_result = await update_shence_tags(
            file_name=upload_result.get("file_name", ""),
            tag_name=tag_data.tag_name,
            tag_id=tag_data.tag_id,
            dir_id=tag_data.dir_id,
            data_type=tag_data.data_type or "STRING",
        )

        if update_result.get("error"):
            raise HTTPException(
                status_code=500,
                detail=f"ShenCe tag update failed: {update_result['error']}",
            )

        response_data = {
            "success": True,
            "tag_id": update_result.get("tag_id"),
            "file_name": upload_result.get("file_name"),
            "upload_response": upload_result,
            "update_response": update_result,
            "message": "Tag created/updated successfully",
        }

        return success(data=response_data)
    except Exception as e:
        logger.error(f"Error creating ShenCe tags: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post(
    "/integrations/shence/tags/update",
    response_model=ShenceTagResponse,
    summary="Update ShenCe tags",
    description="Update existing ShenCe analytics tags with new data",
)
async def update_shence_tag(
    update_data: ShenceTagUpdateRequest = Body(...),
    user_id: str = Depends(require_shence_access_permission),
):
    """Update existing ShenCe analytics tags."""
    try:
        from gateway.integrations.dependencies import (
            update_shence_tags,
            get_shence_service,
        )

        await get_shence_service()
        result = await update_shence_tags(
            file_name=update_data.file_name,
            tag_name=update_data.tag_name,
            tag_id=update_data.tag_id,
            dir_id=update_data.dir_id,
            data_type=update_data.data_type or "STRING",
            task_name=update_data.task_name,
        )

        if result.get("error"):
            raise HTTPException(
                status_code=500, detail=f"ShenCe tag update failed: {result['error']}"
            )

        response_data = {
            "success": True,
            "tag_id": result.get("tag_id"),
            "update_response": result,
            "message": "Tag updated successfully",
        }

        return success(data=response_data)
    except Exception as e:
        logger.error(f"Error updating ShenCe tag: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))
