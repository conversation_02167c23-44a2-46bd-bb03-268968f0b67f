"""
HeyWhale Integration - External API calls with graceful error handling.

Handles HeyWhale platform integration for badge synchronization and qualification notifications.
"""

import os
from typing import Dict
import logging
from datetime import datetime

from pydantic import ValidationError

from gateway.integrations.base import BaseIntegration
from libs.auth.context import AuthContext
from libs.exceptions.handlers import ExternalServiceError
from libs.errors import IntegrationErrors
from gateway.integrations.schemas import (
    TagType,
    GetTagListResponse,
    PostTagRequest,
    PostTagResponse,
    BadgeSyncResponse,
    BadgeSyncBatchRequest,
)
from libs.schemas.api.base import ExternalServiceResponse

logger = logging.getLogger(__name__)


class HeyWhaleIntegrationService(BaseIntegration):
    """Integration service for HeyWhale platform badge and qualification management.
    
    后续考虑将 heywhale 和 modelwhale 的 API 或可用接口进行整合， MW-OFFICIAL-API 已经整理了部分公开的 API 了， 可以参考
    """

    def __init__(self):
        """Initialize HeyWhale integration service."""
        super().__init__(base_url="https://www.heywhale.com", timeout=30)

        # Load authentication from environment
        self._sid_v2 = os.getenv("HEYWHALE_SID_V2", "")
        self._sid_v2_sig = os.getenv("HEYWHALE_SID_V2_SIG", "")
        self._client_sig = os.getenv("KESCI_CLIENT_SIG", "")

        logger.debug("Initialized HeyWhale integration service")

    def _get_auth_cookies(self) -> Dict[str, str]:
        """Get authentication cookies for HeyWhale API."""
        return {
            "heywhale.sid.v2": self._sid_v2,
            "heywhale.sid.v2.sig": self._sid_v2_sig,
            "kesci.client_sig": self._client_sig,
        }
    # 用户标签相关
    async def get_tags(self,tag_type:TagType) -> GetTagListResponse:
        """Get tag from API"""
        try:
            url = "/e/api/organization/tags"
            cookies = self._get_auth_cookies()
            session = await self._get_session()
            async with session.get(f"{self.base_url}{url}",
                                   cookies=cookies,
                                    headers={"Content-Type": "application/json",
                                             "Authorization": f"Bearer {os.getenv('HEYWHALE_TOKEN_API','')}"},
                                   timeout=self.timeout) as response:
                response_data = await response.json()
                return GetTagListResponse(
                    tags=response_data,
                    message="获取标签成功",
                    success=True
                )
        except ValidationError as e:
            logger.error(f"Failed to validate tag tree from HeyWhale: {e}")
            raise ExternalServiceError(
                service_name="heywhale",
                operation="get_tags",
                message=IntegrationErrors.external_service_failed("Heywhale", f"Failed to validate tag tree from HeyWhale: {e}"),
                status_code=400
            )
        except Exception as e:
            logger.error(f"Failed to get tag tree from HeyWhale: {e}")
            raise ExternalServiceError(
                service_name="heywhale",
                operation="get_tags",
                message=IntegrationErrors.external_service_failed("Heywhale", f"Failed to get tag tree from HeyWhale: {e}"),
                status_code=500
            )
       
    async def update_tag(self, tag_request_body:PostTagRequest) -> PostTagResponse:
        """Update tag from API"""
        try:
            url = "/e/api/organization/tags"
            cookies = self._get_auth_cookies()
            session = await self._get_session()
            async with session.put(f"{self.base_url}{url}",
                                    cookies=cookies,
                                    headers={"Content-Type": "application/json",
                                             "Authorization": f"Bearer {os.getenv('HEYWHALE_TOKEN_API','')}"},
                                    json=tag_request_body.model_dump(),
                                    timeout=self.timeout) as response:
                response_data = await response.json()
                _  = PostTagResponse.model_validate(response_data)
                return PostTagResponse(
                    invalid_data=response_data,
                    message="更新标签成功",
                    success=True
                )
        except ValidationError as e:
            logger.error(f"Failed to validate tag tree from HeyWhale: {e}")
            raise ExternalServiceError(
                service_name="heywhale",
                operation="update_tag",
                message=IntegrationErrors.external_service_failed("Heywhale", f"Failed to validate tag tree from HeyWhale: {e}"),
                status_code=400
            )
        except Exception as e:
            logger.error(f"Failed to get tag tree from HeyWhale: {e}")
            raise ExternalServiceError(
                service_name="heywhale",
                operation="update_tag",
                message=IntegrationErrors.external_service_failed("Heywhale", f"Failed to get tag tree from HeyWhale: {e}"),
                status_code=500
            )
    
    # 徽章发放
    async def sync_badges(
        self,
        auth_context: AuthContext,
        badge_payload: BadgeSyncBatchRequest = None,
    ) -> BadgeSyncResponse:
        """
        Synchronize user badges with HeyWhale platform.

        Args:
            users_payload: List of user badge data
            auth_context: Authentication context
            badge_type: Optional badge type filter
            force_update: Force update existing badges

        Returns:
            IntegrationResult containing sync results
        """
        if not badge_payload or not len(badge_payload.data):
            raise ExternalServiceError(
                service_name="heywhale",
                operation="sync_badges",
                message=IntegrationErrors.external_service_failed("Heywhale Tag", "徽章发放请求不能为空"),
                status_code=400
            )       

        # Validate required authentication
        if not all([self._sid_v2, self._sid_v2_sig, self._client_sig]):
            raise ExternalServiceError(
                service_name="heywhale",
                operation="sync_badges",
                message=IntegrationErrors.authentication_failed("Heywhale"),
                status_code=401
            )

        # Prepare request
        url = "/admin/api/user/badges/grant"
        cookies = self._get_auth_cookies()

        # Make request with cookies
        session = await self._get_session()

        try:
            async with session.post(
                f"{self.base_url}{url}",
                json=badge_payload.model_dump(),
                cookies=cookies,
                timeout=self.timeout,
            ) as response:
                # Read response data
                try:
                    response_data = await response.json()
                except Exception:
                    response_data = {"raw_response": await response.text()}

                if response.status == 200:
                    logger.info(
                        f"Successfully synced {len(badge_payload.data)} badges to HeyWhale",
                        synced_by=auth_context.user_id,
                        badge_count=len(badge_payload.data),
                    )

                    return BadgeSyncResponse(
                        data=response_data,
                        message=f"Successfully synced {len(badge_payload.data)} badges to HeyWhale",
                        success=True
                    )
                else:
                    error_message = f"HeyWhale API error: {response.status}"
                    if response_data:
                        error_message += f" - {response_data}"

                    logger.error(
                        f"Failed to sync badges to HeyWhale: {error_message}",
                        status_code=response.status,
                        response_data=response_data,
                    )

                    return BadgeSyncResponse(
                        data=response_data,
                        message=error_message,
                        success=False
                    )

        except Exception as e:
            logger.error(f"Request error during HeyWhale badge sync: {e}")
            raise ExternalServiceError(
                service_name="heywhale",
                operation="sync_badges",
                message=IntegrationErrors.external_service_failed("Heywhale", f"Request failed: {str(e)}"),
                status_code=500
            )


    async def health_check(self) -> ExternalServiceResponse:
        """
        Check the health of HeyWhale integration service.

        Returns:
            IntegrationResult with health status information
        """
        try:
            # Check authentication configuration
            auth_configured = all([self._sid_v2, self._sid_v2_sig, self._client_sig])

            health_data = {
                "service": "heywhale",
                "status": "healthy" if auth_configured else "degraded",
                "base_url": self.base_url,
                "authentication_configured": auth_configured,
                "timeout": self.timeout.total,
                "timestamp": datetime.now().isoformat(),
            }

            if not auth_configured:
                health_data["warnings"] = [
                    "Authentication credentials not fully configured"
                ]

            return ExternalServiceResponse(
                data=health_data,
                message="健康检查成功",
                success=True
            )

        except Exception as e:
            logger.error(f"Health check failed for HeyWhale integration: {e}")
            return ExternalServiceResponse(
                data=None,
                message=IntegrationErrors.external_service_failed("Heywhale", f"Health check failed: {str(e)}"),
                success=False
            )

