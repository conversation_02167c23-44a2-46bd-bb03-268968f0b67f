/**
 * Test file for qualification management features
 * This file tests the integration of competition qualification features
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'
import { ElButton, ElCard, ElTag } from 'element-plus'

// Import components to test
import CompetitionList from '../src/views/competitions/CompetitionList.vue'
import CompetitionDetail from '../src/views/competitions/CompetitionDetail.vue'
import QualificationList from '../src/components/competitions/QualificationList.vue'
import QualificationForm from '../src/views/competitions/QualificationForm.vue'

// Import services
import { competitionService } from '../src/services/competitionService.js'

// Mock router
const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', component: { template: '<div>Home</div>' } },
    { path: '/competitions/:id', component: CompetitionDetail },
    { path: '/competitions/:competitionId/qualifications/create', component: QualificationForm }
  ]
})

// Mock pinia store
const pinia = createPinia()

describe('Qualification Management Features', () => {
  beforeEach(() => {
    // Reset any mocks or state before each test
  })

  describe('Competition Service', () => {
    it('should have qualification management methods', () => {
      expect(typeof competitionService.getQualifications).toBe('function')
      expect(typeof competitionService.createQualification).toBe('function')
      expect(typeof competitionService.updateQualification).toBe('function')
      expect(typeof competitionService.deleteQualification).toBe('function')
    })

    it('should get qualifications for a competition', async () => {
      const competitionId = 'comp_1'
      const result = await competitionService.getQualifications(competitionId)
      
      expect(result).toBeDefined()
      expect(result.success).toBe(true)
      expect(Array.isArray(result.data)).toBe(true)
    })

    it('should create a new qualification', async () => {
      const qualificationData = {
        competition_id: 'comp_1',
        qualification_name: 'Test Qualification',
        credit: 100,
        qualification_type: 1,
        qualification_logic: 1,
        score_threshold: 80
      }
      
      const result = await competitionService.createQualification(qualificationData)
      
      expect(result).toBeDefined()
      expect(result.success).toBe(true)
      expect(result.data).toBeDefined()
    })

    it('should get routes with Chinese names', async () => {
      const result = await competitionService.getRoutes()
      
      expect(result).toBeDefined()
      expect(result.success).toBe(true)
      expect(Array.isArray(result.data)).toBe(true)
      
      if (result.data.length > 0) {
        const route = result.data[0]
        expect(route.name).toBeDefined()
        expect(typeof route.name).toBe('string')
        // Check if it contains Chinese characters
        expect(/[\u4e00-\u9fff]/.test(route.name)).toBe(true)
      }
    })
  })

  describe('QualificationList Component', () => {
    it('should render qualification list', () => {
      const wrapper = mount(QualificationList, {
        props: {
          competitionId: 'comp_1',
          qualifications: [
            {
              id: 'qual_1',
              qualification_name: 'Test Qualification',
              credit: 100,
              qualification_type: 1,
              qualification_logic: 1
            }
          ]
        },
        global: {
          plugins: [router, pinia],
          components: {
            ElButton,
            ElCard,
            ElTag
          }
        }
      })

      expect(wrapper.exists()).toBe(true)
      expect(wrapper.find('.qualification-list').exists()).toBe(true)
    })

    it('should show empty state when no qualifications', () => {
      const wrapper = mount(QualificationList, {
        props: {
          competitionId: 'comp_1',
          qualifications: []
        },
        global: {
          plugins: [router, pinia],
          components: {
            ElButton,
            ElCard,
            ElTag
          }
        }
      })

      expect(wrapper.find('.empty-state').exists()).toBe(true)
    })
  })

  describe('QualificationForm Component', () => {
    it('should render qualification form', async () => {
      // Mock route params
      router.push('/competitions/comp_1/qualifications/create')
      await router.isReady()

      const wrapper = mount(QualificationForm, {
        global: {
          plugins: [router, pinia],
          components: {
            ElButton,
            ElCard
          }
        }
      })

      expect(wrapper.exists()).toBe(true)
      expect(wrapper.find('.qualification-form').exists()).toBe(true)
      expect(wrapper.find('h1').text()).toContain('Create Qualification')
    })
  })

  describe('Route Filter Integration', () => {
    it('should display Chinese route names in competition list', async () => {
      const wrapper = mount(CompetitionList, {
        global: {
          plugins: [router, pinia],
          components: {
            ElButton,
            ElCard,
            ElTag
          }
        }
      })

      // Wait for component to load data
      await wrapper.vm.$nextTick()
      
      expect(wrapper.exists()).toBe(true)
      expect(wrapper.find('.competition-list').exists()).toBe(true)
    })
  })
})

describe('Navigation Integration', () => {
  it('should navigate to competition detail', async () => {
    router.push('/competitions/comp_1')
    await router.isReady()
    
    expect(router.currentRoute.value.path).toBe('/competitions/comp_1')
    expect(router.currentRoute.value.params.id).toBe('comp_1')
  })

  it('should navigate to qualification create form', async () => {
    router.push('/competitions/comp_1/qualifications/create')
    await router.isReady()
    
    expect(router.currentRoute.value.path).toBe('/competitions/comp_1/qualifications/create')
    expect(router.currentRoute.value.params.competitionId).toBe('comp_1')
  })
})

describe('Mock Data Integration', () => {
  it('should have Chinese route names in mock data', async () => {
    const routes = await competitionService.getRoutes()
    
    expect(routes.success).toBe(true)
    expect(routes.data.length).toBeGreaterThan(0)
    
    const chineseRoutes = routes.data.filter(route => 
      /[\u4e00-\u9fff]/.test(route.name)
    )
    
    expect(chineseRoutes.length).toBeGreaterThan(0)
    
    // Check specific Chinese route names
    const routeNames = routes.data.map(route => route.name)
    expect(routeNames).toContain('数据科学赛道')
    expect(routeNames).toContain('人工智能赛道')
  })

  it('should have qualifications with Chinese names', async () => {
    const qualifications = await competitionService.getQualifications('comp_1')
    
    expect(qualifications.success).toBe(true)
    expect(qualifications.data.length).toBeGreaterThan(0)
    
    const chineseQualifications = qualifications.data.filter(qual => 
      /[\u4e00-\u9fff]/.test(qual.qualification_name)
    )
    
    expect(chineseQualifications.length).toBeGreaterThan(0)
  })
})
