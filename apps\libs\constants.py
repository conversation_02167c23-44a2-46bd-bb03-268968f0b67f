from enum import Enum, IntEnum


class AdminActionType(IntEnum):
    ADD_CREDIT = 1
    ADD_COMPETITION = 2
    INJECT_SUBMISSIONS = 3
    INJECT_REGISTERS = 4
    DOWNLOAD_CREDIT_LOG = 5


class StatisticsName(Enum):
    TOTAL_REGISTERS = "全体报名人数"
    TOTAL_SUBMISSIONS_ON_QUALIFICATION_TASK = "通关任务提交数"
    TOTAL_CREDITS_EARNED = "累计总学分数"


REGIONS = [
    "广东",
    "北京",
    "江苏",
    "上海",
    "浙江",
    "四川",
    "山东",
    "河南",
    "湖北",
    "湖南",
    "重庆",
    "陕西",
    "河北",
    "安徽",
    "福建",
    "辽宁",
    "广西",
    "天津",
    "江西",
    "云南",
    "山西",
    "黑龙江",
    "贵州",
    "香港",
    "甘肃",
    "吉林",
    "新疆",
    "内蒙古",
    "海南",
    "宁夏",
    "台湾",
    "青海",
    "澳门",
]
