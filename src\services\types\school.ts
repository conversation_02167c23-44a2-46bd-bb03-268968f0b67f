/**
 * School-related TypeScript interfaces
 */

import { BaseEntity, User, StatisticItem, TimeSeriesData } from './common'

// School types
export interface School extends BaseEntity {
  name: string
  shortName?: string
  type: SchoolType
  level: SchoolLevel
  status: SchoolStatus
  
  // Location
  country: string
  province: string
  city: string
  address?: string
  coordinates?: {
    latitude: number
    longitude: number
  }
  
  // Contact
  website?: string
  email?: string
  phone?: string
  
  // Academic info
  establishedYear?: number
  studentCount?: number
  facultyCount?: number
  
  // Platform info
  registeredAt: string
  lastActiveAt?: string
  
  // Statistics
  stats: SchoolStats
  
  // Settings
  isVerified: boolean
  isPartner: boolean
  
  // Metadata
  logo?: string
  description?: string
  tags: string[]
}

export type SchoolType = 'university' | 'college' | 'institute' | 'academy' | 'school' | 'other'
export type SchoolLevel = 'primary' | 'secondary' | 'undergraduate' | 'graduate' | 'vocational' | 'mixed'
export type SchoolStatus = 'active' | 'inactive' | 'pending' | 'suspended'

export interface SchoolStats {
  totalStudents: number
  activeStudents: number
  totalCompetitions: number
  totalSubmissions: number
  totalCreditsEarned: number
  averageScore: number
  
  // Participation metrics
  participationRate: number
  completionRate: number
  winRate: number
  
  // Rankings
  overallRank?: number
  categoryRanks?: Record<string, number>
  
  // Trends
  monthlyActiveUsers: TimeSeriesData[]
  monthlyParticipation: TimeSeriesData[]
  monthlyCredits: TimeSeriesData[]
}

// Student types
export interface Student extends BaseEntity {
  user: User
  school: School
  studentId?: string
  major?: string
  year?: number
  level: StudentLevel
  status: StudentStatus
  
  // Academic info
  gpa?: number
  graduationYear?: number
  
  // Platform activity
  joinedAt: string
  lastActiveAt?: string
  
  // Statistics
  stats: StudentStats
  
  // Verification
  isVerified: boolean
  verificationMethod?: VerificationMethod
  verifiedAt?: string
}

export type StudentLevel = 'freshman' | 'sophomore' | 'junior' | 'senior' | 'graduate' | 'phd' | 'other'
export type StudentStatus = 'active' | 'inactive' | 'graduated' | 'transferred' | 'suspended'
export type VerificationMethod = 'email' | 'document' | 'manual' | 'sso'

export interface StudentStats {
  totalCompetitions: number
  totalSubmissions: number
  totalCredits: number
  averageScore: number
  bestRank: number
  
  // Participation
  participationStreak: number
  lastParticipation?: string
  
  // Achievements
  wins: number
  topThreeFinishes: number
  achievements: string[]
  
  // Activity
  monthlyActivity: TimeSeriesData[]
}

// Department/Faculty types
export interface Department extends BaseEntity {
  name: string
  shortName?: string
  school: School
  type: DepartmentType
  
  // Academic info
  headOfDepartment?: string
  facultyCount?: number
  studentCount?: number
  
  // Statistics
  stats: DepartmentStats
  
  // Metadata
  description?: string
  website?: string
}

export type DepartmentType = 'engineering' | 'science' | 'business' | 'arts' | 'medicine' | 'law' | 'education' | 'other'

export interface DepartmentStats {
  totalStudents: number
  activeStudents: number
  totalParticipations: number
  averageScore: number
  totalCredits: number
}

// Analytics and reporting types
export interface SchoolAnalytics {
  school: School
  period: AnalyticsPeriod
  
  // Overview metrics
  overview: {
    totalStudents: StatisticItem
    activeStudents: StatisticItem
    participationRate: StatisticItem
    averageScore: StatisticItem
  }
  
  // Participation trends
  participation: {
    byMonth: TimeSeriesData[]
    byCompetitionType: Array<{ type: string; count: number }>
    byDepartment: Array<{ department: string; count: number }>
  }
  
  // Performance metrics
  performance: {
    scoreDistribution: Array<{ range: string; count: number }>
    topPerformers: Student[]
    improvementTrend: TimeSeriesData[]
  }
  
  // Engagement metrics
  engagement: {
    activeUsersTrend: TimeSeriesData[]
    retentionRate: number
    averageSessionDuration: number
  }
  
  // Comparisons
  benchmarks: {
    vsNationalAverage: number
    vsRegionalAverage: number
    vsSimilarSchools: number
    ranking: {
      overall: number
      byCategory: Record<string, number>
    }
  }
}

export type AnalyticsPeriod = 'week' | 'month' | 'quarter' | 'year' | 'custom'

export interface SchoolComparison {
  schools: School[]
  metrics: Array<{
    name: string
    values: Record<string, number>
  }>
  period: AnalyticsPeriod
}

export interface SchoolRanking {
  school: School
  rank: number
  score: number
  metrics: Record<string, number>
  change?: number
}

// Form types
export interface SchoolFormData {
  name: string
  shortName?: string
  type: SchoolType
  level: SchoolLevel
  country: string
  province: string
  city: string
  address?: string
  website?: string
  email?: string
  phone?: string
  establishedYear?: number
  studentCount?: number
  facultyCount?: number
  description?: string
  tags: string[]
}

export interface StudentFormData {
  userId: string
  schoolId: string
  studentId?: string
  major?: string
  year?: number
  level: StudentLevel
  gpa?: number
  graduationYear?: number
}

// API request/response types
export interface SchoolListParams {
  page?: number
  limit?: number
  type?: SchoolType
  level?: SchoolLevel
  status?: SchoolStatus
  country?: string
  province?: string
  city?: string
  search?: string
  isVerified?: boolean
  isPartner?: boolean
  sortBy?: 'name' | 'studentCount' | 'participationRate' | 'averageScore'
  sortOrder?: 'asc' | 'desc'
}

export interface StudentListParams {
  page?: number
  limit?: number
  schoolId?: string
  level?: StudentLevel
  status?: StudentStatus
  major?: string
  year?: number
  search?: string
  isVerified?: boolean
  sortBy?: 'name' | 'gpa' | 'totalCredits' | 'averageScore'
  sortOrder?: 'asc' | 'desc'
}

export interface SchoolAnalyticsParams {
  schoolId: string
  period: AnalyticsPeriod
  startDate?: string
  endDate?: string
  compareWith?: string[]
  metrics?: string[]
}

export interface SchoolStatsResponse {
  totalSchools: number
  totalStudents: number
  averageParticipationRate: number
  topPerformingSchools: SchoolRanking[]
  geographicDistribution: Record<string, number>
  typeDistribution: Record<SchoolType, number>
  levelDistribution: Record<SchoolLevel, number>
}
