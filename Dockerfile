FROM python:3.10-alpine

RUN mkdir /community_services
WORKDIR /community_services
ADD requirements.txt .

# RUN pip install pip --upgrade
RUN pip install -i https://pypi.tuna.tsinghua.edu.cn/simple PyMySQL
RUN pip install -i https://pypi.mirrors.ustc.edu.cn/simple -r requirements.txt

ADD . .
EXPOSE 8000

# CMD ["sh", "-c", "flask --app src run --host=0.0.0.0"]
CMD ["gunicorn", "--workers", "2", "--timeout","120","--bind", "0.0.0.0:8000", "src:create_app()"]
