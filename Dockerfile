# Use an official Python runtime as a parent image
FROM python:3.9-slim

# Set the working directory in the container
WORKDIR /app

# Copy the current directory contents into the container at /app
COPY . /app

# Install any needed packages specified in requirements.txt
RUN pip install  -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt

# Expose port 8501 for Streamlit
EXPOSE 8501

# Pick certain apps to run
ENV APP_TO_RUN=apps/main.py

# Run streamlit when the container launches
CMD ["sh", "-c", "streamlit run ${APP_TO_RUN} --server.port=8501 --server.address=0.0.0.0"]
