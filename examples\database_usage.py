"""
Example usage of the improved tunnel manager and MongoDB connection modules.

This script demonstrates:
- Proper initialization and connection management
- Error handling and logging
- Context managers for resource management
- Health checks and monitoring
"""

import asyncio

from core.config import logger, settings
from core.config.tunnel_manager import tunnel_manager
from core.database.mongo_db import mongo_manager


async def demonstrate_tunnel_usage():
    """Demonstrate SSH tunnel management."""
    logger.info("=== SSH Tunnel Management Demo ===")

    try:
        # Check if tunnel is enabled
        if not settings.ssh_tunnel_enabled:
            logger.info("SSH tunnel is disabled in configuration")
            return

        # Start tunnel
        logger.info("Starting SSH tunnel...")
        await tunnel_manager.start()

        # Check tunnel status
        if tunnel_manager.is_active:
            logger.success(
                f"Tunnel is active on local port {tunnel_manager.local_bind_port}"
            )

        # Perform health check
        is_healthy = await tunnel_manager.health_check()
        logger.info(f"Tunnel health check: {'PASS' if is_healthy else 'FAIL'}")

        # Use context manager
        async with tunnel_manager.tunnel_context() as tunnel:
            if tunnel:
                logger.info(f"Using tunnel in context: {tunnel.local_bind_port}")
            else:
                logger.warning("No tunnel available in context")

    except Exception as e:
        logger.error(f"Tunnel demonstration failed: {e}")


async def demonstrate_mongodb_usage():
    """Demonstrate MongoDB connection management."""
    logger.info("=== MongoDB Connection Management Demo ===")

    try:
        # Connect to MongoDB
        logger.info("Connecting to MongoDB...")
        await mongo_manager.connect()

        # Check connection status
        if mongo_manager.is_connected:
            logger.success("MongoDB connection established")

        # Perform health check
        is_healthy = await mongo_manager.health_check()
        logger.info(f"MongoDB health check: {'PASS' if is_healthy else 'FAIL'}")

        # Use connection context manager
        async with mongo_manager.connection_context() as client:
            logger.info("Using MongoDB client in context")

            # List databases
            db_list = await client.list_database_names()
            logger.info(f"Available databases: {db_list}")

        # Use database context manager
        async with mongo_manager.database_context() as db:
            logger.info(f"Using database: {db.name}")

            # List collections
            collections = await db.list_collection_names()
            logger.info(f"Available collections: {collections[:5]}...")  # Show first 5

    except Exception as e:
        logger.error(f"MongoDB demonstration failed: {e}")


async def demonstrate_error_handling():
    """Demonstrate error handling scenarios."""
    logger.info("=== Error Handling Demo ===")

    # Test invalid tunnel configuration
    try:
        # Temporarily modify settings to test error handling
        original_host = settings.ssh_tunnel_host
        settings.ssh_tunnel_host = None

        await tunnel_manager.start()

    except ValueError as e:
        logger.info(f"Expected configuration error caught: {e}")
    finally:
        # Restore original settings
        settings.ssh_tunnel_host = original_host

    # Test MongoDB connection without tunnel (if tunnel is required)
    try:
        # This will fail if tunnel is required but not available
        if settings.ssh_tunnel_enabled:
            await tunnel_manager.stop()  # Stop tunnel to test error
            await mongo_manager.connect()
    except ConnectionError as e:
        logger.info(f"Expected connection error caught: {e}")


async def demonstrate_lifecycle_management():
    """Demonstrate proper lifecycle management."""
    logger.info("=== Lifecycle Management Demo ===")

    try:
        # Start services
        logger.info("Starting services...")

        if settings.ssh_tunnel_enabled:
            await tunnel_manager.start()
            logger.info("✓ SSH tunnel started")

        await mongo_manager.connect()
        logger.info("✓ MongoDB connected")

        # Perform some operations
        logger.info("Performing operations...")

        # Health checks
        tunnel_healthy = (
            await tunnel_manager.health_check() if settings.ssh_tunnel_enabled else True
        )
        mongo_healthy = await mongo_manager.health_check()

        logger.info(
            f"System health: Tunnel={'OK' if tunnel_healthy else 'FAIL'}, MongoDB={'OK' if mongo_healthy else 'FAIL'}"
        )

        # Graceful shutdown
        logger.info("Shutting down services...")

        await mongo_manager.disconnect()
        logger.info("✓ MongoDB disconnected")

        if settings.ssh_tunnel_enabled:
            await tunnel_manager.stop()
            logger.info("✓ SSH tunnel stopped")

        logger.success("All services shut down gracefully")

    except Exception as e:
        logger.error(f"Lifecycle management failed: {e}")

        # Ensure cleanup even on error
        try:
            await mongo_manager.disconnect()
            if settings.ssh_tunnel_enabled:
                await tunnel_manager.stop()
        except Exception as cleanup_error:
            logger.error(f"Cleanup failed: {cleanup_error}")


async def main():
    """Main demonstration function."""
    logger.info("Starting database infrastructure demonstration")

    # Show current configuration
    logger.info(
        "Configuration summary",
        ssh_tunnel_enabled=settings.ssh_tunnel_enabled,
        ssh_host=settings.ssh_tunnel_host,
        mongo_host=settings.mongo_host,
        mongo_port=settings.mongo_port,
        mongo_database=settings.mongo_database,
    )

    # Run demonstrations
    await demonstrate_tunnel_usage()
    await demonstrate_mongodb_usage()
    await demonstrate_error_handling()
    await demonstrate_lifecycle_management()

    logger.success("Database infrastructure demonstration completed")


if __name__ == "__main__":
    # Run the demonstration
    asyncio.run(main())
