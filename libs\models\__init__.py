"""
Database models for the data pipeline.

This module exports all Tortoise ORM models used throughout the application.
All models are centralized here for better organization and consistency.
"""

from .base import (
    # Base mixin
    TimestampMixin,
    VisibilityMixin,
    UserBasedMixin,
    SchoolBasedMixin,
    QualificationMixin,
    StatisticsMixin,
)
from .tables import (
    # Core models
    UserRegistration,
    Teams,
    Competition,
    Qualification,
    Submission,
    CreditHistory,
    Route,
    Administrators,
    Statistics,
    QualifiedUser,

)
from .views import (
    # Views
    RouteStatistics,
    CompetitionStatistics,
    UserStatistics,
    SchoolStatistics,
)

__all__ = [
    # Base mixin
    "TimestampMixin",
    "VisibilityMixin",
    "UserBasedMixin",
    "SchoolBasedMixin",
    "QualificationMixin",
    "StatisticsMixin",
    # Core models
    "UserRegistration",
    "Teams",
    "Competition",
    "Qualification",
    "Submission",
    "CreditHistory",
    "Route",
    "Statistics",
    # Views
    "RouteStatistics",
    "CompetitionStatistics",
    "UserStatistics",
    "SchoolStatistics",
    "QualifiedUser",
    # Admin models
    "Administrators",
    # Enums
    "StatisticsCategory",
] 