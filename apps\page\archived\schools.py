import streamlit as st
import pandas as pd
from libs.api import list_schools, get_school_data, get_school_details

st.session_state.school_data = st.session_state.get("school_data", "")
st.session_state.enable_download = st.session_state.get("enable_download", False)


@st.cache_data
def convert_df(df):
    # IMPORTANT: Cache the conversion to prevent computation on every rerun
    return df.to_csv(index=False).encode("utf-8-sig")


@st.experimental_dialog("开始下载")
def download_data(school):
    st.write("即将下载【{}】的数据集详情，点击确认".format(school.strip()))
    st.download_button(
        label="下载",
        data=st.session_state.school_data,
        file_name="school_statistcs.csv",
        mime="text/csv",
        use_container_width=True,
    )


existing_schools = st.session_state.get("existing_schools", [])
if not existing_schools:
    response = list_schools()
    if response.status_code == 200:
        existing_schools = response.json()["data"]
    st.session_state.existing_schools = existing_schools

options = existing_schools


st.header("学校统计", divider="rainbow")

operations = st.container()
schools, download = operations.columns(2)


school = schools.selectbox(
    "选择学校",
    options=options,
    index=options.index(st.session_state.get("default_school", ""))
    if st.session_state.get("default_school", "") in options
    else 0,
    placeholder="请选择学校",
    label_visibility="collapsed",
)

st.session_state.default_school = school

resp = get_school_data({"school": school.strip()})
school_statistics = {}
if resp.status_code == 200:
    school_statistics = resp.json()["data"]

gen, down = download.columns(2)
if gen.button(label="下载详情数据", use_container_width=True):
    response = get_school_details({"school": school.strip(), "type": "qualification"})
    print(response.json())
    if response.status_code == 200:
        st.session_state.school_data = convert_df(pd.DataFrame(response.json()["data"]))
    download_data(school)

if down.button(label="下载报名数据", use_container_width=True):
    response = get_school_details(
        {"school": school.strip(), "is_detail": True, "type": "register"}
    )
    if response.status_code == 200:
        st.session_state.school_data = convert_df(pd.DataFrame(response.json()["data"]))
    download_data(school)


total_registers, total_students, total_credits, total_submissions = st.columns(4)
total_registers.metric(
    label="总报名人次",
    value=school_statistics.get("total_registers", 0) if school else 0,
)
total_students.metric(
    label="总学生人数",
    value=school_statistics.get("total_students", 0) if school else 0,
)
total_credits.metric(
    label="获得总学分", value=school_statistics.get("total_credits", 0) if school else 0
)
total_submissions.metric(
    label="总提交数",
    value=school_statistics.get("total_submissions", 0) if school else 0,
)
