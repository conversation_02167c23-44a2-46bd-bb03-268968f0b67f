from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "access_log" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "user_id" VARCHAR(255) NOT NULL,
    "target_url" VARCHAR(255),
    "user_agent" VARCHAR(255),
    "request_params" JSONB,
    "ip" VARCHAR(32),
    "note" VARCHAR(255)
);
COMMENT ON COLUMN "access_log"."created_at" IS '创建时间';
COMMENT ON COLUMN "access_log"."updated_at" IS '更新时间';
COMMENT ON COLUMN "access_log"."user_id" IS '用户ID';
COMMENT ON COLUMN "access_log"."target_url" IS '访问的url';
COMMENT ON COLUMN "access_log"."user_agent" IS '访问UA';
COMMENT ON COLUMN "access_log"."request_params" IS '请求参数get|post';
COMMENT ON COLUMN "access_log"."ip" IS '访问IP';
COMMENT ON COLUMN "access_log"."note" IS '备注';
COMMENT ON TABLE "access_log" IS '用户操作记录表';
CREATE TABLE IF NOT EXISTS "administrators" (
    "email_address" VARCHAR(255) NOT NULL  PRIMARY KEY,
    "password" VARCHAR(255) NOT NULL,
    "user_name" VARCHAR(255),
    "last_login_ts" TIMESTAMPTZ
);
COMMENT ON TABLE "administrators" IS 'Site administrators';
CREATE TABLE IF NOT EXISTS "admin_logs" (
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "record_id" UUID NOT NULL  PRIMARY KEY,
    "action_type" INT NOT NULL,
    "action_related_id" VARCHAR(255),
    "remark" VARCHAR(255),
    "admin_email_id" VARCHAR(255) NOT NULL REFERENCES "administrators" ("email_address") ON DELETE CASCADE
);
COMMENT ON COLUMN "admin_logs"."created_at" IS '创建时间';
COMMENT ON COLUMN "admin_logs"."updated_at" IS '更新时间';
COMMENT ON TABLE "admin_logs" IS 'Logs of administrator actions';
CREATE TABLE IF NOT EXISTS "majors" (
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "id" SERIAL NOT NULL PRIMARY KEY,
    "name" VARCHAR(255) NOT NULL UNIQUE
);
COMMENT ON COLUMN "majors"."created_at" IS '创建时间';
COMMENT ON COLUMN "majors"."updated_at" IS '更新时间';
COMMENT ON COLUMN "majors"."id" IS '专业ID';
COMMENT ON COLUMN "majors"."name" IS '专业名称';
CREATE TABLE IF NOT EXISTS "qualified_users_triggered" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "user_id" VARCHAR(255) NOT NULL,
    "qualification_id" VARCHAR(255) NOT NULL,
    "qualification_name" VARCHAR(255),
    "credit" INT NOT NULL  DEFAULT 0,
    "task_id" VARCHAR(255) NOT NULL,
    "competition_id" VARCHAR(255) NOT NULL,
    "team_id" VARCHAR(255) NOT NULL,
    "qualified_date" TIMESTAMPTZ NOT NULL,
    "user_name" VARCHAR(255) NOT NULL,
    "university" VARCHAR(255)   DEFAULT '',
    "update_ts" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "uid_qualified_u_user_id_4d8b7f" UNIQUE ("user_id", "qualification_id")
);
COMMENT ON TABLE "qualified_users_triggered" IS 'View of users who met qualification criteria';
CREATE TABLE IF NOT EXISTS "rankings" (
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "record_id" UUID NOT NULL  PRIMARY KEY,
    "user_id" VARCHAR(255) NOT NULL,
    "competition_id" VARCHAR(255) NOT NULL,
    "qualification_id" VARCHAR(255),
    "route_id" VARCHAR(255),
    "credit" INT NOT NULL  DEFAULT 0,
    "rank" INT,
    "timestamp" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "uid_rankings_user_id_90cd8c" UNIQUE ("user_id", "competition_id", "qualification_id")
);
COMMENT ON COLUMN "rankings"."created_at" IS '创建时间';
COMMENT ON COLUMN "rankings"."updated_at" IS '更新时间';
COMMENT ON TABLE "rankings" IS 'User rankings in competitions';
CREATE TABLE IF NOT EXISTS "routes" (
    "route_id" VARCHAR(255) NOT NULL  PRIMARY KEY,
    "route_name" VARCHAR(255),
    "info" VARCHAR(255)
);
COMMENT ON TABLE "routes" IS 'Competition routes or tracks';
CREATE TABLE IF NOT EXISTS "competitions" (
    "record_id" VARCHAR(255) NOT NULL  PRIMARY KEY,
    "competition_id" VARCHAR(255) NOT NULL UNIQUE,
    "route_name" VARCHAR(255) NOT NULL,
    "competition_name" VARCHAR(255),
    "route_id" VARCHAR(255) NOT NULL REFERENCES "routes" ("route_id") ON DELETE RESTRICT
);
COMMENT ON TABLE "competitions" IS 'Competition details';
CREATE TABLE IF NOT EXISTS "qualifications" (
    "qualification_id" VARCHAR(255) NOT NULL  PRIMARY KEY,
    "qualification_name" VARCHAR(255),
    "qualification_type" INT   DEFAULT 1,
    "qualification_logic" INT   DEFAULT 1,
    "related_task_id" VARCHAR(255),
    "score_threshold" DOUBLE PRECISION,
    "deleted" BOOL   DEFAULT False,
    "credit" INT,
    "competition_id" VARCHAR(255) NOT NULL REFERENCES "competitions" ("competition_id") ON DELETE CASCADE
);
COMMENT ON COLUMN "qualifications"."competition_id" IS 'The competition this qualification belongs to';
COMMENT ON TABLE "qualifications" IS 'Qualification criteria for competitions';
CREATE TABLE IF NOT EXISTS "statistics" (
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "record_id" UUID NOT NULL  PRIMARY KEY,
    "statistics_name" VARCHAR(255) NOT NULL,
    "statistics_value" INT   DEFAULT 0,
    "statistics_related_id" VARCHAR(255),
    "remark" VARCHAR(255)
);
COMMENT ON COLUMN "statistics"."created_at" IS '创建时间';
COMMENT ON COLUMN "statistics"."updated_at" IS '更新时间';
COMMENT ON TABLE "statistics" IS 'General statistics records';
CREATE TABLE IF NOT EXISTS "submissions" (
    "record_id" VARCHAR(255) NOT NULL  PRIMARY KEY,
    "submission_ts" TIMESTAMPTZ NOT NULL,
    "score" INT,
    "team" VARCHAR(255) NOT NULL,
    "task_id" VARCHAR(255) NOT NULL,
    "competition_id" VARCHAR(255) NOT NULL REFERENCES "competitions" ("competition_id") ON DELETE CASCADE
);
COMMENT ON TABLE "submissions" IS 'User submissions for tasks/competitions';
CREATE TABLE IF NOT EXISTS "universities" (
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "id" SERIAL NOT NULL PRIMARY KEY,
    "name" VARCHAR(255) NOT NULL UNIQUE
);
COMMENT ON COLUMN "universities"."created_at" IS '创建时间';
COMMENT ON COLUMN "universities"."updated_at" IS '更新时间';
COMMENT ON COLUMN "universities"."id" IS '学校ID';
COMMENT ON COLUMN "universities"."name" IS '学校名称';
CREATE TABLE IF NOT EXISTS "users" (
    "user_id" VARCHAR(255) NOT NULL  PRIMARY KEY,
    "register_id" VARCHAR(255) NOT NULL UNIQUE,
    "user_name" VARCHAR(255),
    "university" VARCHAR(255)   DEFAULT '',
    "competition_id" VARCHAR(255),
    "phone" VARCHAR(255),
    "email" VARCHAR(255)  UNIQUE
);
COMMENT ON TABLE "users" IS 'User accounts';
CREATE TABLE IF NOT EXISTS "credit_history" (
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "record_id" UUID NOT NULL  PRIMARY KEY,
    "credit" INT NOT NULL  DEFAULT 0,
    "competition_id" VARCHAR(255) NOT NULL,
    "qualification_id" VARCHAR(255),
    "remark" VARCHAR(255),
    "batch_id" VARCHAR(255),
    "deleted" BOOL NOT NULL  DEFAULT False,
    "user_id" VARCHAR(255) NOT NULL REFERENCES "users" ("user_id") ON DELETE CASCADE
);
COMMENT ON COLUMN "credit_history"."created_at" IS '创建时间';
COMMENT ON COLUMN "credit_history"."updated_at" IS '更新时间';
COMMENT ON TABLE "credit_history" IS 'Record of user credit changes';
CREATE TABLE IF NOT EXISTS "teams" (
    "membership_id" VARCHAR(255) NOT NULL  PRIMARY KEY,
    "team_id" VARCHAR(255) NOT NULL,
    "update_ts" TIMESTAMPTZ,
    "competition_id" VARCHAR(255) NOT NULL REFERENCES "competitions" ("competition_id") ON DELETE CASCADE,
    "user_id" VARCHAR(255) NOT NULL REFERENCES "users" ("user_id") ON DELETE CASCADE
);
CREATE TABLE IF NOT EXISTS "aerich" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "version" VARCHAR(255) NOT NULL,
    "app" VARCHAR(100) NOT NULL,
    "content" JSONB NOT NULL
);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        """
