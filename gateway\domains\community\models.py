"""
Community domain-specific models and data structures.

Contains models and data classes specific to the community domain
that are not shared across the application.
"""

from typing import List, Optional
from dataclasses import dataclass
from datetime import datetime
from enum import Enum


class EventStatus(str, Enum):
    """Event status enumeration."""

    DRAFT = "draft"
    UPCOMING = "upcoming"
    ACTIVE = "active"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class EventType(str, Enum):
    """Event type enumeration."""

    WORKSHOP = "workshop"
    WEBINAR = "webinar"
    COMPETITION = "competition"
    MEETUP = "meetup"
    ANNOUNCEMENT = "announcement"


class ParticipationStatus(str, Enum):
    """Participation status enumeration."""

    REGISTERED = "registered"
    ATTENDED = "attended"
    NO_SHOW = "no_show"
    CANCELLED = "cancelled"


@dataclass
class EventContext:
    """Context data for event operations."""

    user_id: str
    event_id: Optional[str] = None
    can_moderate: bool = False
    can_create: bool = False


@dataclass
class EventParticipation:
    """Event participation data structure."""

    user_id: str
    event_id: str
    status: ParticipationStatus
    registration_date: datetime
    attendance_date: Optional[datetime] = None
    notes: Optional[str] = None


@dataclass
class AnnouncementContext:
    """Context data for announcement operations."""

    user_id: str
    announcement_id: Optional[str] = None
    target_audience: List[str] = None
    priority: str = "normal"

    def __post_init__(self):
        if self.target_audience is None:
            self.target_audience = []


# TODO: Add other domain-specific models as needed
