<template>
  <div class="home-page">
    <div class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">和鲸社区 - 管理员平台</h1>
        <p class="hero-subtitle" color="red">
            【管理员专用】：仅限管理员访问的系统
        </p>
        <p class="hero-description">
            这是一个<b>【管理员专用】</b>平台，用于管理社区活动、学分分发、数据统计等管理功能。所有通过身份验证的用户都具有完整的管理权限。
        </p>
        
        <div class="hero-actions">
          <el-button 
            type="primary" 
            size="large" 
            @click="$router.push('/dashboard')"
            class="primary-action"
          >
            <el-icon><House /></el-icon>
            前往主页面
          </el-button>
          
          <el-button 
            size="large" 
            @click="$router.push('/competitions')"
            class="secondary-action"
          >
            <el-icon><Trophy /></el-icon>
            查看活动
          </el-button>
        </div>
      </div>
      
      <div class="hero-image">
        <div class="feature-grid">
          <div class="feature-item">
            <el-icon class="feature-icon"><Trophy /></el-icon>
            <span>活动</span>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><Coin /></el-icon>
            <span>学分</span>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><School /></el-icon>
            <span>学校</span>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><Tools /></el-icon>
            <span>管理工具</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Access Section
    <div class="quick-access-section">
      <h2>Quick Access</h2>
      <div class="access-grid">
        <el-card class="access-card" @click="$router.push('/dashboard')">
          <div class="access-content">
            <el-icon class="access-icon"><House /></el-icon>
            <h3>Dashboard</h3>
            <p>Overview and statistics</p>
          </div>
        </el-card>
        
        <el-card class="access-card" @click="$router.push('/competitions')">
          <div class="access-content">
            <el-icon class="access-icon"><Trophy /></el-icon>
            <h3>Competitions</h3>
            <p>Manage competitions and rules</p>
          </div>
        </el-card>
        
        <el-card class="access-card" @click="$router.push('/credits')">
          <div class="access-content">
            <el-icon class="access-icon"><Coin /></el-icon>
            <h3>Credits</h3>
            <p>Distribute and manage credits</p>
          </div>
        </el-card>
        
        <el-card class="access-card" @click="$router.push('/schools')">
          <div class="access-content">
            <el-icon class="access-icon"><School /></el-icon>
            <h3>Schools</h3>
            <p>View school statistics</p>
          </div>
        </el-card>
      </div>
    </div> -->


  </div>
</template>

<script setup>
import {
  House,
  Trophy,
  Coin,
  School,
  Tools
} from '@element-plus/icons-vue'
</script>

<style lang="scss" scoped>
.home-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.hero-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  min-height: 500px;
  margin-bottom: 80px;
  
  .hero-content {
    .hero-title {
      font-size: 48px;
      font-weight: 700;
      color: var(--text-color);
      margin-bottom: 16px;
      line-height: 1.2;
    }
    
    .hero-subtitle {
      font-size: 24px;
      color: var(--text-color-secondary);
      margin-bottom: 20px;
      font-weight: 500;
    }
    
    .hero-description {
      font-size: 16px;
      color: var(--text-color-secondary);
      line-height: 1.6;
      margin-bottom: 40px;
    }
    
    .hero-actions {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;
      
      .primary-action {
        font-size: 16px;
        padding: 12px 24px;
        height: auto;
      }
      
      .secondary-action {
        font-size: 16px;
        padding: 12px 24px;
        height: auto;
        color: var(--text-color);
        border-color: var(--border-color);
        background-color: var(--card-bg);

        &:hover {
          color: var(--sidebar-active);
          border-color: var(--sidebar-active);
          background-color: var(--bg-color-secondary);
        }
      }
    }
  }
  
  .hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
    
    .feature-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 30px;
      
      .feature-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 30px;
        background: var(--card-bg);
        border-radius: 16px;
        box-shadow: var(--shadow-color) 0px 4px 12px;
        transition: transform 0.3s ease;
        
        &:hover {
          transform: translateY(-4px);
        }
        
        .feature-icon {
          font-size: 48px;
          color: var(--sidebar-active);
          margin-bottom: 12px;
        }
        
        span {
          font-size: 16px;
          font-weight: 600;
          color: var(--text-color);
        }
      }
    }
  }
}

.quick-access-section {
  margin-bottom: 80px;
  
  h2 {
    font-size: 32px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 40px;
    text-align: center;
  }
  
  .access-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    
    .access-card {
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid var(--border-color);
      background-color: var(--card-bg);

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px var(--shadow-color);
        border-color: var(--sidebar-active);
      }
      
      .access-content {
        text-align: center;
        padding: 30px 20px;
        
        .access-icon {
          font-size: 48px;
          color: var(--sidebar-active);
          margin-bottom: 16px;
        }
        
        h3 {
          font-size: 20px;
          font-weight: 600;
          color: var(--text-color);
          margin-bottom: 8px;
        }
        
        p {
          font-size: 14px;
          color: var(--text-color-secondary);
          line-height: 1.5;
        }
      }
    }
  }
}



// Responsive design
@media (max-width: 768px) {
  .hero-section {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
    
    .hero-content .hero-title {
      font-size: 36px;
    }
    
    .hero-image .feature-grid {
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      
      .feature-item {
        padding: 20px;
      }
    }
  }
  
  .access-grid {
    grid-template-columns: 1fr;
  }
  
  .hero-actions {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .hero-image .feature-grid {
    grid-template-columns: 1fr;
  }
}
</style>
