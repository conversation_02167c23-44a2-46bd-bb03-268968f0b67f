"""
Base event schema definitions for inter-service communication.

This module contains base event structures and common patterns
for message queue integration and service-to-service communication.
"""

from pydantic import BaseModel, Field
from datetime import datetime
from typing import Any, Dict, Optional, Literal, List
from enum import Enum


class EventType(str, Enum):
    """Enumeration of event types in the system."""

    # Data processing events
    DATA_INGESTION_STARTED = "data.ingestion.started"
    DATA_INGESTION_COMPLETED = "data.ingestion.completed"
    DATA_INGESTION_FAILED = "data.ingestion.failed"

    # Tag management events
    TAG_CREATED = "tag.created"
    TAG_UPDATED = "tag.updated"
    TAG_DELETED = "tag.deleted"

    # User events
    USER_TAG_ASSIGNED = "user.tag.assigned"
    USER_TAG_REMOVED = "user.tag.removed"

    # External service events
    SHENCE_UPLOAD_STARTED = "shence.upload.started"
    SHENCE_UPLOAD_COMPLETED = "shence.upload.completed"
    SHENCE_UPLOAD_FAILED = "shence.upload.failed"

    # System events
    SYSTEM_HEALTH_CHECK = "system.health.check"
    SYSTEM_ERROR = "system.error"


class EventPriority(str, Enum):
    """Event priority levels for message queue processing."""

    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


class BaseEvent(BaseModel):
    """
    Base event structure for all system events.

    This provides a consistent structure for events that will be
    published to message queues or used for inter-service communication.
    """

    event_id: str = Field(..., description="Unique event identifier")
    event_type: EventType = Field(..., description="Type of event")
    timestamp: datetime = Field(
        default_factory=datetime.utcnow, description="Event timestamp (UTC)"
    )
    source_service: str = Field(
        default="api-gateway-service", description="Service that generated the event"
    )
    correlation_id: Optional[str] = Field(
        None, description="Correlation ID for request tracing"
    )
    user_id: Optional[str] = Field(
        None, description="User ID associated with the event"
    )
    session_id: Optional[str] = Field(None, description="Session ID for user actions")
    priority: EventPriority = Field(
        default=EventPriority.NORMAL, description="Event processing priority"
    )
    data: Dict[str, Any] = Field(
        default_factory=dict, description="Event-specific data payload"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata"
    )

    class Config:
        """Pydantic configuration."""

        use_enum_values = True
        json_encoders = {datetime: lambda v: v.isoformat()}


class DataIngestionEvent(BaseEvent):
    """Event for data ingestion operations."""

    event_type: Literal[
        EventType.DATA_INGESTION_STARTED,
        EventType.DATA_INGESTION_COMPLETED,
        EventType.DATA_INGESTION_FAILED,
    ]

    # Specific data fields for data ingestion
    job_id: Optional[str] = Field(None, description="Data ingestion job identifier")
    source_type: Optional[str] = Field(
        None, description="Type of data source (mongodb, api, etc.)"
    )
    record_count: Optional[int] = Field(None, description="Number of records processed")
    error_message: Optional[str] = Field(None, description="Error message if failed")


class TagEvent(BaseEvent):
    """Event for tag management operations."""

    event_type: Literal[
        EventType.TAG_CREATED,
        EventType.TAG_UPDATED,
        EventType.TAG_DELETED,
        EventType.USER_TAG_ASSIGNED,
        EventType.USER_TAG_REMOVED,
    ]

    # Specific data fields for tag operations
    tag_id: str = Field(..., description="Tag identifier")
    tag_name: Optional[str] = Field(None, description="Human-readable tag name")
    tag_category: Optional[str] = Field(None, description="Tag category")
    affected_users: Optional[List[str]] = Field(
        None, description="List of affected user IDs"
    )


class ExternalServiceEvent(BaseEvent):
    """Event for external service interactions."""

    event_type: Literal[
        EventType.SHENCE_UPLOAD_STARTED,
        EventType.SHENCE_UPLOAD_COMPLETED,
        EventType.SHENCE_UPLOAD_FAILED,
    ]

    # Specific data fields for external service operations
    service_name: str = Field(..., description="Name of the external service")
    operation: str = Field(..., description="Operation performed")
    request_id: Optional[str] = Field(None, description="External service request ID")
    response_status: Optional[int] = Field(None, description="HTTP response status")
    file_name: Optional[str] = Field(None, description="Uploaded file name")


class SystemEvent(BaseEvent):
    """Event for system-level operations."""

    event_type: Literal[EventType.SYSTEM_HEALTH_CHECK, EventType.SYSTEM_ERROR]

    # Specific data fields for system events
    component: Optional[str] = Field(None, description="System component")
    health_status: Optional[str] = Field(None, description="Health check status")
    error_code: Optional[str] = Field(None, description="System error code")
    stack_trace: Optional[str] = Field(None, description="Error stack trace")
