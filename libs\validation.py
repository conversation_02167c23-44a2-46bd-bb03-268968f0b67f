"""
Shared Validation Dependencies.

Common validation functions used across all API domains.
"""

from fastapi import HTTPException
from typing import Optional, List, Dict, Any
import re
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


def validate_pagination(limit: int = 20, offset: int = 0) -> tuple[int, int]:
    """
    Validate and normalize pagination parameters.

    Args:
        limit: Number of items to return (max 1000)
        offset: Number of items to skip

    Returns:
        Tuple of (validated_limit, validated_offset)

    Raises:
        HTTPException: If parameters are invalid
    """
    if limit < 1:
        raise HTTPException(status_code=400, detail="Limit must be at least 1")
    if limit > 1000:
        raise HTTPException(status_code=400, detail="Limit cannot exceed 1000")
    if offset < 0:
        raise HTTPException(status_code=400, detail="Offset cannot be negative")

    return limit, offset


def validate_date_format(date_str: str, format_str: str = "%Y-%m-%d") -> str:
    """
    Validate date format.

    Args:
        date_str: Date string to validate
        format_str: Expected date format (default: YYYY-MM-DD)

    Returns:
        Validated date string

    Raises:
        HTTPException: If date format is invalid
    """
    try:
        datetime.strptime(date_str, format_str)
        return date_str
    except ValueError:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid date format. Expected format: {format_str}",
        )


def validate_datetime_format(
    datetime_str: str, format_str: str = "%Y-%m-%d %H:%M:%S"
) -> str:
    """
    Validate datetime format.

    Args:
        datetime_str: Datetime string to validate
        format_str: Expected datetime format

    Returns:
        Validated datetime string
    """
    try:
        datetime.strptime(datetime_str, format_str)
        return datetime_str
    except ValueError:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid datetime format. Expected format: {format_str}",
        )


def validate_email_format(email: str) -> str:
    """
    Validate email format using regex.

    Args:
        email: Email string to validate

    Returns:
        Validated email string

    Raises:
        HTTPException: If email format is invalid
    """
    email_pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    if not re.match(email_pattern, email):
        raise HTTPException(status_code=400, detail="Invalid email format")
    return email.lower()


def validate_phone_format(phone: str, country_code: Optional[str] = None) -> str:
    """
    Validate phone number format.

    Args:
        phone: Phone number to validate
        country_code: Optional country code for specific validation

    Returns:
        Validated phone number

    Raises:
        HTTPException: If phone format is invalid
    """
    # Remove common separators
    cleaned_phone = re.sub(r"[\s\-\(\)]", "", phone)

    # Basic international phone number validation
    if country_code == "CN":
        # Chinese phone number validation (11 digits starting with 1)
        if not re.match(r"^1[3-9]\d{9}$", cleaned_phone):
            raise HTTPException(
                status_code=400, detail="Invalid Chinese phone number format"
            )
    else:
        # General international format (7-15 digits, optional + prefix)
        if not re.match(r"^\+?[1-9]\d{6,14}$", cleaned_phone):
            raise HTTPException(status_code=400, detail="Invalid phone number format")

    return cleaned_phone


def validate_url_format(url: str) -> str:
    """
    Validate URL format.

    Args:
        url: URL string to validate

    Returns:
        Validated URL string
    """
    url_pattern = r"^https?:\/\/(?:[-\w.])+(?:\:[0-9]+)?(?:\/(?:[\w\/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?$"
    if not re.match(url_pattern, url):
        raise HTTPException(status_code=400, detail="Invalid URL format")
    return url


def validate_uuid_format(uuid_str: str) -> str:
    """
    Validate UUID format.

    Args:
        uuid_str: UUID string to validate

    Returns:
        Validated UUID string
    """
    uuid_pattern = (
        r"^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$"
    )
    if not re.match(uuid_pattern, uuid_str.lower()):
        raise HTTPException(status_code=400, detail="Invalid UUID format")
    return uuid_str.lower()


def validate_id_format(id_str: str, id_type: str = "general") -> str:
    """
    Validate ID format based on type.

    Args:
        id_str: ID string to validate
        id_type: Type of ID (general, user_id, competition_id, etc.)

    Returns:
        Validated ID string
    """
    if id_type == "user_id":
        # User ID validation (alphanumeric, 3-50 characters)
        if not re.match(r"^[a-zA-Z0-9_]{3,50}$", id_str):
            raise HTTPException(
                status_code=400,
                detail="Invalid user ID format (3-50 alphanumeric characters)",
            )
    elif id_type == "competition_id":
        # Competition ID validation
        if not re.match(r"^[a-zA-Z0-9_-]{3,100}$", id_str):
            raise HTTPException(status_code=400, detail="Invalid competition ID format")
    else:
        # General ID validation (non-empty, reasonable length)
        if not id_str or len(id_str) > 255:
            raise HTTPException(status_code=400, detail="Invalid ID format")

    return id_str


def validate_sort_parameters(
    sort_by: Optional[str] = None,
    sort_order: Optional[str] = None,
    allowed_fields: Optional[List[str]] = None,
) -> tuple[Optional[str], Optional[str]]:
    """
    Validate sort parameters.

    Args:
        sort_by: Field to sort by
        sort_order: Sort order (asc/desc)
        allowed_fields: List of allowed sort fields

    Returns:
        Tuple of (validated_sort_by, validated_sort_order)
    """
    if sort_by is not None:
        if allowed_fields and sort_by not in allowed_fields:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid sort field. Allowed fields: {', '.join(allowed_fields)}",
            )

    if sort_order is not None:
        if sort_order.lower() not in ["asc", "desc"]:
            raise HTTPException(
                status_code=400, detail="Sort order must be 'asc' or 'desc'"
            )
        sort_order = sort_order.lower()

    return sort_by, sort_order


def validate_filter_parameters(
    filters: Dict[str, Any], allowed_filters: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Validate filter parameters.

    Args:
        filters: Dictionary of filter parameters
        allowed_filters: List of allowed filter keys

    Returns:
        Validated filter dictionary
    """
    if allowed_filters:
        invalid_filters = set(filters.keys()) - set(allowed_filters)
        if invalid_filters:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid filter fields: {', '.join(invalid_filters)}. "
                f"Allowed filters: {', '.join(allowed_filters)}",
            )

    # Remove empty filter values
    validated_filters = {k: v for k, v in filters.items() if v is not None and v != ""}

    return validated_filters


def validate_search_query(
    query: str, min_length: int = 1, max_length: int = 255
) -> str:
    """
    Validate search query parameters.

    Args:
        query: Search query string
        min_length: Minimum query length
        max_length: Maximum query length

    Returns:
        Validated and cleaned query string
    """
    if not query or not isinstance(query, str):
        raise HTTPException(status_code=400, detail="Search query is required")

    # Strip whitespace
    query = query.strip()

    if len(query) < min_length:
        raise HTTPException(
            status_code=400,
            detail=f"Search query must be at least {min_length} characters",
        )

    if len(query) > max_length:
        raise HTTPException(
            status_code=400,
            detail=f"Search query cannot exceed {max_length} characters",
        )

    # Basic sanitization - remove potentially dangerous characters
    # Allow alphanumeric, spaces, and common punctuation
    if not re.match(r"^[a-zA-Z0-9\s\-_.,!?()]+$", query):
        raise HTTPException(
            status_code=400,
            detail="Search query contains invalid characters",
        )

    return query


def validate_comma_separated_list(
    value: str, item_validator: Optional[callable] = None, max_items: int = 100
) -> List[str]:
    """
    Validate and parse comma-separated list.

    Args:
        value: Comma-separated string
        item_validator: Optional function to validate each item
        max_items: Maximum number of items allowed

    Returns:
        List of validated items
    """
    if not value:
        return []

    items = [item.strip() for item in value.split(",") if item.strip()]

    if len(items) > max_items:
        raise HTTPException(
            status_code=400, detail=f"Too many items. Maximum {max_items} allowed"
        )

    if item_validator:
        validated_items = []
        for item in items:
            try:
                validated_item = item_validator(item)
                validated_items.append(validated_item)
            except Exception as e:
                raise HTTPException(
                    status_code=400, detail=f"Invalid item '{item}': {str(e)}"
                )
        return validated_items

    return items


def validate_date_range(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    date_format: str = "%Y-%m-%d",
) -> tuple[Optional[datetime], Optional[datetime]]:
    """
    Validate date range parameters.

    Args:
        start_date: Start date string
        end_date: End date string
        date_format: Expected date format

    Returns:
        Tuple of (start_datetime, end_datetime)
    """
    start_dt = None
    end_dt = None

    if start_date:
        start_dt = datetime.strptime(
            validate_date_format(start_date, date_format), date_format
        )

    if end_date:
        end_dt = datetime.strptime(
            validate_date_format(end_date, date_format), date_format
        )

    if start_dt and end_dt and start_dt > end_dt:
        raise HTTPException(
            status_code=400, detail="Start date must be before or equal to end date"
        )

    return start_dt, end_dt


# User-specific validation functions
def validate_user_profile_data(profile_data: dict) -> dict:
    """Validate user profile data."""
    # Validate email format if provided
    if "email" in profile_data and profile_data["email"]:
        profile_data["email"] = validate_email_format(profile_data["email"])

    # Validate phone format if provided
    if "phone" in profile_data and profile_data["phone"]:
        profile_data["phone"] = validate_phone_format(profile_data["phone"])

    # Validate name length
    if "name" in profile_data and profile_data["name"]:
        if len(profile_data["name"]) > 100:
            raise HTTPException(
                status_code=400, detail="Name cannot exceed 100 characters"
            )

    # Validate bio length
    if "bio" in profile_data and profile_data["bio"]:
        if len(profile_data["bio"]) > 1000:
            raise HTTPException(
                status_code=400, detail="Bio cannot exceed 1000 characters"
            )

    return profile_data


def validate_identifier_type(identifier_type: str) -> str:
    """Validate identifier type for user ID resolution."""
    allowed_types = {"phone", "email", "user_id", "external_id"}
    if identifier_type not in allowed_types:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid identifier type. Allowed: {', '.join(allowed_types)}",
        )
    return identifier_type


# Community-specific validation functions
def validate_discovery_criteria(criteria: dict) -> dict:
    """Validate user discovery criteria."""
    # Ensure at least one discovery criteria is provided
    required_fields = ["locations", "universities", "majors", "identity", "experiences"]
    if not any(criteria.get(field) for field in required_fields):
        raise HTTPException(
            status_code=400, detail="At least one discovery criteria must be provided"
        )

    # Validate date format if provided
    if "register_start_date" in criteria and criteria["register_start_date"]:
        validate_date_format(criteria["register_start_date"])

    return criteria


def validate_search_parameters(query: str, category: str) -> tuple[str, str]:
    """Validate community search parameters."""
    # Validate search query
    query = validate_search_query(query, min_length=1, max_length=255)

    # Validate search category
    allowed_categories = {"users", "universities", "majors", "content_creators", "all"}
    if category not in allowed_categories:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid search category. Allowed: {', '.join(allowed_categories)}",
        )

    return query, category
