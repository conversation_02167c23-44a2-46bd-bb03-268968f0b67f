import streamlit as st
import datetime
from libs.api import get_user, get_universities, get_majors, get_content_creator
from libs.constants import REGIONS
import pandas as pd
import time


def timeit(
    start_time,
):
    """ """
    return time.time() - start_time


st.title("社区相关")


if "data" not in st.session_state:
    st.session_state.data = ""


@st.cache_data
def get_university_lst():
    """
    获取所有大学信息
    """
    response = get_universities().json()
    if len(response["data"]) == 0 and response["message"] != "success":
        st.error("未找到对应的大学")
        return []
    elif response["message"] == "success":
        return response["data"]
    else:
        st.error("获取大学数据失败")
        return []


@st.cache_data
def get_major_lst():
    """
    获取所有专业信息
    """
    response = get_majors().json()
    if len(response["data"]) == 0 and response["message"] != "success":
        st.error("未找到对应的专业")
        return []
    elif response["message"] == "success":
        return response["data"]
    else:
        st.error("获取专业数据失败")
        return []


@st.cache_data
def convert_df(df):
    # IMPORTANT: Cache the conversion to prevent computation on every rerun
    return df.to_csv(index=False).encode("utf-8-sig")


@st.experimental_dialog("开始下载")
def download_data(file_name, hinter="用户身份信息"):
    st.write("即将下载【{}】的数据，点击确认".format(hinter))
    st.download_button(
        label="下载",
        data=st.session_state.data,
        file_name="{}.csv".format(file_name),
        mime="text/csv",
        use_container_width=True,
    )


st.markdown("""#### I. 查询社区用户信息
- 所有查询结果均包含的字段：
1. user_id: 用户 ID
2. user_name: 社区用户名
3. join_date: 加入社区的日期
4. last_login_date: 最后一次登录的日期
5. Email: 邮箱(如有)
6. Phone: 手机号(如有)
""")
st.info("""大批量的查询较慢，请耐心等待 ღ( ´･ᴗ･` )""")

with st.expander("根据当前身份获取用户信息"):
    identity_mapping = {
        "学生": "student",
        "在职": "employed",
    }
    identity_type = st.selectbox(
        "用户类型",
        options=identity_mapping.keys(),
        placeholder="用户身份选择",
        index=None,
    )
    register_start_date = st.date_input(
        "最早注册日期 (默认查询距今 90 天内注册的用户）",
        value=datetime.datetime.now().date() - datetime.timedelta(days=90),
        help="返回注册时间在所选日期之后的数据",
    )

    if st.button(
        "查询",
        key="query_identity",
        help="筛选当前符合条件的用户",
        use_container_width=True,
    ):
        if not identity_type:
            st.error("请选择用户类型")
        else:
            start_time = time.time()
            with st.spinner("查询中..."):
                response = get_user(
                    {
                        "identity": [identity_type],
                        "register_start_date": register_start_date.strftime("%Y-%m-%d"),
                    }
                ).json()
                print(time.time(), timeit(start_time))
                if len(response["data"]) > 0:
                    st.success(
                        "查询成功," + "共有 {} 条数据".format(len(response["data"]))
                    )
                    st.session_state.data = convert_df(pd.DataFrame(response["data"]))
                else:
                    st.error("查询失败：{}".format(response["message"]))

            if st.session_state.data:
                try:
                    download_data("user_info_by_identity")
                except:
                    st.error("下载失败")

with st.expander("根据地区获取用户信息"):
    locations = st.multiselect(
        "地区", placeholder="选择地区(最多选5个)", options=REGIONS, max_selections=5
    )
    register_start_date = st.date_input(
        "最早注册日期 (默认查询距今 90 天内注册的用户）",
        value=datetime.datetime.now().date() - datetime.timedelta(days=90),
        help="返回注册时间在所选日期之后的数据",
        key="location_query_start_date",
    )

    if st.button(
        "查询",
        help="筛选当前符合条件的用户",
        use_container_width=True,
        key="location_query_button",
    ):
        if not locations:
            st.error("请选择用户所在地区")
        else:
            with st.spinner("查询中..."):
                response = get_user(
                    {
                        "locations": locations,
                        "register_start_date": register_start_date.strftime("%Y-%m-%d"),
                    }
                ).json()
                if len(response["data"]) > 0:
                    st.success(
                        "查询成功," + "共有 {} 条数据".format(len(response["data"]))
                    )
                    st.session_state.data = convert_df(pd.DataFrame(response["data"]))
                else:
                    st.error("查询失败：{}".format(response["message"]))

            if st.session_state.data:
                try:
                    download_data("user_info_by_location")
                except:
                    st.error("下载失败")
    pass

with st.expander("根据所在学校/专业获取用户信息"):
    university_lst = get_university_lst()
    major_lst = get_major_lst()
    if not university_lst:
        st.error("获取学校数据失败")
    else:
        region_box, universities_box = st.columns([1, 2])
        region = region_box.selectbox(
            "所在省市", options=["不限"] + REGIONS, placeholder="留空代表全选", index=0
        )
        university_names = [
            university["university_name"]
            for university in university_lst
            if region == university["region"] or region == "不限"
        ]
        selected_university = universities_box.multiselect(
            "选择学校", options=university_names, placeholder="选择学校，留空代表全选"
        )

        discipline_box, majors_box = st.columns([1, 2])
        discipline = discipline_box.selectbox(
            "学科",
            options=["不限"]
            + list(set([major["discipline_name"] for major in major_lst])),
            index=0,
        )
        major_names = [
            major["major_name"]
            for major in major_lst
            if discipline == major["discipline_name"] or discipline == "不限"
        ]
        selected_majors = majors_box.multiselect(
            "所选专业", options=major_names, placeholder="选择专业，留空代表全选"
        )

        register_start_date = st.date_input(
            "最早注册日期 (默认查询距今 90 天内注册的用户）",
            value=datetime.datetime.now().date() - datetime.timedelta(days=90),
            help="返回注册时间在所选日期之后的数据",
            key="university_query_start_date",
        )
    if st.button(
        "查询",
        help="筛选当前符合条件的用户",
        use_container_width=True,
        key="univ_query_button",
    ):
        if not selected_university and not selected_majors:
            st.error("至少选择学校或专业中的一项进行查询")
        else:
            with st.spinner("查询中..."):
                response = get_user(
                    {
                        "universities": selected_university,
                        "majors": selected_majors,
                        "register_start_date": register_start_date.strftime("%Y-%m-%d"),
                    }
                ).json()
                if len(response["data"]) > 0:
                    st.success(
                        "查询成功," + "共有 {} 条数据".format(len(response["data"]))
                    )
                    st.session_state.data = convert_df(pd.DataFrame(response["data"]))
                elif len(response["data"]) == 0 and response["message"] != "success":
                    st.error("查询失败：{}".format("无法找到对应的数据"))
                else:
                    st.error("查询失败：{}".format(response["message"]))

            if st.session_state.data:
                try:
                    download_data("user_info_by_university")
                except:
                    st.error("下载失败")

with st.expander("查询创作者信息") as expander:
    query_start_date = st.date_input(
        "最早成为创作者的时间",
        value=datetime.datetime.now().date() - datetime.timedelta(days=365 * 5),
        help="返回注册时间在所选日期之后的数据",
        key="content_creator_query_start_date",
    )
    if st.button(
        "查询",
        help="找到当前符合条件的创作者",
        use_container_width=True,
        key="content_creator_query_button",
    ):
        if not query_start_date:
            st.error("请选择最早成为创作者的时间")
        else:
            with st.spinner("查询中..."):
                response = get_content_creator(
                    {"register_start_date": query_start_date.strftime("%Y-%m-%d")}
                ).json()
                if len(response["data"]) > 0:
                    st.success(
                        "查询成功," + "共有 {} 条数据".format(len(response["data"]))
                    )
                    st.session_state.data = convert_df(pd.DataFrame(response["data"]))
                else:
                    st.error("查询失败：{}".format(response["message"]))

                if st.session_state.data:
                    try:
                        download_data("content_creator_info")
                    except:
                        st.error("下载失败")
