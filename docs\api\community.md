# Community API Documentation

## Overview

The Community API provides endpoints for managing community-related data including universities, majors, user discovery, and content creator management. All endpoints are prefixed with `/api/v1/community`.

## Authentication

All endpoints require authentication and specific permission checks as noted per endpoint.

## Endpoints

### Universities and Majors

#### List Universities
```http
GET /api/v1/community/universities
```

**Description**: Get a list of all universities in the platform with optional filtering.

**Parameters**:
- `limit` (query, optional): Number of universities to return (max: 1000, default: -1 for all)
- `offset` (query, optional): Number of universities to skip (default: -1)
- `country` (query, optional): Filter by country
- `province` (query, optional): Filter by province

**Response**: `SuccessResponse` with `UniversityListResponse`

**Permissions**: Requires `user_data_access`

**Example Response**:
```json
{
  "success": true,
  "data": [
    {
      "university_id": "string",
      "university_name": "Beijing University",
      "country": "China",
      "province": "Beijing"
    }
  ]
}
```

---

#### List Majors
```http
GET /api/v1/community/majors
```

**Description**: Get a list of all majors across all universities.

**Parameters**:
- `limit` (query, optional): Number of majors to return (1-1000, default: 20)
- `offset` (query, optional): Number of majors to skip (default: 0)

**Response**: `SuccessResponse` with `MajorListResponse`

**Permissions**: Requires `user_data_access`

**Example Response**:
```json
{
  "success": true,
  "data": [
    {
      "major_id": "string",
      "major_name": "Computer Science",
      "discipline": "Engineering"
    }
  ]
}
```

---

### Content Creators (Currently Disabled)

The content creators endpoint is currently commented out in the codebase but would provide:

#### Get Content Creators
```http
POST /api/v1/community/content_creators
```

**Description**: Fetch content creators based on start date criteria.

**Request Body**: `ContentCreatorsRequest`
```json
{
  "start_date": "2023-01-01"
}
```

**Response**: `SuccessResponse` with content creator data

**Permissions**: Requires `content_creator_access`

**Status**: Currently disabled/commented out

---

## Data Models

### University Response
```json
{
  "university_id": "string",
  "university_name": "string",
  "country": "string",
  "province": "string"
}
```

### Major Response
```json
{
  "major_id": "string", 
  "major_name": "string",
  "discipline": "string"
}
```

### List Response Format
All list endpoints return data in the following format:
```json
{
  "success": true,
  "data": [...],
  "total": 100,
  "limit": 20,
  "offset": 0
}
```

## Error Responses

All endpoints may return standard error responses:
- `400 Bad Request`: Invalid parameters
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server error

## Usage Examples

### Frontend Integration

#### Fetch Universities for Dropdown
```javascript
// Get all universities
const response = await fetch('/api/v1/community/universities');
const data = await response.json();

// Get universities by country
const chinaUniversities = await fetch('/api/v1/community/universities?country=China');
```

#### Fetch Majors with Pagination
```javascript
// Get first 50 majors
const majors = await fetch('/api/v1/community/majors?limit=50&offset=0');
const data = await majors.json();
```

#### Filter Universities by Province
```javascript
// Get universities in Beijing
const beijingUniversities = await fetch('/api/v1/community/universities?province=Beijing');
```

## Notes for Frontend Development

1. **Pagination**: Use `limit` and `offset` parameters for pagination
2. **Filtering**: Universities support country and province filtering
3. **Data Structure**: All responses follow the standard `SuccessResponse` format
4. **Error Handling**: Always check the `success` field in responses
5. **Authentication**: Ensure proper authentication headers are included
6. **Permissions**: Different endpoints require different permission levels

## Related APIs

- **Users API**: For user profile and discovery functionality
- **Analytics API**: For university and major statistics
- **Competitions API**: For competition-related university data
