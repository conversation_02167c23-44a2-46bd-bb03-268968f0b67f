import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActive<PERSON><PERSON>, createPinia } from 'pinia'
import { useAuthStore } from '../../../frontend/src/stores/auth'

// Mock axios
vi.mock('axios', () => ({
  default: {
    create: vi.fn(() => ({
      post: vi.fn(),
      get: vi.fn(),
      interceptors: {
        request: { use: vi.fn() },
        response: { use: vi.fn() }
      }
    }))
  }
}))

// Mock js-cookie
vi.mock('js-cookie', () => ({
  default: {
    get: vi.fn(),
    set: vi.fn(),
    remove: vi.fn()
  }
}))

describe('Auth Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('initializes with correct default state', () => {
    const store = useAuthStore()
    
    expect(store.user).toBeNull()
    expect(store.token).toBeNull()
    expect(store.isLoggedIn).toBe(false)
    expect(store.loading).toBe(false)
    expect(store.error).toBeNull()
  })

  it('has correct getters', () => {
    const store = useAuthStore()
    
    expect(store.isAuthenticated).toBe(false)
    expect(store.userInfo).toBeNull()
  })

  it('login action updates state correctly on success', async () => {
    const store = useAuthStore()
    const mockResponse = {
      data: {
        token: 'mock-token',
        user: { id: 1, email: '<EMAIL>', name: 'Test User' }
      }
    }

    // Mock successful API response
    const mockApi = await import('../../../frontend/src/services/api')
    mockApi.default.post = vi.fn().mockResolvedValue(mockResponse)

    await store.login({ email: '<EMAIL>', password: 'password' })

    expect(store.token).toBe('mock-token')
    expect(store.user).toEqual(mockResponse.data.user)
    expect(store.isLoggedIn).toBe(true)
    expect(store.loading).toBe(false)
    expect(store.error).toBeNull()
  })

  it('login action handles errors correctly', async () => {
    const store = useAuthStore()
    const mockError = new Error('Invalid credentials')

    // Mock failed API response
    const mockApi = await import('../../../frontend/src/services/api')
    mockApi.default.post = vi.fn().mockRejectedValue(mockError)

    await store.login({ email: '<EMAIL>', password: 'wrong-password' })

    expect(store.token).toBeNull()
    expect(store.user).toBeNull()
    expect(store.isLoggedIn).toBe(false)
    expect(store.loading).toBe(false)
    expect(store.error).toBe('Invalid credentials')
  })

  it('logout action clears state correctly', () => {
    const store = useAuthStore()
    
    // Set some state first
    store.token = 'mock-token'
    store.user = { id: 1, email: '<EMAIL>' }
    store.isLoggedIn = true

    store.logout()

    expect(store.token).toBeNull()
    expect(store.user).toBeNull()
    expect(store.isLoggedIn).toBe(false)
    expect(store.error).toBeNull()
  })

  it('register action works correctly', async () => {
    const store = useAuthStore()
    const mockResponse = {
      data: {
        message: 'Registration successful',
        user: { id: 1, email: '<EMAIL>', name: 'Test User' }
      }
    }

    // Mock successful API response
    const mockApi = await import('../../../frontend/src/services/api')
    mockApi.default.post = vi.fn().mockResolvedValue(mockResponse)

    const result = await store.register({
      email: '<EMAIL>',
      password: 'password',
      name: 'Test User'
    })

    expect(result).toBe(true)
    expect(store.loading).toBe(false)
    expect(store.error).toBeNull()
  })
})
