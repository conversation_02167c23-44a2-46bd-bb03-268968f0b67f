<template>
  <div class="qualification-form">
    <div class="form-header">
      <el-button @click="$router.back()" type="default" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
        Back
      </el-button>
      
      <h1>{{ isEdit ? 'Edit' : 'Create' }} Qualification</h1>
    </div>

    <el-card>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="140px"
        @submit.prevent="handleSubmit"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="Qualification Name" prop="qualification_name" required>
              <el-input
                v-model="form.qualification_name"
                placeholder="Enter qualification name"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="Credit Amount" prop="credit" required>
              <el-input-number
                v-model="form.credit"
                :min="1"
                :max="10000"
                :step="10"
                placeholder="Credit amount"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="Qualification Type" prop="qualification_type" required>
              <el-select v-model="form.qualification_type" placeholder="Select type" style="width: 100%">
                <el-option
                  v-for="type in qualificationTypes"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="Qualification Logic" prop="qualification_logic" required>
              <el-select v-model="form.qualification_logic" placeholder="Select logic" style="width: 100%">
                <el-option
                  v-for="logic in qualificationLogics"
                  :key="logic.value"
                  :label="logic.label"
                  :value="logic.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item 
              label="Score Threshold" 
              prop="score_threshold"
              :required="needsScoreThreshold"
            >
              <el-input-number
                v-model="form.score_threshold"
                :min="0"
                :max="100"
                :step="1"
                :disabled="!needsScoreThreshold"
                placeholder="Score threshold (%)"
                style="width: 100%"
              />
              <div class="form-help-text">
                Required for score-based qualifications
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="Related Task ID" prop="related_task_id">
              <el-input
                v-model="form.related_task_id"
                placeholder="Enter related task ID (optional)"
                maxlength="50"
              />
              <div class="form-help-text">
                Optional: Link to specific task or challenge
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="Description" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="4"
            placeholder="Enter qualification description (optional)"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <!-- Preview Section -->
        <el-divider content-position="left">Preview</el-divider>
        
        <div class="qualification-preview">
          <el-card shadow="never" class="preview-card">
            <template #header>
              <div class="preview-header">
                <h4>{{ form.qualification_name || 'Qualification Name' }}</h4>
                <el-tag type="success">{{ form.credit || 0 }} credits</el-tag>
              </div>
            </template>
            
            <div class="preview-content">
              <div class="preview-row">
                <span class="preview-label">Type:</span>
                <span class="preview-value">{{ getQualificationTypeName(form.qualification_type) }}</span>
              </div>
              <div class="preview-row">
                <span class="preview-label">Logic:</span>
                <span class="preview-value">{{ getQualificationLogicName(form.qualification_logic) }}</span>
              </div>
              <div class="preview-row" v-if="form.score_threshold">
                <span class="preview-label">Score Threshold:</span>
                <span class="preview-value">{{ form.score_threshold }}%</span>
              </div>
              <div class="preview-row" v-if="form.related_task_id">
                <span class="preview-label">Related Task:</span>
                <span class="preview-value">{{ form.related_task_id }}</span>
              </div>
              <div class="preview-row" v-if="form.description">
                <span class="preview-label">Description:</span>
                <span class="preview-value">{{ form.description }}</span>
              </div>
            </div>
          </el-card>
        </div>

        <!-- Form Actions -->
        <el-form-item>
          <div class="form-actions">
            <el-button @click="$router.back()">Cancel</el-button>
            <el-button type="primary" @click="handleSubmit" :loading="submitting">
              {{ isEdit ? 'Update' : 'Create' }} Qualification
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// State
const formRef = ref()
const submitting = ref(false)

// Computed
const isEdit = computed(() => !!route.params.qualificationId)
const competitionId = computed(() => route.params.competitionId)
const qualificationId = computed(() => route.params.qualificationId)

const needsScoreThreshold = computed(() => {
  return form.qualification_type === 1 // Score Based
})

// Form data
const form = reactive({
  competition_id: '',
  qualification_name: '',
  credit: 100,
  qualification_type: 1,
  qualification_logic: 1,
  score_threshold: 80,
  related_task_id: '',
  description: ''
})

// Form validation rules
const rules = {
  qualification_name: [
    { required: true, message: 'Please enter qualification name', trigger: 'blur' },
    { min: 3, max: 100, message: 'Length should be 3 to 100 characters', trigger: 'blur' }
  ],
  credit: [
    { required: true, message: 'Please enter credit amount', trigger: 'blur' },
    { type: 'number', min: 1, max: 10000, message: 'Credit must be between 1 and 10000', trigger: 'blur' }
  ],
  qualification_type: [
    { required: true, message: 'Please select qualification type', trigger: 'change' }
  ],
  qualification_logic: [
    { required: true, message: 'Please select qualification logic', trigger: 'change' }
  ],
  score_threshold: [
    { 
      validator: (rule, value, callback) => {
        if (needsScoreThreshold.value && (value === null || value === undefined)) {
          callback(new Error('Score threshold is required for score-based qualifications'))
        } else if (value !== null && value !== undefined && (value < 0 || value > 100)) {
          callback(new Error('Score threshold must be between 0 and 100'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ]
}

// Options
const qualificationTypes = [
  { value: 1, label: 'Score Based' },
  { value: 2, label: 'Rank Based' },
  { value: 3, label: 'Participation' },
  { value: 4, label: 'Completion' },
  { value: 5, label: 'Custom' }
]

const qualificationLogics = [
  { value: 1, label: 'Greater Than' },
  { value: 2, label: 'Less Than' },
  { value: 3, label: 'Equal To' },
  { value: 4, label: 'Between' },
  { value: 5, label: 'Top N' },
  { value: 6, label: 'Bottom N' }
]

// Methods
const getQualificationTypeName = (type) => {
  const typeObj = qualificationTypes.find(t => t.value === type)
  return typeObj?.label || 'Unknown'
}

const getQualificationLogicName = (logic) => {
  const logicObj = qualificationLogics.find(l => l.value === logic)
  return logicObj?.label || 'Unknown'
}

const loadQualification = async () => {
  if (!isEdit.value) return

  try {
    // TODO: Implement actual API call to load qualification
    // const response = await qualificationService.getQualification(qualificationId.value)
    // if (response.success) {
    //   Object.assign(form, response.data)
    // }
    
    // Mock data for now
    ElMessage.info('Loading qualification data...')
  } catch (error) {
    console.error('Failed to load qualification:', error)
    ElMessage.error('Failed to load qualification data')
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
  } catch (error) {
    ElMessage.error('Please fix form errors before submitting')
    return
  }

  submitting.value = true

  try {
    // Set competition ID
    form.competition_id = competitionId.value

    if (isEdit.value) {
      // TODO: Implement actual update API call
      // await qualificationService.updateQualification(qualificationId.value, form)
      ElMessage.success('Qualification updated successfully')
    } else {
      // TODO: Implement actual create API call
      // await qualificationService.createQualification(form)
      ElMessage.success('Qualification created successfully')
    }

    // Navigate back to competition detail
    router.push(`/competitions/${competitionId.value}`)
  } catch (error) {
    console.error('Failed to save qualification:', error)
    ElMessage.error('Failed to save qualification')
  } finally {
    submitting.value = false
  }
}

// Lifecycle
onMounted(() => {
  form.competition_id = competitionId.value
  if (isEdit.value) {
    loadQualification()
  }
})
</script>

<style lang="scss" scoped>
.qualification-form {
  max-width: 1000px;
  margin: 0 auto;
}

.form-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;

  .back-button {
    flex-shrink: 0;
  }

  h1 {
    font-size: 28px;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
  }
}

.form-help-text {
  font-size: 12px;
  color: var(--text-color-secondary);
  margin-top: 4px;
}

.qualification-preview {
  margin: 24px 0;

  .preview-card {
    background-color: var(--bg-color-soft);

    .preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--text-color);
      }
    }

    .preview-content {
      .preview-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid var(--border-color-light);

        &:last-child {
          border-bottom: none;
        }

        .preview-label {
          font-size: 14px;
          color: var(--text-color-secondary);
          font-weight: 500;
        }

        .preview-value {
          font-size: 14px;
          color: var(--text-color);
        }
      }
    }
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

// Responsive design
@media (max-width: 768px) {
  .form-header {
    flex-direction: column;
    align-items: stretch;

    h1 {
      text-align: center;
    }
  }

  .form-actions {
    flex-direction: column;
  }
}
</style>
