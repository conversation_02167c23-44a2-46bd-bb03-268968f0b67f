/**
 * Mock API Handler
 * Intercepts API calls and returns mock data for development
 */

import { authService } from './mock/authService.js'
import { competitionService } from './mock/competitionService.js'
import { creditService } from './mock/creditService.js'
import { schoolService } from './mock/schoolService.js'

// Mock API configuration
const MOCK_ENABLED = import.meta.env.VITE_MOCK_API !== 'false'
const MOCK_DELAY = parseInt(import.meta.env.VITE_MOCK_DELAY) || 500

console.log('🔧 Mock API Handler:', MOCK_ENABLED ? 'ENABLED' : 'DISABLED')

// Helper function to simulate network delay
const delay = (ms = MOCK_DELAY) => new Promise(resolve => setTimeout(resolve, ms))

// Mock response wrapper
const createMockResponse = (data, status = 200) => {
  return {
    data,
    status,
    statusText: status === 200 ? 'OK' : 'Error',
    headers: {
      'content-type': 'application/json'
    },
    config: {},
    request: {}
  }
}

// Mock error response
const createMockError = (message, status = 400, code = 'MOCK_ERROR') => {
  const error = new Error(message)
  error.response = {
    data: {
      success: false,
      error: {
        code,
        message
      }
    },
    status,
    statusText: 'Error'
  }
  return error
}

// Route handlers
const routeHandlers = {
  // Authentication routes
  'POST /auth/login': async (data) => {
    const result = await authService.login(data)
    if (result.success) {
      return createMockResponse(result.data)
    } else {
      throw createMockError(result.error.message, 401, result.error.code)
    }
  },

  // Registration disabled - admin-only system
  'POST /auth/register': async () => {
    throw createMockError('Registration is disabled. This is an admin-only system.', 403, 'REGISTRATION_DISABLED')
  },

  'GET /auth/me': async (data, headers) => {
    const token = headers.Authorization?.replace('Bearer ', '')
    const result = await authService.me(token)
    if (result.success) {
      return createMockResponse(result.data)
    } else {
      throw createMockError(result.error.message, 401, result.error.code)
    }
  },

  'POST /auth/logout': async (data, headers) => {
    const token = headers.Authorization?.replace('Bearer ', '')
    const result = await authService.logout(token)
    return createMockResponse(result)
  },

  // Camp Admin Authentication routes (same as regular auth)
  'POST /camp-admin/auth/login': async (data) => {
    const result = await authService.login(data)
    if (result.success) {
      return createMockResponse(result.data)
    } else {
      throw createMockError(result.error.message, 401, result.error.code)
    }
  },

  'GET /camp-admin/auth/me': async (data, headers) => {
    const token = headers.Authorization?.replace('Bearer ', '')
    const result = await authService.me(token)
    if (result.success) {
      return createMockResponse(result.data)
    } else {
      throw createMockError(result.error.message, 401, result.error.code)
    }
  },

  'POST /camp-admin/auth/logout': async (data, headers) => {
    const token = headers.Authorization?.replace('Bearer ', '')
    const result = await authService.logout(token)
    return createMockResponse(result)
  },

  // Competition routes
  'GET /competitions': async (data, headers, params) => {
    const result = await competitionService.getCompetitions(params)
    if (result.success) {
      return createMockResponse(result)
    } else {
      throw createMockError(result.error.message, 500, result.error.code)
    }
  },

  'GET /camp-admin/routes': async (data, headers, params) => {
    const result = await competitionService.getRoutes(params)
    if (result.success) {
      return createMockResponse(result)
    } else {
      throw createMockError(result.error.message, 500, result.error.code)
    }
  },

  'GET /camp-admin/': async (data, headers, params) => {
    const result = await competitionService.getCompetitions(params)
    if (result.success) {
      return createMockResponse(result)
    } else {
      throw createMockError(result.error.message, 500, result.error.code)
    }
  },

  'GET /competitions/:id': async (data, headers, params, pathParams) => {
    const result = await competitionService.getCompetition(pathParams.id)
    if (result.success) {
      return createMockResponse(result.data)
    } else {
      throw createMockError(result.error.message, 404, result.error.code)
    }
  },

  'POST /competitions': async (data) => {
    const result = await competitionService.createCompetition(data)
    if (result.success) {
      return createMockResponse(result.data)
    } else {
      throw createMockError(result.error.message, 400, result.error.code)
    }
  },

  // Credit routes
  'GET /credits': async (data, headers, params) => {
    const result = await creditService.getCredits(params)
    if (result.success) {
      return createMockResponse(result)
    } else {
      throw createMockError(result.error.message, 500, result.error.code)
    }
  },

  'POST /credits': async (data) => {
    const result = await creditService.createCredit(data)
    if (result.success) {
      return createMockResponse(result.data)
    } else {
      throw createMockError(result.error.message, 400, result.error.code)
    }
  },

  // School routes
  'GET /schools': async (data, headers, params) => {
    const result = await schoolService.getSchools(params)
    if (result.success) {
      return createMockResponse(result)
    } else {
      throw createMockError(result.error.message, 500, result.error.code)
    }
  },

  'GET /schools/:id': async (data, headers, params, pathParams) => {
    const result = await schoolService.getSchool(pathParams.id)
    if (result.success) {
      return createMockResponse(result.data)
    } else {
      throw createMockError(result.error.message, 404, result.error.code)
    }
  },

  // Camp Admin API routes
  'GET /camp-admin/credits/history': async (data, headers, params) => {
    const result = await creditService.getCredits(params)
    if (result.success) {
      return createMockResponse(result)
    } else {
      throw createMockError(result.error.message, 500, result.error.code)
    }
  },

  'GET /camp-admin/logs': async (data, headers, params) => {
    // Return mock admin logs for recent activities
    const mockLogs = [
      {
        id: 1,
        action_type: 1,
        action_related_id: 'comp_123',
        remark: 'Summer Challenge 2024 created',
        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 2,
        action_type: 2,
        action_related_id: 'credit_456',
        remark: 'Credits distributed to 150 users',
        created_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 3,
        action_type: 4,
        action_related_id: 'qual_789',
        remark: 'New qualification created for AI track',
        created_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()
      }
    ]

    return createMockResponse({
      success: true,
      data: mockLogs.slice(0, params.limit || 10)
    })
  },

  // Community API routes
  'GET /community/universities': async (data, headers, params) => {
    const result = await schoolService.getSchools(params)
    if (result.success) {
      // Transform school data to university format
      const universities = result.data.map(school => ({
        university_id: school.id,
        university_name: school.name,
        country: school.country || 'China',
        province: school.province || 'Beijing'
      }))

      return createMockResponse({
        success: true,
        data: universities,
        total: universities.length,
        limit: params.limit || -1,
        offset: params.offset || -1
      })
    } else {
      throw createMockError(result.error.message, 500, result.error.code)
    }
  },

  // Analytics API routes
  'POST /analytics/camp-only/statistics/summary': async () => {
    return createMockResponse({
      success: true,
      data: {
        total_competitions: 12,
        total_credits: 15420,
        total_schools: 85,
        total_users: 2340
      }
    })
  },

  'POST /analytics/camp-only/rankings/users/total': async (data) => {
    return createMockResponse({
      success: true,
      data: {
        total: 2340,
        page_size: data.page_size || 20,
        current_page: data.current_page || 1,
        res: []
      }
    })
  }
}

// Parse route pattern
const parseRoute = (method, url) => {
  // Remove query parameters
  const [path] = url.split('?')
  const route = `${method} ${path}`
  
  // Check for exact match first
  if (routeHandlers[route]) {
    return { handler: routeHandlers[route], pathParams: {} }
  }
  
  // Check for parameterized routes
  for (const pattern in routeHandlers) {
    const [patternMethod, patternPath] = pattern.split(' ')
    if (patternMethod !== method) continue
    
    const patternParts = patternPath.split('/')
    const pathParts = path.split('/')
    
    if (patternParts.length !== pathParts.length) continue
    
    const pathParams = {}
    let matches = true
    
    for (let i = 0; i < patternParts.length; i++) {
      if (patternParts[i].startsWith(':')) {
        const paramName = patternParts[i].substring(1)
        pathParams[paramName] = pathParts[i]
      } else if (patternParts[i] !== pathParts[i]) {
        matches = false
        break
      }
    }
    
    if (matches) {
      return { handler: routeHandlers[pattern], pathParams }
    }
  }
  
  return null
}

// Mock API interceptor
export const mockApiInterceptor = {
  // Request interceptor
  request: (config) => {
    if (!MOCK_ENABLED) return config
    
    // Add mock flag to identify mock requests
    config._isMockRequest = true
    return config
  },

  // Response interceptor
  response: async (config) => {
    if (!MOCK_ENABLED || !config._isMockRequest) {
      // Let the request go through normally
      throw new Error('Not a mock request')
    }

    const method = config.method.toUpperCase()
    const url = config.url.replace(config.baseURL, '')
    
    console.log(`🔄 Mock API: ${method} ${url}`)
    
    const routeMatch = parseRoute(method, url)
    
    if (!routeMatch) {
      console.warn(`⚠️ Mock API: No handler for ${method} ${url}`)
      throw createMockError(`Mock endpoint not found: ${method} ${url}`, 404, 'ENDPOINT_NOT_FOUND')
    }
    
    try {
      // Parse query parameters
      const urlObj = new URL(url, 'http://localhost')
      const params = Object.fromEntries(urlObj.searchParams)
      
      // Add delay to simulate network latency
      await delay()
      
      // Call the handler
      const response = await routeMatch.handler(
        config.data ? JSON.parse(config.data) : {},
        config.headers || {},
        params,
        routeMatch.pathParams
      )
      
      console.log(`✅ Mock API: ${method} ${url} - ${response.status}`)
      return response
      
    } catch (error) {
      console.error(`❌ Mock API: ${method} ${url} - Error:`, error.message)
      throw error
    }
  }
}

// Export test credentials for easy access
export const getTestCredentials = () => {
  return authService.getTestCredentials()
}

// Export function to check if mock is enabled
export const isMockEnabled = () => MOCK_ENABLED
