/**
 * Mock Competition API Service
 */

import { initializeMockData } from './mockData.js'

// Initialize mock data
const mockData = initializeMockData()
let { competitions, users, routes } = mockData

// Helper functions
const delay = (ms = 500) => new Promise(resolve => setTimeout(resolve, ms))

const paginate = (data, page = 1, limit = 20) => {
  const offset = (page - 1) * limit
  const paginatedData = data.slice(offset, offset + limit)
  
  return {
    data: paginatedData,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: data.length,
      pages: Math.ceil(data.length / limit),
      hasNext: offset + limit < data.length,
      hasPrev: page > 1
    }
  }
}

const filterCompetitions = (competitions, params) => {
  let filtered = [...competitions]
  
  if (params.status) {
    filtered = filtered.filter(comp => comp.status === params.status)
  }
  
  if (params.type) {
    filtered = filtered.filter(comp => comp.type === params.type)
  }
  
  if (params.category) {
    filtered = filtered.filter(comp => comp.category === params.category)
  }
  
  if (params.difficulty) {
    filtered = filtered.filter(comp => comp.difficulty === params.difficulty)
  }
  
  if (params.search) {
    const searchLower = params.search.toLowerCase()
    filtered = filtered.filter(comp => 
      comp.title.toLowerCase().includes(searchLower) ||
      comp.description.toLowerCase().includes(searchLower)
    )
  }
  
  if (params.featured !== undefined) {
    filtered = filtered.filter(comp => comp.featured === params.featured)
  }
  
  if (params.organizerId) {
    filtered = filtered.filter(comp => comp.organizer.id === params.organizerId)
  }
  
  if (params.tags && params.tags.length > 0) {
    filtered = filtered.filter(comp => 
      params.tags.some(tag => comp.tags.includes(tag))
    )
  }
  
  if (params.dateRange) {
    const { start, end } = params.dateRange
    if (start) {
      filtered = filtered.filter(comp => new Date(comp.startDate) >= new Date(start))
    }
    if (end) {
      filtered = filtered.filter(comp => new Date(comp.endDate) <= new Date(end))
    }
  }
  
  return filtered
}

// Mock API functions
export const competitionService = {
  // Get competitions list
  async getCompetitions(params = {}) {
    await delay()
    
    try {
      const filtered = filterCompetitions(competitions, params)
      const result = paginate(filtered, params.page, params.limit)
      
      return {
        success: true,
        ...result
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'FETCH_ERROR',
          message: 'Failed to fetch competitions'
        }
      }
    }
  },

  // Get competition by ID
  async getCompetition(id) {
    await delay()
    
    try {
      const competition = competitions.find(comp => comp.id === id)
      
      if (!competition) {
        return {
          success: false,
          error: {
            code: 'COMPETITION_NOT_FOUND',
            message: 'Competition not found'
          }
        }
      }
      
      return {
        success: true,
        data: competition
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'FETCH_ERROR',
          message: 'Failed to fetch competition'
        }
      }
    }
  },

  // Create competition
  async createCompetition(competitionData) {
    await delay()
    
    try {
      const newCompetition = {
        id: `comp_${competitions.length + 1}`,
        ...competitionData,
        currentParticipants: 0,
        stats: {
          totalParticipants: 0,
          totalTeams: 0,
          totalSubmissions: 0,
          averageScore: 0,
          completionRate: 0,
          participantsBySchool: {},
          participantsByLevel: {}
        },
        organizer: users[0], // Mock current user
        slug: competitionData.title.toLowerCase().replace(/\s+/g, '-'),
        viewCount: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      competitions.push(newCompetition)
      
      return {
        success: true,
        data: newCompetition,
        message: 'Competition created successfully'
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'CREATE_ERROR',
          message: 'Failed to create competition'
        }
      }
    }
  },

  // Update competition
  async updateCompetition(id, competitionData) {
    await delay()
    
    try {
      const index = competitions.findIndex(comp => comp.id === id)
      
      if (index === -1) {
        return {
          success: false,
          error: {
            code: 'COMPETITION_NOT_FOUND',
            message: 'Competition not found'
          }
        }
      }
      
      competitions[index] = {
        ...competitions[index],
        ...competitionData,
        updatedAt: new Date().toISOString()
      }
      
      return {
        success: true,
        data: competitions[index],
        message: 'Competition updated successfully'
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'UPDATE_ERROR',
          message: 'Failed to update competition'
        }
      }
    }
  },

  // Delete competition
  async deleteCompetition(id) {
    await delay()
    
    try {
      const index = competitions.findIndex(comp => comp.id === id)
      
      if (index === -1) {
        return {
          success: false,
          error: {
            code: 'COMPETITION_NOT_FOUND',
            message: 'Competition not found'
          }
        }
      }
      
      competitions.splice(index, 1)
      
      return {
        success: true,
        message: 'Competition deleted successfully'
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'DELETE_ERROR',
          message: 'Failed to delete competition'
        }
      }
    }
  },

  // Join competition
  async joinCompetition(competitionId, joinData) {
    await delay()
    
    try {
      const competition = competitions.find(comp => comp.id === competitionId)
      
      if (!competition) {
        return {
          success: false,
          error: {
            code: 'COMPETITION_NOT_FOUND',
            message: 'Competition not found'
          }
        }
      }
      
      if (competition.currentParticipants >= competition.maxParticipants) {
        return {
          success: false,
          error: {
            code: 'COMPETITION_FULL',
            message: 'Competition is full'
          }
        }
      }
      
      // Mock joining logic
      competition.currentParticipants += 1
      competition.stats.totalParticipants += 1
      
      return {
        success: true,
        message: 'Successfully joined competition'
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'JOIN_ERROR',
          message: 'Failed to join competition'
        }
      }
    }
  },

  // Get competition statistics
  async getCompetitionStats() {
    await delay()

    try {
      const stats = {
        totalCompetitions: competitions.length,
        activeCompetitions: competitions.filter(c => c.status === 'active').length,
        upcomingCompetitions: competitions.filter(c => c.status === 'registration_open').length,
        completedCompetitions: competitions.filter(c => c.status === 'completed').length,
        totalParticipants: competitions.reduce((sum, c) => sum + c.currentParticipants, 0),
        averageParticipants: Math.round(competitions.reduce((sum, c) => sum + c.currentParticipants, 0) / competitions.length),
        popularCategories: competitions.reduce((acc, c) => {
          acc[c.category] = (acc[c.category] || 0) + 1
          return acc
        }, {}),
        monthlyTrend: [
          { month: '2024-01', competitions: 5, participants: 234 },
          { month: '2024-02', competitions: 8, participants: 456 },
          { month: '2024-03', competitions: 12, participants: 678 }
        ]
      }

      return {
        success: true,
        data: stats
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'STATS_ERROR',
          message: 'Failed to fetch statistics'
        }
      }
    }
  },

  // Get routes
  async getRoutes(params = {}) {
    await delay()

    try {
      let filteredRoutes = [...routes]

      // Apply pagination if needed
      if (params.limit || params.offset) {
        const limit = params.limit || 20
        const offset = params.offset || 0
        filteredRoutes = filteredRoutes.slice(offset, offset + limit)
      }

      return {
        success: true,
        data: filteredRoutes
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'ROUTES_ERROR',
          message: 'Failed to fetch routes'
        }
      }
    }
  },

  // Get all qualifications across competitions
  async getAllQualifications(params = {}) {
    await delay()

    try {
      // Get all competitions first
      const competitions = generateCompetitions(50)
      const allQualifications = []

      // Generate qualifications for each competition
      competitions.forEach(competition => {
        const qualCount = Math.floor(Math.random() * 3) + 1 // 1-3 qualifications per competition
        for (let i = 0; i < qualCount; i++) {
          allQualifications.push({
            id: `qual_${competition.id}_${i + 1}`,
            competition_id: competition.id,
            qualification_name: generateQualifications(1)[0].qualification_name,
            credit: Math.floor(Math.random() * 200) + 50, // 50-250 credits
            qualification_type: Math.floor(Math.random() * 5) + 1,
            qualification_logic: Math.floor(Math.random() * 6) + 1,
            score_threshold: Math.floor(Math.random() * 50) + 50, // 50-100%
            related_task_id: Math.random() > 0.5 ? `task_${Math.floor(Math.random() * 100)}` : null,
            is_active: Math.random() > 0.1 // 90% active
          })
        }
      })

      // Apply filters
      let filteredQualifications = allQualifications

      if (params.competition_id) {
        filteredQualifications = filteredQualifications.filter(q => q.competition_id === params.competition_id)
      }

      if (params.qualification_type) {
        filteredQualifications = filteredQualifications.filter(q => q.qualification_type === params.qualification_type)
      }

      // Apply pagination
      const limit = params.limit || 20
      const offset = params.offset || 0
      const paginatedData = filteredQualifications.slice(offset, offset + limit)

      return {
        success: true,
        data: paginatedData,
        pagination: {
          total: filteredQualifications.length,
          limit,
          offset,
          hasNext: offset + limit < filteredQualifications.length,
          hasPrev: offset > 0
        }
      }
    } catch (error) {
      console.error('Mock getAllQualifications error:', error)
      return {
        success: false,
        error: {
          code: 'MOCK_ERROR',
          message: 'Failed to fetch qualifications'
        }
      }
    }
  },

  // Get qualifications for a competition
  async getQualifications(competitionId) {
    await delay()

    try {
      // Mock qualifications data
      const mockQualifications = [
        {
          id: `qual_${competitionId}_1`,
          competition_id: competitionId,
          qualification_name: '优秀表现奖',
          credit: 100,
          qualification_type: 1,
          qualification_logic: 1,
          score_threshold: 80,
          related_task_id: 'task_001',
          description: '在比赛中取得优秀成绩的参赛者'
        },
        {
          id: `qual_${competitionId}_2`,
          competition_id: competitionId,
          qualification_name: '参与奖',
          credit: 50,
          qualification_type: 3,
          qualification_logic: 3,
          score_threshold: null,
          related_task_id: null,
          description: '完成比赛的所有参赛者'
        }
      ]

      return {
        success: true,
        data: mockQualifications
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'QUALIFICATIONS_ERROR',
          message: 'Failed to fetch qualifications'
        }
      }
    }
  },

  // Create qualification
  async createQualification(qualificationData) {
    await delay()

    try {
      const newQualification = {
        id: `qual_${Date.now()}`,
        ...qualificationData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      return {
        success: true,
        data: newQualification,
        message: 'Qualification created successfully'
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'CREATE_QUALIFICATION_ERROR',
          message: 'Failed to create qualification'
        }
      }
    }
  },

  // Update qualification
  async updateQualification(id, qualificationData) {
    await delay()

    try {
      const updatedQualification = {
        id,
        ...qualificationData,
        updated_at: new Date().toISOString()
      }

      return {
        success: true,
        data: updatedQualification,
        message: 'Qualification updated successfully'
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'UPDATE_QUALIFICATION_ERROR',
          message: 'Failed to update qualification'
        }
      }
    }
  },

  // Delete qualification
  async deleteQualification(id) {
    await delay()

    try {
      return {
        success: true,
        message: 'Qualification deleted successfully'
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'DELETE_QUALIFICATION_ERROR',
          message: 'Failed to delete qualification'
        }
      }
    }
  }
}
