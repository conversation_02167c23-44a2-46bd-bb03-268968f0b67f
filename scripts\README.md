# Scripts Directory

This directory contains utility scripts for database management and system setup.

## Database Scripts

### `initialize_db.py`
**Purpose**: Initialize the database schema and connections

**Usage**:
```bash
python scripts/initialize_db.py
```

**Features**:
- Initializes both Tortoise ORM (PostgreSQL) and MongoDB connections
- Sets up database schema
- Handles SSH tunnel configuration if enabled
- Provides both direct execution and API endpoint methods

**Requirements**:
- Valid `.env` file with database configuration
- SSH key file if tunnel is enabled
- Required Python dependencies installed

### `fill_qualification_data.py`
**Purpose**: Populate the database with qualification and competition data

**Usage**:
```bash
python scripts/fill_qualification_data.py
```

**Features**:
- Loads qualification data from MongoDB to PostgreSQL
- Handles data transformation and validation
- Supports clearing existing data with `--clear` flag
- Comprehensive logging and error handling
- SSH tunnel support for remote database connections

**Requirements**:
- Database connections properly configured
- MongoDB and PostgreSQL accessible
- Valid data sources available

## Configuration

Both scripts require:
- `.env` file in the project root with database credentials
- SSH key file (if using SSH tunnel)
- Proper network access to database servers

## Environment Variables

Key environment variables used by these scripts:
- `DATABASE_URL`: PostgreSQL connection string
- `MONGO_HOST`, `MONGO_PORT`, `MONGO_USER`, `MONGO_PASSWORD`, `MONGO_DB`: MongoDB configuration
- `SSH_TUNNEL_*`: SSH tunnel configuration if enabled

## Security Notes

- Never commit SSH keys or credentials to version control
- Use `.env` files for sensitive configuration
- Ensure proper file permissions on SSH keys (600)
- Use SSH tunnels for secure remote database access 