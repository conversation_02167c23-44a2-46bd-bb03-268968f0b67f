/**
 * Authentication Middleware Utilities
 * Helper functions for authentication and authorization
 */

import { useAuthStore } from '@/stores/auth'
import { ElMessage, ElNotification } from 'element-plus'

/**
 * Check if user has required permission
 * Simplified: any authenticated user has all permissions in admin-only system
 * @param {string} permission - Required permission (ignored)
 * @returns {boolean} True if user is authenticated
 */
export const hasPermission = () => {
  const authStore = useAuthStore()
  return authStore.isAuthenticated
}

/**
 * Check if user has required role
 * Simplified: any authenticated user has all roles in admin-only system
 * @param {string} role - Required role (ignored)
 * @returns {boolean} True if user is authenticated
 */
export const hasRole = () => {
  const authStore = useAuthStore()
  return authStore.isAuthenticated
}

/**
 * Check if user is admin
 * Simplified: any authenticated user is admin in admin-only system
 * @returns {boolean} True if user is authenticated
 */
export const isAdmin = () => {
  const authStore = useAuthStore()
  return authStore.isAuthenticated
}

/**
 * Require authentication for a function
 * @param {Function} fn - Function to wrap
 * @returns {Function} Wrapped function
 */
export const requireAuth = (fn) => {
  return async (...args) => {
    const authStore = useAuthStore()
    
    if (!authStore.isAuthenticated) {
      ElMessage.error('Authentication required')
      throw new Error('Authentication required')
    }
    
    return fn(...args)
  }
}

/**
 * Require specific permission for a function
 * @param {string} permission - Required permission
 * @param {Function} fn - Function to wrap
 * @returns {Function} Wrapped function
 */
export const requirePermission = (permission, fn) => {
  return async (...args) => {
    const authStore = useAuthStore()
    
    if (!authStore.isAuthenticated) {
      ElMessage.error('Authentication required')
      throw new Error('Authentication required')
    }
    
    if (!authStore.hasPermission(permission)) {
      ElMessage.error(`Permission required: ${permission}`)
      throw new Error(`Permission required: ${permission}`)
    }
    
    return fn(...args)
  }
}

/**
 * Require specific role for a function
 * Simplified: only requires authentication in admin-only system
 * @param {string} role - Required role (ignored)
 * @param {Function} fn - Function to wrap
 * @returns {Function} Wrapped function
 */
export const requireRole = (_, fn) => {
  return async (...args) => {
    const authStore = useAuthStore()

    if (!authStore.isAuthenticated) {
      ElMessage.error('Authentication required')
      throw new Error('Authentication required')
    }

    // All authenticated users have all roles in admin-only system
    return fn(...args)
  }
}

/**
 * Require admin access for a function
 * Simplified: only requires authentication in admin-only system
 * @param {Function} fn - Function to wrap
 * @returns {Function} Wrapped function
 */
export const requireAdmin = (fn) => {
  return async (...args) => {
    const authStore = useAuthStore()

    if (!authStore.isAuthenticated) {
      ElMessage.error('Authentication required')
      throw new Error('Authentication required')
    }

    // All authenticated users are admins in admin-only system
    return fn(...args)
  }
}

/**
 * Handle authentication errors
 * @param {Error} error - Authentication error
 * @param {Object} options - Error handling options
 */
export const handleAuthError = (error, options = {}) => {
  const { showNotification = true, redirectToLogin = true } = options
  
  console.error('Authentication error:', error)
  
  if (showNotification) {
    ElNotification({
      title: 'Authentication Error',
      message: error.message || 'Authentication failed',
      type: 'error',
      duration: 4000
    })
  }
  
  if (redirectToLogin) {
    const authStore = useAuthStore()
    authStore.logout()
    
    // Redirect to login (this should be handled by router guards)
    if (window.location.pathname !== '/login') {
      window.location.href = '/login'
    }
  }
}

/**
 * Auto-refresh token before expiry
 * @param {number} bufferMinutes - Minutes before expiry to refresh (default: 5)
 */
export const setupTokenAutoRefresh = (bufferMinutes = 5) => {
  const authStore = useAuthStore()
  
  const checkAndRefresh = async () => {
    if (!authStore.isAuthenticated || !authStore.refreshToken) {
      return
    }
    
    if (authStore.tokenExpiry) {
      const expiryTime = new Date(authStore.tokenExpiry)
      const now = new Date()
      const timeUntilExpiry = expiryTime - now
      const bufferTime = bufferMinutes * 60 * 1000 // Convert to milliseconds
      
      if (timeUntilExpiry <= bufferTime && timeUntilExpiry > 0) {
        try {
          console.log('Auto-refreshing token...')
          const result = await authStore.refreshAuthToken()
          
          if (result.success) {
            console.log('Token auto-refresh successful')
          } else {
            console.warn('Token auto-refresh failed:', result.error)
            handleAuthError(new Error(result.error), { redirectToLogin: true })
          }
        } catch (error) {
          console.error('Token auto-refresh error:', error)
          handleAuthError(error, { redirectToLogin: true })
        }
      }
    }
  }
  
  // Check every minute
  const interval = setInterval(checkAndRefresh, 60 * 1000)
  
  // Initial check
  checkAndRefresh()
  
  // Return cleanup function
  return () => clearInterval(interval)
}

/**
 * Initialize authentication system
 * @returns {Promise<boolean>} True if initialization successful
 */
export const initializeAuth = async () => {
  try {
    const authStore = useAuthStore()
    
    console.log('🔐 Initializing authentication system...')
    
    // Initialize auth store
    const result = await authStore.initializeAuth()
    
    if (result) {
      console.log('✅ Authentication initialized successfully')
      
      // Setup auto-refresh
      setupTokenAutoRefresh()
      
      return true
    } else {
      console.log('ℹ️ No existing authentication found')
      return false
    }
  } catch (error) {
    console.error('❌ Authentication initialization failed:', error)
    handleAuthError(error, { showNotification: false, redirectToLogin: false })
    return false
  }
}

/**
 * Logout and cleanup
 * @returns {Promise<void>}
 */
export const performLogout = async () => {
  try {
    const authStore = useAuthStore()
    
    console.log('🚪 Logging out...')
    
    await authStore.logout()
    
    console.log('✅ Logout successful')
    
    // Redirect to login page
    if (window.location.pathname !== '/login') {
      window.location.href = '/login'
    }
  } catch (error) {
    console.error('❌ Logout error:', error)
    
    // Force clear auth data even if API call fails
    const authStore = useAuthStore()
    authStore.clearAuthData()
    
    // Still redirect to login
    if (window.location.pathname !== '/login') {
      window.location.href = '/login'
    }
  }
}

/**
 * Check authentication status
 * @returns {Object} Authentication status
 */
export const getAuthStatus = () => {
  const authStore = useAuthStore()
  
  return {
    isAuthenticated: authStore.isAuthenticated,
    isAdmin: authStore.isAdmin,
    user: authStore.user,
    permissions: authStore.permissions,
    roles: authStore.roles,
    tokenExpiry: authStore.tokenExpiry,
    isTokenExpired: authStore.isTokenExpired
  }
}
