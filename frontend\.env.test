# Test Environment Configuration
# Used for automated testing and CI/CD pipelines

# API Configuration (use mock APIs for testing)
VITE_API_BASE_URL=/api/v1

# Mock API Configuration (enabled for testing)
VITE_MOCK_API=true
VITE_MOCK_DELAY=100

# Application Configuration
VITE_APP_TITLE=Community Services Admin (Test)
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=test

# Debug Configuration (minimal logging for tests)
VITE_DEBUG=false
VITE_LOG_LEVEL=error

# Feature Flags (all features enabled for comprehensive testing)
VITE_FEATURE_ANALYTICS=true
VITE_FEATURE_COMPETITIONS=true
VITE_FEATURE_CREDITS=true
VITE_FEATURE_SCHOOLS=true

# Performance Configuration (faster timeouts for tests)
VITE_API_TIMEOUT=5000
VITE_RETRY_ATTEMPTS=1

# Test Configuration
VITE_TEST_MODE=true
VITE_SKIP_AUTH=false
VITE_MOCK_DELAY_VARIANCE=50

# Security Configuration (relaxed for testing)
VITE_SECURE_COOKIES=false
VITE_CSRF_PROTECTION=false

# Analytics Configuration (disabled for testing)
VITE_ENABLE_ANALYTICS=false
VITE_ANALYTICS_DEBUG=false

# Error Reporting (disabled for testing)
VITE_ERROR_REPORTING=false
