# -*- coding:utf-8 -*-
import re
from typing import Dict, <PERSON>, <PERSON><PERSON>, List
from tortoise import Tortoise
import logging

logger = logging.getLogger(__name__)


def add_extra_braces(sql: str) -> str:
    """Ensures that single braces are doubled for .format() compatibility if used,
    though current replacement is regex-based. Primarily for f-string like templates.
    """
    # This function might need adjustment if the template format is strictly {{placeholder}}
    # The original regex r"({[^{}]*})(?!})" aimed to double single braces.
    # If templates are always {{placeholder}}, this might not be strictly needed for re.sub.
    # For safety, keeping a similar logic if {{ }} is the standard.
    # Regex to find single curly braces not part of a {{ or }}
    # This is complex; simpler approach if {{var}} is the only format:
    # just ensure the re.sub pattern is specific like r"{{\s*" + placeholder + r"\s*}}"
    return sql  # Original logic was: re.sub(r"({[^{}]*})(?!})", r"{\1}", sql)


def escape_backslashes(text: str) -> str:
    """Escapes backslashes in text to be inserted into SQL, primarily for string literals."""
    # This is a simplified version. Be cautious with SQL injection.
    # Proper parameterized queries or ORM methods are safer than string formatting for SQL.
    # However, this is for replacing placeholders in a pre-defined SQL template.
    return text.replace("\\", "\\\\").replace(
        "'", "''"
    )  # Basic escaping for SQL strings


def process_sql_template(template_path: str, replacements: Dict[str, Any]) -> str:
    """Processes an SQL template file by replacing placeholders with provided values."""
    try:
        with open(template_path, "r", encoding="utf-8") as file:
            # sql_template = add_extra_braces(file.read()) # Original had add_extra_braces here
            sql_template = file.read()
    except FileNotFoundError:
        logger.error(f"SQL template file not found: {template_path}")
        raise
    except Exception as e:
        logger.error(f"Error reading SQL template {template_path}: {e}")
        raise

    # replacements_escaped = {k: escape_backslashes(str(v)) for k, v in replacements.items()}
    # Current summer_camp curd/func.py does not show replacements_escaped being used before re.sub
    # It applies escape_backslashes inside a loop in its version of process_sql_template
    # Replicating that structure below for now.

    for placeholder, value in replacements.items():
        # value_escaped = escape_backslashes(str(value)) # Original did not escape here, but in CURD directly
        # The curd/func.py from summer_camp applies its own split and type conversion logic,
        # and doesn't show general escaping at the process_sql_template level for all values.
        # It seems like the values are often numbers or lists that are then joined into strings.
        # Sticking to direct replacement based on observed curd/func.py usage.
        try:
            # Ensure placeholder is treated as a literal string in the regex
            # The original code from summer_camp/utils.py has placeholder directly in regex string
            # This might be problematic if placeholder contains regex special characters.
            # A safer way for {{placeholder}} would be: re.sub(r"{{\s*" + re.escape(placeholder) + r"\s*}}", str(value), sql_template)
            # Assuming placeholders are simple and don't need re.escape for now, matching original.
            sql_template = re.sub(
                r"{{\s*" + placeholder + r"\s*}}", str(value), sql_template
            )
        except Exception as e:
            logger.error(
                f"Error replacing placeholder {placeholder} in SQL template: {e}"
            )
            # Decide if to raise, or continue, or log and skip
            pass  # Original was pass

    return sql_template


async def execute_query(
    sql: str, connection_name: str = "default"
) -> Tuple[int, List[Dict[str, Any]]]:
    """Executes a raw SQL query and returns row count and results."""
    conn = Tortoise.get_connection(connection_name)
    try:
        # The result type of execute_query_dict depends on the DB driver.
        # It's typically List[Dict[str, Any]].
        # The row_count might not be directly available or accurate for all SELECT queries.
        # For 영향 받은 행 수 (affected_rows), it's usually for INSERT, UPDATE, DELETE.
        # We'll assume the first element of the tuple is for affected_rows or similar, and second is results.
        # The original function from summer_camp used to return `_, raw_data` where `_` was unused.
        # Tortoise's `execute_query_dict` returns `(int, list)`. The int is rowcount.
        logger.debug(f"Executing SQL: {sql[:200]}...")
        row_count, results = await conn.execute_query(sql)
        logger.debug(
            f"Executed SQL: {sql[:200]}... Result count: {len(results)}, DB row_count: {row_count}"
        )
        return row_count, results
    except Exception as e:
        logger.error(
            f"Error executing SQL query: {sql[:200]}... Error: {e}", exc_info=True
        )
        # Depending on policy, you might want to return (0, []) or re-raise
        raise
