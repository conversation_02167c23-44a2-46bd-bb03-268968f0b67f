"""
Integration Schemas

Pydantic models for integration API requests and responses.
"""

from enum import Enum
from pydantic import BaseModel, Field, HttpUrl, field_validator
from typing import Optional, Dict, Any, List
from datetime import datetime
from libs.schemas.api.base import ExternalServiceResponse

# ———— HeyWhale Schemas ————

class TagType(int,Enum):
    """标签类型"""
    USER = 1
    ORGANIZATION = 2
    
# 获取标签列表

class TagResponse(BaseModel):
    """标签响应"""
    tag_id: int = Field(..., description="标签ID",alias='_id')
    tag_type: TagType = Field(..., description="标签类型",alias="Type")
    tag_name: str = Field(..., description="标签名称",alias="Name")
    children: List["TagResponse"] = Field(..., description="子标签")

class GetTagListResponse(ExternalServiceResponse):
    """获取标签列表"""
    tags: List[TagResponse] = Field(..., description="标签列表")


# 打标签
class TagRequest(BaseModel):
    """标签请求"""
    tag_id: int = Field(..., description="标签ID",alias="tagId")
    update_ids: List[str] = Field(..., description="更新ID列表",alias="updateIds")

class PostTagRequest(BaseModel):
    """打标签请求"""
    tags: List[TagRequest] = Field(..., description="标签列表")

class PostTagResponse(ExternalServiceResponse):
    """打标签响应"""
    invalid_data:List[TagRequest] = Field(..., description="无效数据",alias="invalidData")
    
# 徽章发放

class BadgeSyncRequest(BaseModel):
    """徽章发放请求"""
    badge_id:str = Field(..., description="徽章ID",alias='badgeOid')
    count:int | str = Field(..., description="发放数量",alias='count')
    level:int = Field(..., description="等级",alias='level')
    reason:str = Field(..., description="原因",alias='reason')
    user_id:str = Field(..., description="用户ID",alias='userOid')
    
    @field_validator('count')
    def validate_count(cls, v):
        if isinstance(v, str):
            return int(v)
        return v

class BadgeSyncBatchRequest(BaseModel):
    """徽章发放请求"""
    data: List[BadgeSyncRequest] = Field(..., description="数据")

class BadgeSyncResponse(ExternalServiceResponse):
    """徽章发放响应"""
    



# ——— File Processing Schemas ———


class FileProcessingRequest(BaseModel):
    """Request model for file processing."""

    processing_type: str = Field(..., description="Type of processing to perform")
    parameters: Optional[Dict[str, Any]] = Field(
        None, description="Processing parameters"
    )
    callback_url: Optional[HttpUrl] = Field(
        None, description="Callback URL for completion notification"
    )


class FileUploadResponse(BaseModel):
    """Response model for file upload."""

    success: bool = Field(..., description="Upload success status")
    file_id: str = Field(..., description="Unique file identifier")
    file_name: str = Field(..., description="Original file name")
    file_size: int = Field(..., description="File size in bytes")
    upload_url: Optional[str] = Field(None, description="Upload URL if applicable")
    status: str = Field(..., description="Upload status")
    message: str = Field(..., description="Status message")


# ——— Webhook Schemas ———


class WebhookRequest(BaseModel):
    """Request model for webhook execution."""

    url: HttpUrl = Field(..., description="Webhook URL")
    payload: Dict[str, Any] = Field(..., description="Webhook payload")
    headers: Optional[Dict[str, str]] = Field(None, description="Additional headers")
    method: str = Field("POST", description="HTTP method")
    retry_count: Optional[int] = Field(0, description="Number of retries")


class WebhookResponse(BaseModel):
    """Response model for webhook execution."""

    success: bool = Field(..., description="Execution success status")
    webhook_id: str = Field(..., description="Webhook execution ID")
    status_code: Optional[int] = Field(None, description="HTTP response status code")
    response_body: Optional[str] = Field(None, description="Response body")
    execution_time_ms: int = Field(..., description="Execution time in milliseconds")
    retry_count: int = Field(..., description="Number of retries performed")

