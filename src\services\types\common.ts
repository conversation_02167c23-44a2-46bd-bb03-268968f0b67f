/**
 * Common TypeScript interfaces for API responses and shared data structures
 */

// Base API Response Structure
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: ApiError
}

export interface ApiError {
  code: string
  message: string
  details?: Record<string, any>
}

// Pagination
export interface PaginationParams {
  page?: number
  limit?: number
  sort?: string
  order?: 'asc' | 'desc'
}

export interface PaginationMeta {
  page: number
  limit: number
  total: number
  pages: number
  hasNext: boolean
  hasPrev: boolean
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: PaginationMeta
}

// Common Entity Fields
export interface BaseEntity {
  id: string | number
  createdAt: string
  updatedAt: string
}

// User-related types
export interface User extends BaseEntity {
  username: string
  email: string
  name: string
  avatar?: string
  role: UserRole
  status: UserStatus
  lastLoginAt?: string
}

export type UserRole = 'admin' | 'manager' | 'operator' | 'viewer'
export type UserStatus = 'active' | 'inactive' | 'suspended'

// File upload types
export interface FileUpload {
  id: string
  filename: string
  originalName: string
  mimeType: string
  size: number
  url: string
  uploadedAt: string
  uploadedBy: string
}

// Search and filter types
export interface SearchParams {
  query?: string
  filters?: Record<string, any>
  dateRange?: {
    start: string
    end: string
  }
}

// Status types
export type Status = 'active' | 'inactive' | 'pending' | 'completed' | 'cancelled'

// Statistics types
export interface StatisticItem {
  label: string
  value: number
  change?: number
  changeType?: 'increase' | 'decrease' | 'neutral'
  format?: 'number' | 'percentage' | 'currency'
}

export interface ChartDataPoint {
  label: string
  value: number
  color?: string
}

export interface TimeSeriesData {
  date: string
  value: number
  label?: string
}

// Form validation types
export interface ValidationRule {
  required?: boolean
  min?: number
  max?: number
  pattern?: RegExp
  message?: string
  validator?: (value: any) => boolean | string
}

export interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea' | 'date' | 'file'
  rules?: ValidationRule[]
  options?: Array<{ label: string; value: any }>
  placeholder?: string
  disabled?: boolean
}

// Table column types
export interface TableColumn {
  key: string
  label: string
  sortable?: boolean
  filterable?: boolean
  width?: string | number
  align?: 'left' | 'center' | 'right'
  formatter?: (value: any, row: any) => string
  component?: string
}

// Action types
export interface Action {
  key: string
  label: string
  icon?: string
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  disabled?: boolean
  handler: (item: any) => void
}

// Export types
export interface ExportOptions {
  format: 'csv' | 'excel' | 'pdf'
  filename?: string
  columns?: string[]
  filters?: Record<string, any>
}

// Notification types
export interface Notification {
  id: string
  type: 'success' | 'warning' | 'error' | 'info'
  title: string
  message: string
  timestamp: string
  read: boolean
  actions?: Array<{
    label: string
    handler: () => void
  }>
}
