import { describe, it, expect, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'
import App from './App.vue'

describe('App.vue', () => {
  let wrapper
  let router
  let pinia

  beforeEach(async () => {
    // Create router instance
    router = createRouter({
      history: createWebHistory(),
      routes: [
        { path: '/', name: 'home', component: { template: '<div>Home</div>' } },
        { path: '/login', name: 'login', component: { template: '<div>Login</div>' } }
      ]
    })

    // Create pinia instance
    pinia = createPinia()

    // Mount component
    wrapper = mount(App, {
      global: {
        plugins: [router, pinia]
      }
    })

    await router.isReady()
  })

  it('renders without crashing', () => {
    expect(wrapper.exists()).toBe(true)
  })

  it('has router-view component', () => {
    expect(wrapper.html()).toContain('router-view')
  })

  it('applies correct CSS classes', () => {
    expect(wrapper.classes()).toContain('app')
  })

  it('has proper HTML structure', () => {
    expect(wrapper.element.tagName).toBe('DIV')
    expect(wrapper.attributes('id')).toBe('app')
  })
})
