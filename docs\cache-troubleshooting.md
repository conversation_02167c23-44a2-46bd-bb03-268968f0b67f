# Cache Troubleshooting Guide

This guide helps diagnose and resolve common issues with the Redis-based caching system.

## Quick Diagnostics

### 1. Check Cache Status

```python
from core.cache.utils import is_cache_enabled
from core.database.dependencies import check_redis_health

# Check if caching is enabled
print(f"Cache enabled: {is_cache_enabled()}")

# Check Redis health
health = await check_redis_health()
print(f"Redis status: {health['status']}")
if health['status'] == 'healthy':
    print(f"Latency: {health['latency_ms']}ms")
else:
    print(f"Error: {health['error']}")
```

### 2. Test Basic Operations

```python
from core.cache.client import get_cache_client

async def test_cache_operations():
    cache_client = await get_cache_client()
    
    # Test set/get
    await cache_client.set("test_key", "test_value", ttl=60)
    value = await cache_client.get("test_key")
    print(f"Set/Get test: {'PASS' if value == 'test_value' else 'FAIL'}")
    
    # Test delete
    deleted = await cache_client.delete("test_key")
    print(f"Delete test: {'PASS' if deleted else 'FAIL'}")
    
    # Test exists
    exists = await cache_client.exists("test_key")
    print(f"Exists test: {'PASS' if not exists else 'FAIL'}")
```

## Common Issues and Solutions

### Issue 1: Cache Not Working

**Symptoms:**
- Cache decorators have no effect
- All requests hit the database
- No cache keys in Redis

**Diagnosis:**

```python
# Check if caching is globally enabled
from core.config.settings import get_settings
settings = get_settings()
print(f"Cache enabled: {settings.cache_enabled}")

# Check environment variable
import os
print(f"CACHE_ENABLED env var: {os.getenv('CACHE_ENABLED')}")
```

**Solutions:**

1. **Enable caching in environment:**
   ```bash
   # In .env file
   CACHE_ENABLED=true
   ```

2. **Check decorator usage:**
   ```python
   # Correct usage
   @cache_response(domain="users", operation="profile")
   async def get_user_profile(request: Request, user_id: str):
       pass
   
   # Incorrect - missing Request parameter
   @cache_response(domain="users", operation="profile")
   async def get_user_profile(user_id: str):  # Missing Request
       pass
   ```

3. **Verify cache key generation:**
   ```python
   from core.cache.utils import generate_cache_key
   
   key = generate_cache_key("users", "profile", {"user_id": "123"})
   print(f"Generated key: {key}")
   ```

### Issue 2: Redis Connection Failed

**Symptoms:**
- `ConnectionError` or `RedisConnectionError`
- Health check shows "unhealthy"
- Application logs show Redis connection errors

**Diagnosis:**

```bash
# Test Redis connectivity
redis-cli -h $REDIS_HOST -p $REDIS_PORT ping

# Check if Redis is running
ps aux | grep redis
# or
systemctl status redis

# Test network connectivity
telnet $REDIS_HOST $REDIS_PORT
```

**Solutions:**

1. **Start Redis server:**
   ```bash
   # Ubuntu/Debian
   sudo systemctl start redis-server
   
   # macOS with Homebrew
   brew services start redis
   
   # Docker
   docker run -d -p 6379:6379 redis:7-alpine
   ```

2. **Check configuration:**
   ```bash
   # Verify environment variables
   echo $REDIS_HOST
   echo $REDIS_PORT
   echo $REDIS_PASSWORD
   ```

3. **Test connection manually:**
   ```python
   import aioredis
   
   async def test_connection():
       try:
           redis = aioredis.from_url("redis://localhost:6379")
           await redis.ping()
           print("Connection successful")
           await redis.close()
       except Exception as e:
           print(f"Connection failed: {e}")
   ```

### Issue 3: Authentication Failed

**Symptoms:**
- `AuthenticationError` or `NOAUTH` errors
- Redis logs show authentication failures

**Diagnosis:**

```bash
# Test authentication
redis-cli -h $REDIS_HOST -p $REDIS_PORT -a $REDIS_PASSWORD ping

# Check Redis configuration
redis-cli CONFIG GET requirepass
```

**Solutions:**

1. **Set correct password:**
   ```bash
   # In .env file
   REDIS_PASSWORD=your_actual_password
   ```

2. **Configure Redis authentication:**
   ```bash
   # In redis.conf
   requirepass your_password
   
   # Restart Redis
   sudo systemctl restart redis-server
   ```

3. **Use Redis URL format:**
   ```bash
   REDIS_URL=redis://:password@localhost:6379/0
   ```

### Issue 4: High Cache Miss Rate

**Symptoms:**
- Cache hit rate below expected levels
- Performance not improving with caching
- Frequent database queries despite caching

**Diagnosis:**

```python
from core.database.dependencies import get_cache_manager

async def check_cache_stats():
    cache_manager = await get_cache_manager()
    stats = await cache_manager.get_stats()
    
    print(f"Hit rate: {stats['hit_rate']:.2%}")
    print(f"Total hits: {stats['hits']}")
    print(f"Total misses: {stats['misses']}")
    print(f"Total operations: {stats['hits'] + stats['misses']}")
```

**Solutions:**

1. **Check TTL values:**
   ```python
   # TTL might be too short
   from core.cache.utils import get_cache_ttl
   
   print(f"Profile TTL: {get_cache_ttl('profile')} seconds")
   print(f"Search TTL: {get_cache_ttl('search')} seconds")
   ```

2. **Verify cache key consistency:**
   ```python
   # Ensure parameters are consistent
   @cache_response(
       domain="users",
       operation="search",
       key_from_request=lambda req: {
           "query": req.query_params.get("query", "").lower(),  # Normalize
           "limit": req.query_params.get("limit", "10"),
           "sort": sorted(req.query_params.getlist("sort"))     # Sort lists
       }
   )
   ```

3. **Increase TTL for stable data:**
   ```bash
   # In .env file
   CACHE_USER_PROFILE_TTL=1800  # 30 minutes instead of 10
   CACHE_HEYWHALE_TAGS_TTL=7200  # 2 hours instead of 1
   ```

### Issue 5: Memory Issues

**Symptoms:**
- Redis running out of memory
- Cache eviction happening too frequently
- `OOM` errors in Redis logs

**Diagnosis:**

```bash
# Check Redis memory usage
redis-cli info memory

# Monitor memory in real-time
redis-cli --latency-history -i 1

# Check memory configuration
redis-cli CONFIG GET maxmemory
redis-cli CONFIG GET maxmemory-policy
```

**Solutions:**

1. **Increase Redis memory limit:**
   ```bash
   # In redis.conf
   maxmemory 2gb
   
   # Or via command line
   redis-cli CONFIG SET maxmemory 2gb
   ```

2. **Configure eviction policy:**
   ```bash
   # Evict least recently used keys
   redis-cli CONFIG SET maxmemory-policy allkeys-lru
   
   # Or evict keys with TTL
   redis-cli CONFIG SET maxmemory-policy volatile-lru
   ```

3. **Optimize cache usage:**
   ```python
   # Reduce TTL for large objects
   @cache_response(domain="analytics", operation="large_report", ttl=300)  # 5 minutes
   
   # Clear old cache patterns
   cache_manager = await get_cache_manager()
   await cache_manager.clear_pattern("old_domain:*")
   ```

### Issue 6: Slow Cache Operations

**Symptoms:**
- High cache operation latency
- Timeouts on cache operations
- Application performance degradation

**Diagnosis:**

```bash
# Monitor Redis latency
redis-cli --latency -i 1

# Check slow queries
redis-cli SLOWLOG GET 10

# Monitor Redis stats
redis-cli --stat
```

**Solutions:**

1. **Optimize connection pool:**
   ```bash
   # Increase connection pool size
   REDIS_MAX_CONNECTIONS=50
   REDIS_SOCKET_TIMEOUT=10
   ```

2. **Use pipeline for bulk operations:**
   ```python
   cache_manager = await get_cache_manager()
   
   # Instead of individual operations
   data = {"key1": "value1", "key2": "value2", "key3": "value3"}
   await cache_manager.set_many(data, ttl=600)  # Bulk operation
   ```

3. **Optimize data serialization:**
   ```python
   # Avoid caching very large objects
   @cached(
       domain="analytics",
       operation="report",
       cache_condition=lambda result: len(str(result)) < 1000000  # 1MB limit
   )
   async def generate_report():
       pass
   ```

### Issue 7: Cache Invalidation Not Working

**Symptoms:**
- Stale data returned from cache
- Updates not reflected in cached responses
- Cache not cleared after data modifications

**Diagnosis:**

```python
# Check if invalidation decorators are applied
@invalidate_cache(domain="users", operation="profile")
async def update_user_profile(user_id: str, updates: dict):
    pass

# Test manual invalidation
from core.cache.utils import generate_pattern_key
pattern = generate_pattern_key("users", "profile")
print(f"Invalidation pattern: {pattern}")

cache_manager = await get_cache_manager()
cleared = await cache_manager.clear_pattern(pattern)
print(f"Cleared {cleared} cache entries")
```

**Solutions:**

1. **Add invalidation decorators:**
   ```python
   @router.post("/users/{user_id}/profile")
   @invalidate_cache(domain="users", operation="profile")  # Add this
   async def update_user_profile(user_id: str, updates: dict):
       # Update logic
       pass
   ```

2. **Use pattern-based invalidation:**
   ```python
   @invalidate_cache(
       domain="users",
       pattern="users:search:*"  # Clear all search caches
   )
   async def bulk_update_users(updates: list):
       pass
   ```

3. **Manual cache clearing:**
   ```python
   # Clear specific cache entries
   cache_manager = await get_cache_manager()
   await cache_manager.clear_pattern("users:profile:123:*")
   ```

## Debug Tools

### 1. Cache Inspector

```python
async def inspect_cache(pattern: str = "*"):
    """Inspect cache contents matching pattern."""
    from core.cache.client import get_cache_client
    
    cache_client = await get_cache_client()
    
    # Get all keys matching pattern
    keys = []
    async for key in cache_client._redis.scan_iter(match=pattern):
        keys.append(key)
    
    print(f"Found {len(keys)} cache keys:")
    for key in keys[:10]:  # Show first 10
        value = await cache_client.get(key)
        print(f"  {key}: {str(value)[:100]}...")
    
    if len(keys) > 10:
        print(f"  ... and {len(keys) - 10} more")

# Usage
await inspect_cache("community_services:users:*")
```

### 2. Cache Performance Monitor

```python
import time
from contextlib import asynccontextmanager

@asynccontextmanager
async def cache_timer(operation: str):
    """Monitor cache operation performance."""
    start_time = time.time()
    try:
        yield
    finally:
        duration = (time.time() - start_time) * 1000
        print(f"Cache {operation} took {duration:.2f}ms")

# Usage
async with cache_timer("get"):
    value = await cache_client.get("some_key")
```

### 3. Cache Statistics Dashboard

```python
async def cache_dashboard():
    """Display comprehensive cache statistics."""
    from core.database.dependencies import get_cache_manager, check_redis_health
    
    # Health check
    health = await check_redis_health()
    print(f"Redis Health: {health['status']}")
    if health['status'] == 'healthy':
        print(f"  Latency: {health['latency_ms']}ms")
    
    # Cache statistics
    cache_manager = await get_cache_manager()
    stats = await cache_manager.get_stats()
    
    print(f"\nCache Statistics:")
    print(f"  Hit Rate: {stats['hit_rate']:.2%}")
    print(f"  Total Hits: {stats['hits']:,}")
    print(f"  Total Misses: {stats['misses']:,}")
    print(f"  Total Operations: {stats['hits'] + stats['misses']:,}")
    
    # Memory usage (if available)
    if 'memory_usage' in stats:
        print(f"  Memory Usage: {stats['memory_usage']}")
    
    # Connection info
    if 'connections' in stats:
        print(f"  Active Connections: {stats['connections']}")

# Run dashboard
await cache_dashboard()
```

## Logging and Monitoring

### Enable Debug Logging

```python
import logging

# Enable debug logging for cache operations
logging.getLogger("core.cache").setLevel(logging.DEBUG)

# Enable Redis client logging
logging.getLogger("aioredis").setLevel(logging.DEBUG)
```

### Custom Cache Metrics

```python
import time
from functools import wraps

def cache_metrics(func):
    """Decorator to collect cache operation metrics."""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time
            print(f"Cache operation {func.__name__} succeeded in {duration:.3f}s")
            return result
        except Exception as e:
            duration = time.time() - start_time
            print(f"Cache operation {func.__name__} failed in {duration:.3f}s: {e}")
            raise
    return wrapper

# Apply to cache operations
@cache_metrics
async def monitored_cache_get(key: str):
    cache_client = await get_cache_client()
    return await cache_client.get(key)
```

## Prevention Best Practices

1. **Always use try-catch for cache operations**
2. **Set reasonable TTL values based on data change frequency**
3. **Monitor cache hit rates and adjust strategies accordingly**
4. **Use pattern-based cache invalidation for related data**
5. **Implement cache warming for critical data**
6. **Regular monitoring of Redis memory and performance**
7. **Test cache behavior in staging environment**
8. **Document cache strategies for each domain**

This troubleshooting guide should help you quickly identify and resolve most caching-related issues. For complex problems, consider enabling debug logging and using the provided diagnostic tools. 