# Staging Environment Configuration
# Used for pre-production testing and validation

# API Configuration
VITE_API_BASE_URL=http://staging-community-workers:8000/api/v1

# Mock API Configuration (disabled in staging)
VITE_MOCK_API=false
VITE_MOCK_DELAY=0

# Application Configuration
VITE_APP_TITLE=Community Services Admin (Staging)
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=staging

# Debug Configuration (limited debugging in staging)
VITE_DEBUG=true
VITE_LOG_LEVEL=warn

# Feature Flags
VITE_FEATURE_ANALYTICS=true
VITE_FEATURE_COMPETITIONS=true
VITE_FEATURE_CREDITS=true
VITE_FEATURE_SCHOOLS=true

# Performance Configuration
VITE_API_TIMEOUT=20000
VITE_RETRY_ATTEMPTS=2

# Security Configuration
VITE_SECURE_COOKIES=true
VITE_CSRF_PROTECTION=true

# Analytics Configuration
VITE_ENABLE_ANALYTICS=true
VITE_ANALYTICS_DEBUG=true

# Error Reporting
VITE_ERROR_REPORTING=true
VITE_ERROR_REPORTING_URL=http://staging-error-service:8003/errors
