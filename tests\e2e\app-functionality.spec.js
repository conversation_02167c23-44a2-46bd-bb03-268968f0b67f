/**
 * End-to-end tests for the Vue 3 frontend application
 * Testing the application running on localhost:3000
 */

import { test, expect } from '@playwright/test'

// Test configuration
const BASE_URL = 'http://localhost:3000'

test.describe('Vue 3 Frontend Application', () => {
  
  test('should load the home page successfully', async ({ page }) => {
    await page.goto(BASE_URL)
    
    // Check if the page loads without errors
    await expect(page).toHaveTitle(/Community Services/i)
    
    // Check for Vue app mounting
    await expect(page.locator('#app')).toBeVisible()
    
    // Take a screenshot for visual verification
    await page.screenshot({ path: 'tests/screenshots/homepage.png' })
  })

  test('should have working navigation', async ({ page }) => {
    await page.goto(BASE_URL)
    
    // Wait for the app to load
    await page.waitForSelector('#app')
    
    // Check if navigation elements are present
    const nav = page.locator('nav, .el-menu, .sidebar')
    if (await nav.count() > 0) {
      await expect(nav.first()).toBeVisible()
    }
    
    // Look for common navigation items
    const navItems = [
      'Dashboard',
      'Competition',
      'Credit',
      'School',
      'Home'
    ]
    
    for (const item of navItems) {
      const navItem = page.getByText(item, { exact: false })
      if (await navItem.count() > 0) {
        console.log(`Found navigation item: ${item}`)
      }
    }
  })

  test('should handle authentication pages', async ({ page }) => {
    // Test login page
    await page.goto(`${BASE_URL}/login`)
    
    // Check if login form elements are present
    const loginForm = page.locator('form, .login-form, .el-form')
    if (await loginForm.count() > 0) {
      await expect(loginForm.first()).toBeVisible()
      
      // Look for common form elements
      const emailInput = page.locator('input[type="email"], input[placeholder*="email" i]')
      const passwordInput = page.locator('input[type="password"]')
      const submitButton = page.locator('button[type="submit"], .el-button--primary')
      
      if (await emailInput.count() > 0) {
        await expect(emailInput.first()).toBeVisible()
      }
      if (await passwordInput.count() > 0) {
        await expect(passwordInput.first()).toBeVisible()
      }
      if (await submitButton.count() > 0) {
        await expect(submitButton.first()).toBeVisible()
      }
    }
    
    await page.screenshot({ path: 'tests/screenshots/login-page.png' })
  })

  test('should load competition management page', async ({ page }) => {
    await page.goto(`${BASE_URL}/competitions`)
    
    // Wait for the page to load
    await page.waitForTimeout(2000)
    
    // Check for competition-related elements
    const competitionElements = [
      '.competition-list',
      '.data-table',
      '.stats-card',
      'h1, h2, h3'
    ]
    
    let foundElements = 0
    for (const selector of competitionElements) {
      const element = page.locator(selector)
      if (await element.count() > 0) {
        foundElements++
        console.log(`Found competition element: ${selector}`)
      }
    }
    
    // Check if we found any competition-related content
    expect(foundElements).toBeGreaterThan(0)
    
    await page.screenshot({ path: 'tests/screenshots/competitions-page.png' })
  })

  test('should have working Element Plus components', async ({ page }) => {
    await page.goto(BASE_URL)
    await page.waitForTimeout(2000)
    
    // Check for Element Plus components
    const elementPlusSelectors = [
      '.el-button',
      '.el-card',
      '.el-table',
      '.el-input',
      '.el-menu',
      '.el-header',
      '.el-main'
    ]
    
    let foundComponents = 0
    for (const selector of elementPlusSelectors) {
      const component = page.locator(selector)
      if (await component.count() > 0) {
        foundComponents++
        console.log(`Found Element Plus component: ${selector}`)
      }
    }
    
    // Should have at least some Element Plus components
    expect(foundComponents).toBeGreaterThan(0)
  })

  test('should handle API calls and data loading', async ({ page }) => {
    // Monitor network requests
    const apiCalls = []
    page.on('request', request => {
      if (request.url().includes('/api/') || request.url().includes('localhost:5000')) {
        apiCalls.push(request.url())
      }
    })
    
    await page.goto(`${BASE_URL}/competitions`)
    await page.waitForTimeout(3000)
    
    // Check if any API calls were made
    console.log('API calls detected:', apiCalls)
    
    // Look for loading states or data
    const loadingElements = page.locator('.el-loading, .loading, [loading]')
    const dataElements = page.locator('.el-table-row, .stats-card, .data-table')
    
    // Either should have loading states or data
    const hasLoading = await loadingElements.count() > 0
    const hasData = await dataElements.count() > 0
    
    console.log(`Has loading elements: ${hasLoading}`)
    console.log(`Has data elements: ${hasData}`)
    
    // The page should either show loading or data
    expect(hasLoading || hasData).toBeTruthy()
  })

  test('should be responsive on mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto(BASE_URL)
    await page.waitForTimeout(2000)
    
    // Check if the app is still functional on mobile
    await expect(page.locator('#app')).toBeVisible()
    
    // Take mobile screenshot
    await page.screenshot({ path: 'tests/screenshots/mobile-view.png' })
    
    // Check for mobile-responsive elements
    const responsiveElements = page.locator('.el-col, .mobile, [class*="mobile"]')
    if (await responsiveElements.count() > 0) {
      console.log('Found responsive elements')
    }
  })

  test('should handle errors gracefully', async ({ page }) => {
    // Test 404 page
    await page.goto(`${BASE_URL}/non-existent-page`)
    await page.waitForTimeout(2000)
    
    // Should either redirect or show error page
    const currentUrl = page.url()
    const pageContent = await page.textContent('body')
    
    console.log(`Current URL: ${currentUrl}`)
    console.log(`Page contains "404" or "Not Found": ${pageContent.includes('404') || pageContent.includes('Not Found')}`)
    
    await page.screenshot({ path: 'tests/screenshots/error-page.png' })
  })

  test('should have working search and filter functionality', async ({ page }) => {
    await page.goto(`${BASE_URL}/competitions`)
    await page.waitForTimeout(2000)
    
    // Look for search inputs
    const searchInput = page.locator('input[placeholder*="search" i], .search-input input')
    if (await searchInput.count() > 0) {
      await expect(searchInput.first()).toBeVisible()
      
      // Try typing in search
      await searchInput.first().fill('test')
      await page.waitForTimeout(1000)
      
      console.log('Search functionality appears to be working')
    }
    
    // Look for filter elements
    const filterElements = page.locator('.el-select, .filter, select')
    if (await filterElements.count() > 0) {
      console.log(`Found ${await filterElements.count()} filter elements`)
    }
    
    await page.screenshot({ path: 'tests/screenshots/search-filters.png' })
  })

  test('should display statistics and data correctly', async ({ page }) => {
    await page.goto(`${BASE_URL}/competitions`)
    await page.waitForTimeout(3000)
    
    // Look for statistics cards
    const statsCards = page.locator('.stats-card, .el-card')
    if (await statsCards.count() > 0) {
      console.log(`Found ${await statsCards.count()} statistics cards`)
      
      // Check if cards have content
      for (let i = 0; i < Math.min(3, await statsCards.count()); i++) {
        const card = statsCards.nth(i)
        const cardText = await card.textContent()
        if (cardText && cardText.trim().length > 0) {
          console.log(`Card ${i + 1} has content: ${cardText.substring(0, 50)}...`)
        }
      }
    }
    
    // Look for data tables
    const dataTables = page.locator('.el-table, .data-table, table')
    if (await dataTables.count() > 0) {
      console.log(`Found ${await dataTables.count()} data tables`)
    }
    
    await page.screenshot({ path: 'tests/screenshots/statistics-data.png' })
  })
})
