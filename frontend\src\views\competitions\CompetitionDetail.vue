<template>
  <div class="competition-qualifications">
    <!-- Loading State -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="6" animated />
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-container">
      <el-alert
        :title="error"
        type="error"
        show-icon
        :closable="false"
      />
      <el-button @click="loadData" type="primary" style="margin-top: 16px">
        Retry
      </el-button>
    </div>

    <!-- Competition Qualification Management -->
    <div v-else-if="competition">
      <!-- Header -->
      <div class="page-header">
        <el-button @click="$router.back()" type="default" class="back-button">
          <el-icon><ArrowLeft /></el-icon>
          Back to Competitions
        </el-button>

        <div class="header-content">
          <h1>{{ competition.title || competition.name }} - Qualifications</h1>
          <div class="header-meta">
            <el-tag :type="getStatusType(competition.status)" size="large">
              {{ competition.displayStatus || competition.status }}
            </el-tag>
            <span class="route-info">{{ competition.routeName || 'Unknown Route' }}</span>
          </div>
        </div>

        <div class="header-actions">
          <el-button @click="manageCredits" type="success">
            <el-icon><Coin /></el-icon>
            Manage Credits
          </el-button>
          <el-button @click="refreshData" :loading="refreshing">
            <el-icon><Refresh /></el-icon>
            Refresh
          </el-button>
        </div>
      </div>

      <!-- Competition Stats Cards -->
      <el-row :gutter="20" style="margin-bottom: 24px;">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon participants">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ formatNumber(competition.totalParticipants || 0) }}</div>
                <div class="stat-label">Participants</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon submissions">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ formatNumber(competition.totalSubmissions || 0) }}</div>
                <div class="stat-label">Submissions</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon teams">
                <el-icon><UserFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ formatNumber(competition.totalTeams || 0) }}</div>
                <div class="stat-label">Teams</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon qualifications">
                <el-icon><Trophy /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ qualifications.length || 0 }}</div>
                <div class="stat-label">Qualifications</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- Qualification Management -->
      <el-card>
        <template #header>
          <div class="card-header">
            <span>Qualification Management</span>
            <el-button type="primary" @click="createQualification">
              <el-icon><Plus /></el-icon>
              Add Qualification
            </el-button>
          </div>
        </template>

        <QualificationList
          :competition-id="competitionId"
          :qualifications="qualifications"
          :loading="qualificationsLoading"
          @refresh="loadQualifications"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft,
  Refresh,
  User,
  Document,
  Trophy,
  Coin,
  Plus,
  UserFilled
} from '@element-plus/icons-vue'
import { competitionService } from '@/services/competitionService'
import QualificationList from '@/components/competitions/QualificationList.vue'

const route = useRoute()
const router = useRouter()

// State
const loading = ref(true)
const refreshing = ref(false)
const qualificationsLoading = ref(false)
const error = ref(null)
const competition = ref(null)
const qualifications = ref([])

// Computed
const competitionId = computed(() => route.params.id)

// Methods
const formatNumber = (num) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const getStatusType = (status) => {
  const statusTypes = {
    'active': 'success',
    'upcoming': 'warning',
    'completed': 'info',
    'draft': 'info',
    'cancelled': 'danger'
  }
  return statusTypes[status] || 'info'
}

const loadCompetition = async () => {
  loading.value = true
  error.value = null

  try {
    const response = await competitionService.getCompetition(competitionId.value)

    if (response.success) {
      competition.value = response.data
    } else {
      throw new Error(response.error?.message || 'Failed to load competition')
    }
  } catch (err) {
    console.error('Failed to load competition:', err)
    error.value = err.message || 'Failed to load competition'
    ElMessage.error(error.value)
  } finally {
    loading.value = false
  }
}

const loadQualifications = async () => {
  qualificationsLoading.value = true

  try {
    const response = await competitionService.getQualifications(competitionId.value)

    if (response.success) {
      qualifications.value = response.data
    } else {
      throw new Error(response.error?.message || 'Failed to load qualifications')
    }
  } catch (err) {
    console.error('Failed to load qualifications:', err)
    ElMessage.error('Failed to load qualifications')
  } finally {
    qualificationsLoading.value = false
  }
}

const loadData = async () => {
  await Promise.all([
    loadCompetition(),
    loadQualifications()
  ])
}

const refreshData = async () => {
  refreshing.value = true
  await loadData()
  refreshing.value = false
  ElMessage.success('Data refreshed')
}

const createQualification = () => {
  router.push(`/competitions/${competitionId.value}/qualifications/create`)
}

const manageCredits = () => {
  router.push(`/credits?competition=${competitionId.value}`)
}

// Lifecycle
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.competition-qualifications {
  max-width: 1200px;
  margin: 0 auto;
}

.loading-container,
.error-container {
  padding: 40px;
  text-align: center;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;

  .back-button {
    flex-shrink: 0;
  }

  .header-content {
    flex: 1;

    h1 {
      font-size: 24px;
      font-weight: 600;
      color: var(--text-color);
      margin-bottom: 8px;
    }

    .header-meta {
      display: flex;
      align-items: center;
      gap: 16px;

      .route-info {
        color: var(--text-color-secondary);
        font-size: 14px;
      }
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;
    flex-shrink: 0;
  }
}

.stat-card {
  .stat-content {
    display: flex;
    align-items: center;

    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;

      .el-icon {
        font-size: 24px;
        color: white;
      }

      &.participants {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      &.submissions {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }

      &.teams {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
      }

      &.qualifications {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }
    }

    .stat-info {
      flex: 1;

      .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: var(--text-color);
        line-height: 1;
      }

      .stat-label {
        font-size: 14px;
        color: var(--text-color-secondary);
        margin-top: 4px;
      }
    }
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

// Responsive design
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;

    .header-actions {
      justify-content: center;
    }
  }
}
</style>
