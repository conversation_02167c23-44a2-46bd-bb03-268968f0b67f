import asyncio

# This is a simple way to track if the mock job ran for demonstration purposes.
# In more complex scenarios, you might use a mock object, a temporary file, or a database record.
mock_job_run_count = 0
last_run_times = []


async def simple_mock_job():
    """A simple mock job that increments a counter and records its run time."""
    global mock_job_run_count
    mock_job_run_count += 1
    current_time = asyncio.get_event_loop().time()
    last_run_times.append(current_time)
    print(
        f"Simple mock job executed at {current_time}. Total runs: {mock_job_run_count}"
    )


def reset_mock_job_state():
    """Resets the state of the mock job for clean tests."""
    global mock_job_run_count
    global last_run_times
    mock_job_run_count = 0
    last_run_times = []
