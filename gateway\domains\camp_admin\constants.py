"""
Constants for the competitions domain.

Contains competition-specific enums and constants including
qualification types and logic definitions.
"""

from enum import IntEnum


class QualificationType(IntEnum):
    """Types of qualification criteria for competitions."""

    SINGLE_TASK_SCORE = 1
    SINGLE_TASK_SUBMIT = 2


class QualificationLogic(IntEnum):
    """Logic operations for qualification score thresholds."""

    SCORE_GET_THRESHOLD = 1
    SCORE_GT_THRESHOLD = 2
    SCORE_LT_THRESHOLD = 3
    SCORE_LET_THRESHOLD = 4
    SCORE_BETWEEN_THRESHOLD = 5
    SCORE_NOT_ZERO = 6
    SCORE_IS_VALID = 7


__all__ = [
    "QualificationType",
    "QualificationLogic",
]
