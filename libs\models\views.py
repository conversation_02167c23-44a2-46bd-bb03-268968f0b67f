from libs.models.tables import StatisticsMixin, SchoolBasedMixin, UserBasedMixin, QualificationMixin
from bson import ObjectId
from tortoise import fields
from libs.models.base import NaiveDatetimeField

# 统计相关 - 学校
class SchoolStatistics(StatisticsMixin,SchoolBasedMixin):
    """
    School statistics model for analytics and reporting.
    
    Stores various statistical data points and metrics
    for competitions, users, and system performance.
    """
    record_id = fields.CharField(
        max_length=255, pk=True, description="统计 ID",default=str(ObjectId())
    )
    
    class Meta:
        table = "school_statistics"
        table_description = "School statistics"

# 统计相关 - 赛道
class RouteStatistics(StatisticsMixin):
    """
    Route statistics model for analytics and reporting.
    
    Stores various statistical data points and metrics
    for competitions, users, and system performance.
    """
    record_id = fields.CharField(
        max_length=255, pk=True, description="统计 ID",default=str(ObjectId())
    )
    route_id = fields.CharField(
        max_length=255, description="赛道 ID",db_index=True
    )
    route_name = fields.CharField(
        max_length=255, description="赛道名称",db_index=True
    )   
    class Meta:
        table = "route_statistics"
        table_description = "Route statistics"

# 统计相关 - 活动
class CompetitionStatistics(StatisticsMixin):
    """
    Competition statistics model for analytics and reporting.
    
    Stores various statistical data points and metrics
    for competitions, users, and system performance.
    """
    record_id = fields.CharField(
        max_length=255, pk=True, description="统计 ID",default=str(ObjectId())
    )
    competition_id = fields.CharField(
        max_length=255, description="活动 ID",db_index=True
    )
    competition_name = fields.CharField(
        max_length=255, description="活动名称",db_index=True
    )
    class Meta:
        table = "competition_statistics"
        table_description = "Competition statistics"

# 统计相关 - 用户
class UserStatistics(StatisticsMixin,UserBasedMixin):
    """
    User statistics model for analytics and reporting.
    
    Stores various statistical data points and metrics
    for competitions, users, and system performance.
    """ 
    record_id = fields.CharField(
        max_length=255, pk=True, description="统计 ID",default=str(ObjectId())
    )
    class Meta:
        table = "user_statistics"
        table_description = "User statistics"

