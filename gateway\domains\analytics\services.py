"""
Analytics business logic and data access services.

Contains all business logic for analytics including data access,
ranking calculations, and statistical analysis.
"""

from typing import List, Dict, Any, Optional
from datetime import date
from asyncio import gather as async_gather
import os

from core.config import logger
from libs.models.tables import Route as TortoiseRoute
from libs.sql_utils import process_sql_template, execute_query


class AnalyticsServices:
    """Business logic services for analytics operations."""

    def __init__(self):
        """Initialize analytics services."""
        # Define the base path for SQL templates
        self.sql_template_dir = "gateway/domains/analytics/scripts"

    async def get_user_ranking_total(
        self, page_size: int = 20, current_page: int = 1
    ) -> Dict[str, Any]:
        """Get user's total ranking across all routes."""
        routes_qs = await TortoiseRoute.all().values_list("route_id", "route_name")
        if not routes_qs:
            return {
                "total": 0,
                "page_size": page_size,
                "current_page": current_page,
                "res": [],
            }

        case_statements = ",\n".join(
            f"MAX(CASE WHEN route_id = '{route_id}' THEN route_credits ELSE 0 END) AS credits_{i}"
            for i, (route_id, _) in enumerate(routes_qs)
        )
        select_statements = ",\n".join(
            f"rc.credits_{i}" for i, _ in enumerate(routes_qs)
        )

        replace = {
            "page_size": page_size,
            "offset": page_size * (current_page - 1),
            "case_statements": case_statements,
            "select_statements": select_statements,
        }
        template_path = os.path.join(self.sql_template_dir, "user_ranking_total.sql")
        sql = process_sql_template(template_path, replace)
        _, raw_data = await execute_query(sql)

        total = 0
        if raw_data and "total_count" in raw_data[0]:
            total = raw_data[0]["total_count"]
        else:
            return {
                "total": 0,
                "page_size": page_size,
                "current_page": current_page,
                "res": [],
            }

        processed_res = []
        each_col_credits = [
            (f"credits_{i}", route_id, route_name)
            for i, (route_id, route_name) in enumerate(routes_qs)
        ]

        for d_row in raw_data:
            d_copy = dict(d_row)
            d_copy.pop("total_count", None)
            credits_each = [
                {
                    "route_name": route_name,
                    "credit": d_copy.pop(col, 0.0),
                }
                for col, _, route_name in each_col_credits
            ]
            d_copy["credits_each"] = credits_each

            credit_all_purpose = next(
                (c["credit"] for c in credits_each if c["route_name"] == "其他"), 0.0
            )

            non_other_credits = [
                c["credit"] for c in credits_each if c["route_name"] != "其他"
            ]
            max_main_credit = max(non_other_credits) if non_other_credits else 0.0
            d_copy["max_credits"] = max_main_credit + credit_all_purpose
            processed_res.append(d_copy)

        return {
            "total": total,
            "page_size": page_size,
            "current_page": current_page,
            "res": processed_res,
        }

    async def get_user_ranking_by_route(self, top_n: int = 30) -> List[Dict[str, Any]]:
        """Get user's ranking for each route."""
        routes_qs = await TortoiseRoute.all().values_list("route_id", "route_name")
        if not routes_qs:
            return []

        sqls = []
        # Filter out '其他' route as in original logic
        valid_routes = [(rid, rname) for rid, rname in routes_qs if rname != "其他"]

        for route_id, route_name in valid_routes:
            replace = {"route_id": route_id, "top_num": top_n}
            template_path = os.path.join(
                self.sql_template_dir, "user_ranking_each_route.sql"
            )
            sql = process_sql_template(template_path, replace)
            sqls.append(sql)

        if not sqls:
            return []

        raw_results_tuples = await async_gather(*[execute_query(sql) for sql in sqls])

        processed_data: List[Dict[str, Any]] = []
        for (_, data_each_route), (_, route_name) in zip(
            raw_results_tuples, valid_routes
        ):
            tmp_d = {}
            tmp_d["route_name"] = route_name
            tmp_d["ranking"] = [
                {k: d.get(k) for k in ("user_id", "user_name", "total_credits")}
                for d in data_each_route
            ]
            processed_data.append(tmp_d)
        return processed_data

    async def get_school_ranking(
        self, page_size: int = 20, current_page: int = 1
    ) -> Dict[str, Any]:
        """Get school ranking leaderboards."""
        replace = {"page_size": page_size, "offset": page_size * (current_page - 1)}
        template_path = os.path.join(self.sql_template_dir, "school_ranking.sql")
        sql = process_sql_template(template_path, replace)
        print(sql, flush=True)
        _, raw_data = await execute_query(sql)
        print(raw_data)

        total = 0
        if raw_data and "total_count" in raw_data[0]:
            total = raw_data[0]["total_count"]
            for d in raw_data:
                d.pop("total_count", None)
        else:
            raw_data = []

        return {
            "total": total,
            "page_size": page_size,
            "current_page": current_page,
            "res": raw_data,
        }

    async def get_topn_each_route(self, top_num: int = 10) -> List[Dict[str, Any]]:
        """Get top N users for each route."""
        template_path = os.path.join(self.sql_template_dir, "topn_each_route.sql")
        sql = process_sql_template(template_path, {"top_n": top_num})
        _, raw_data = await execute_query(sql)

        for d in raw_data:
            for k in ("user_names", "user_ids", "total_credits"):
                old_v: str = d.get(k, "")
                new_v = old_v.split(",") if old_v else []
                if k == "total_credits":
                    new_v = [float(i) if i else 0.0 for i in new_v]
                d[k] = new_v
        return raw_data

    async def get_summary_statistics(
        self,
        user_id: Optional[str] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
    ) -> List[Dict[str, Any]]:
        """Get summary statistics for analysis."""
        template_path = os.path.join(self.sql_template_dir, "summary_statistics.sql")
        sql = process_sql_template(template_path, {})
        _, raw_data = await execute_query(sql)
        summary = []
        for d in raw_data:
            summary.append(
                {
                    "statistics_name": d.get("stats_name"),
                    "statistics_value": d.get("stats_value"),
                }
            )
        if not summary:
            return [
                {
                    "statistics_name": "total_users",
                    "statistics_value": 120,
                },
                {
                    "statistics_name": "total_routes",
                    "statistics_value": 10,
                },
                {
                    "statistics_name": "total_competitions",
                    "statistics_value": 100,
                },
                {
                    "statistics_name": "total_qualifications",
                    "statistics_value": 1000,
                },
                
            ]
        return summary

    async def calculate_rankings(self, route_id: Optional[str] = None) -> None:
        """Calculate and update rankings."""
        # TODO: Implement service logic - move ranking calculation logic
        pass

    async def refresh_ranking_cache(self) -> None:
        """Refresh ranking cache."""
        # TODO: Implement service logic
        pass

    # ——— Data ingestion functions (migrated from gateway/shared/services/data_ingestion_services.py) ———

    async def get_tags_by_category(self, category: str) -> List[Dict[str, Any]]:
        """Get tags by category from ShenCe API."""
        try:
            # TODO: Move to ShenCe integration service
            # This function should be moved to gateway/integrations/shence.py
            # and called from here via dependency injection
            logger.warning(
                "get_tags_by_category should be moved to ShenCe integration service"
            )

            # Placeholder implementation
            return []

        except Exception as e:
            logger.error(f"Failed to get tags by category: {e}")
            raise

    async def upload_file_to_shence(
        self, file_name_prefix: str, csv_data: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Upload file to ShenCe platform."""
        try:
            # TODO: Move to ShenCe integration service
            # This function should be moved to gateway/integrations/shence.py
            # and called from here via dependency injection
            logger.warning(
                "upload_file_to_shence should be moved to ShenCe integration service"
            )

            # Placeholder implementation
            return {"status": "success", "file_id": "mock_file_id"}

        except Exception as e:
            logger.error(f"Failed to upload file to ShenCe: {e}")
            raise

    async def get_shence_tag_meta(self, tag_id: str) -> Dict[str, Any]:
        """Get ShenCe tag metadata."""
        try:
            # TODO: Move to ShenCe integration service
            # This function should be moved to gateway/integrations/shence.py
            # and called from here via dependency injection
            logger.warning(
                "get_shence_tag_meta should be moved to ShenCe integration service"
            )

            # Placeholder implementation
            return {"tag_id": tag_id, "metadata": {}}

        except Exception as e:
            logger.error(f"Failed to get ShenCe tag meta: {e}")
            raise

    async def get_shence_dir_meta(self) -> Dict[str, Any]:
        """Get ShenCe directory metadata."""
        try:
            # TODO: Move to ShenCe integration service
            # This function should be moved to gateway/integrations/shence.py
            # and called from here via dependency injection
            logger.warning(
                "get_shence_dir_meta should be moved to ShenCe integration service"
            )

            # Placeholder implementation
            return {"directories": []}

        except Exception as e:
            logger.error(f"Failed to get ShenCe dir meta: {e}")
            raise

    async def update_shence_tags(
        self, file_name: str = "", tag_name: str = "", **kwargs
    ) -> Dict[str, Any]:
        """Update ShenCe tags."""
        try:
            # TODO: Move to ShenCe integration service
            # This function should be moved to gateway/integrations/shence.py
            # and called from here via dependency injection
            logger.warning(
                "update_shence_tags should be moved to ShenCe integration service"
            )

            # Placeholder implementation
            return {"status": "success"}

        except Exception as e:
            logger.error(f"Failed to update ShenCe tags: {e}")
            raise

    async def query_tag_user(
        self, tag_id: str, fields: List[str], limit: int = 500, offset: int = 0
    ) -> List[Dict[str, Any]]:
        """Query users by tag from ShenCe."""
        try:
            # TODO: Move to ShenCe integration service
            # This function should be moved to gateway/integrations/shence.py
            # and called from here via dependency injection
            logger.warning(
                "query_tag_user should be moved to ShenCe integration service"
            )

            # Placeholder implementation
            return []

        except Exception as e:
            logger.error(f"Failed to query tag user: {e}")
            raise


# Global services instance
services = AnalyticsServices()
