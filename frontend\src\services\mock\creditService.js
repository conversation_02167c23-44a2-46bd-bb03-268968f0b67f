/**
 * Mock Credit API Service
 */

import { initializeMockData } from './mockData.js'

// Initialize mock data
const mockData = initializeMockData()
let { credits, users } = mockData

// Helper functions
const delay = (ms = 500) => new Promise(resolve => setTimeout(resolve, ms))

const paginate = (data, page = 1, limit = 20) => {
  const offset = (page - 1) * limit
  const paginatedData = data.slice(offset, offset + limit)
  
  return {
    data: paginatedData,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: data.length,
      pages: Math.ceil(data.length / limit),
      hasNext: offset + limit < data.length,
      hasPrev: page > 1
    }
  }
}

const filterCredits = (credits, params) => {
  let filtered = [...credits]
  
  if (params.userId) {
    filtered = filtered.filter(credit => credit.userId === params.userId)
  }
  
  if (params.type) {
    filtered = filtered.filter(credit => credit.type === params.type)
  }
  
  if (params.source) {
    filtered = filtered.filter(credit => credit.source === params.source)
  }
  
  if (params.status) {
    filtered = filtered.filter(credit => credit.status === params.status)
  }
  
  if (params.search) {
    const searchLower = params.search.toLowerCase()
    filtered = filtered.filter(credit => 
      credit.reason.toLowerCase().includes(searchLower) ||
      credit.description?.toLowerCase().includes(searchLower) ||
      credit.user.name.toLowerCase().includes(searchLower)
    )
  }
  
  if (params.dateRange) {
    const { start, end } = params.dateRange
    if (start) {
      filtered = filtered.filter(credit => new Date(credit.issuedAt) >= new Date(start))
    }
    if (end) {
      filtered = filtered.filter(credit => new Date(credit.issuedAt) <= new Date(end))
    }
  }
  
  return filtered
}

// Mock API functions
export const creditService = {
  // Get credits list
  async getCredits(params = {}) {
    await delay()
    
    try {
      const filtered = filterCredits(credits, params)
      const result = paginate(filtered, params.page, params.limit)
      
      return {
        success: true,
        ...result
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'FETCH_ERROR',
          message: 'Failed to fetch credits'
        }
      }
    }
  },

  // Get credit by ID
  async getCredit(id) {
    await delay()
    
    try {
      const credit = credits.find(c => c.id === id)
      
      if (!credit) {
        return {
          success: false,
          error: {
            code: 'CREDIT_NOT_FOUND',
            message: 'Credit not found'
          }
        }
      }
      
      return {
        success: true,
        data: credit
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'FETCH_ERROR',
          message: 'Failed to fetch credit'
        }
      }
    }
  },

  // Create credit
  async createCredit(creditData) {
    await delay()
    
    try {
      const user = users.find(u => u.id === creditData.userId)
      if (!user) {
        return {
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found'
          }
        }
      }

      const newCredit = {
        id: `credit_${credits.length + 1}`,
        ...creditData,
        user,
        issuedBy: users[0], // Mock current user
        issuedAt: new Date().toISOString(),
        auditLog: [
          {
            id: `audit_${credits.length + 1}_1`,
            action: 'created',
            performedBy: users[0],
            performedAt: new Date().toISOString(),
            details: {},
            comment: 'Credit created'
          }
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      credits.push(newCredit)
      
      return {
        success: true,
        data: newCredit,
        message: 'Credit created successfully'
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'CREATE_ERROR',
          message: 'Failed to create credit'
        }
      }
    }
  },

  // Get credit statistics
  async getCreditStats() {
    await delay()
    
    try {
      const totalIssued = credits.filter(c => c.status === 'issued').length
      const totalPending = credits.filter(c => c.status === 'pending').length
      const totalExpired = credits.filter(c => c.status === 'expired').length
      
      const issuedCredits = credits.filter(c => c.status === 'issued')
      const averageAmount = issuedCredits.length > 0 
        ? issuedCredits.reduce((sum, c) => sum + c.amount, 0) / issuedCredits.length 
        : 0
      
      const distributionByType = credits.reduce((acc, c) => {
        acc[c.type] = (acc[c.type] || 0) + 1
        return acc
      }, {})
      
      const distributionBySource = credits.reduce((acc, c) => {
        acc[c.source] = (acc[c.source] || 0) + 1
        return acc
      }, {})
      
      const monthlyTrend = [
        { month: '2024-01', issued: 45, amount: 12500 },
        { month: '2024-02', issued: 67, amount: 18900 },
        { month: '2024-03', issued: 89, amount: 24300 }
      ]
      
      const stats = {
        totalIssued,
        totalPending,
        totalExpired,
        averageAmount: Math.round(averageAmount),
        distributionByType,
        distributionBySource,
        monthlyTrend
      }
      
      return {
        success: true,
        data: stats
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'STATS_ERROR',
          message: 'Failed to fetch credit statistics'
        }
      }
    }
  }
}
