import streamlit as st
from libs.constants import AdminActionType
from libs.api import (
    add_credit,
    get_competitions,
    get_qualifications,
    add_admin_log,
    sync_badges,
)
from bson import ObjectId


def handle_submit(
    competition_id,
    user_ids,
    credit_amount,
    qualification_id: str = None,
    batch_id: str = None,
    remark: str = None,
    sync_badge: bool = False,
    **kwargs,
):
    payload = {
        "users": user_ids,
        "credit": credit_amount,
        "qualification_id": qualification_id,
        "batch_id": batch_id,
        "remark": remark,
    }
    print("user_ids:", user_ids)
    response = add_credit(competition_id, payload)
    invalid_users = response.json()["data"]
    if response.status_code == 200 and len(invalid_users) == 0:
        logdata = {
            "admin": st.session_state.username,
            "action_type": AdminActionType.ADD_CREDIT.value,
            "action_related_id": batch_id,
            "remark": remark,
        }
        add_admin_log(logdata)
        st.success(f"添加学分成功:共添加 {len(user_ids)} 名用户")
    elif len(invalid_users) > 0:
        print(response.text)
        st.error("无法为以下用户添加学分:\n" + "\n".join(invalid_users))

    # if sync_badge and response.status_code==200:
    # invalid_users = []
    if sync_badge:
        badge_paylod = [
            {
                "badgeOid": "6662cda25cb66cf661f8844f",
                "level": 0,
                "remark": "通过【{}】活动获得".format(
                    kwargs.get("competition_name", "")
                )
                + "\n"
                + remark,
                "userOid": user_id,
                "count": credit_amount,
            }
            for user_id in user_ids
            if user_id not in invalid_users
        ]
        sync_response = sync_badges({"data": badge_paylod})
        if sync_response.status_code == 200:
            st.success("徽章发放同步成功")
        else:
            st.error(f"徽章发放同步失败: {sync_response.json()['error']}")


st.title("发放学分")

# Step 1: Select competition
competitions = get_competitions()["data"]
# competitions = [{"id": 1, "name": "2020-2021学年"}, {"id": 2, "name": "2021-2022学年"}, {"id": 3, "name": "2022-2023学年"}]
competition_options = {
    comp["competition_id"]: comp["competition_name"] for comp in competitions
}
# default index is current competition_id or 0
opts = list(competition_options.keys())
default_index = (
    opts.index(st.session_state.default_competition)
    if st.session_state.default_competition
    else 0
)
competition_id = st.selectbox(
    "Select Competition",
    options=opts,
    format_func=lambda x: competition_options[x],
    index=default_index,
)
st.session_state.default_competition = competition_id

# Step 2：选择学分发放的理由
qualifications = get_qualifications(competition_id)["data"]

qual_options = {
    qual["qualification_id"]: qual["qualification_name"] for qual in qualifications
}
credit = {qual["qualification_id"]: qual["credit"] for qual in qualifications}
qualification_id = st.selectbox(
    "选择学分发放的理由",
    format_func=lambda x: qual_options[x],
    options=qual_options.keys(),
)
default_credit = credit[qualification_id]
inputs = st.container()
user_ids_input, utils = inputs.columns(2)


# Step 2: Input user IDs
uids = user_ids_input.text_area(
    "添加 user_id", placeholder="user_id 1\nuser_id 2\n", help="请检查是否有重复的 id"
)
user_ids = [user_id.strip() for user_id in uids.split("\n")]

cred, toggle = utils.columns(2, vertical_alignment="center")
# credits
credits = cred.number_input(
    "待添加的学分",
    min_value=1,
    step=1,
    value=default_credit,
    help="当前显示默认值",
)
sync_badge = toggle.toggle("同步徽章", help="将发放对应学分数量的徽章", value=True)
if credits != default_credit:
    st.warning("待添加的学分和预先设定的学分不同，请再次确认")

remark = st.text_input("备注", placeholder="备注")

batch_id = str(ObjectId())
submission = utils.button(
    "确认提交",
    key="submit",
    help="确认添加学分",
    on_click=lambda: handle_submit(
        competition_id,
        user_ids,
        credits,
        qualification_id=qualification_id,
        batch_id=batch_id,
        remark=remark,
        sync_badge=sync_badge,
        competition_name=competition_options[competition_id],
        qualification_name=qual_options[qualification_id],
    ),
    use_container_width=True,
)
