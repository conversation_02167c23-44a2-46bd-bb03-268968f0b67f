<template>
  <div class="qualification-list">
    <!-- Empty State -->
    <div v-if="!loading && (!qualifications || qualifications.length === 0)" class="empty-state">
      <el-empty description="No qualifications found">
        <el-button type="primary" @click="createQualification">
          <el-icon><Plus /></el-icon>
          Create First Qualification
        </el-button>
      </el-empty>
    </div>

    <!-- Loading State -->
    <div v-else-if="loading" class="loading-container">
      <el-skeleton :rows="4" animated />
    </div>

    <!-- Qualifications List -->
    <div v-else>
      <div class="list-header">
        <div class="header-info">
          <span class="qualification-count">{{ qualifications.length }} qualification{{ qualifications.length !== 1 ? 's' : '' }}</span>
          <span class="total-credits">Total: {{ getTotalCredits() }} credits</span>
        </div>
        <el-button type="primary" size="small" @click="createQualification">
          <el-icon><Plus /></el-icon>
          Add Qualification
        </el-button>
      </div>

      <div class="qualifications-grid">
        <el-card 
          v-for="qualification in qualifications" 
          :key="qualification.id"
          class="qualification-card"
          shadow="hover"
        >
          <template #header>
            <div class="card-header">
              <div class="qualification-title">
                <h4>{{ qualification.name || qualification.qualification_name }}</h4>
                <el-tag type="success" size="small">
                  {{ qualification.credit || 0 }} credits
                </el-tag>
              </div>
              <div class="card-actions">
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="editQualification(qualification)"
                  circle
                >
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button 
                  type="danger" 
                  size="small" 
                  @click="deleteQualification(qualification)"
                  circle
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </template>

          <div class="qualification-content">
            <div class="qualification-details">
              <div class="detail-row">
                <span class="detail-label">Type:</span>
                <span class="detail-value">{{ getQualificationTypeName(qualification.type || qualification.qualification_type) }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Logic:</span>
                <span class="detail-value">{{ getQualificationLogicName(qualification.logic || qualification.qualification_logic) }}</span>
              </div>
              <div class="detail-row" v-if="qualification.scoreThreshold || qualification.score_threshold">
                <span class="detail-label">Score Threshold:</span>
                <span class="detail-value">{{ qualification.scoreThreshold || qualification.score_threshold }}%</span>
              </div>
              <div class="detail-row" v-if="qualification.relatedTaskId || qualification.related_task_id">
                <span class="detail-label">Related Task:</span>
                <span class="detail-value">{{ qualification.relatedTaskId || qualification.related_task_id }}</span>
              </div>
            </div>

            <div class="qualification-actions">
              <el-button type="success" size="small" @click="awardCredits(qualification)">
                <el-icon><Coin /></el-icon>
                Award Credits
              </el-button>
              <el-button type="info" size="small" @click="viewHistory(qualification)">
                <el-icon><Document /></el-icon>
                View History
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- Delete Confirmation Dialog -->
    <el-dialog
      v-model="deleteDialogVisible"
      title="Delete Qualification"
      width="400px"
    >
      <p>Are you sure you want to delete this qualification?</p>
      <p><strong>{{ qualificationToDelete?.name || qualificationToDelete?.qualification_name }}</strong></p>
      <p class="warning-text">This action cannot be undone.</p>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteDialogVisible = false">Cancel</el-button>
          <el-button type="danger" @click="confirmDelete" :loading="deleting">
            Delete
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Edit,
  Delete,
  Coin,
  Document
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  competitionId: {
    type: String,
    required: true
  },
  qualifications: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['refresh'])

const router = useRouter()

// State
const deleteDialogVisible = ref(false)
const qualificationToDelete = ref(null)
const deleting = ref(false)

// Computed
const getTotalCredits = () => {
  return props.qualifications.reduce((total, qual) => {
    return total + (qual.credit || qual.qualification_credit || 0)
  }, 0)
}

// Methods
const getQualificationTypeName = (type) => {
  const typeNames = {
    1: 'Score Based',
    2: 'Rank Based',
    3: 'Participation',
    4: 'Completion',
    5: 'Custom'
  }
  return typeNames[type] || 'Unknown'
}

const getQualificationLogicName = (logic) => {
  const logicNames = {
    1: 'Greater Than',
    2: 'Less Than',
    3: 'Equal To',
    4: 'Between',
    5: 'Top N',
    6: 'Bottom N'
  }
  return logicNames[logic] || 'Unknown'
}

const createQualification = () => {
  router.push(`/competitions/${props.competitionId}/qualifications/create`)
}

const editQualification = (qualification) => {
  router.push(`/competitions/${props.competitionId}/qualifications/${qualification.id}/edit`)
}

const deleteQualification = (qualification) => {
  qualificationToDelete.value = qualification
  deleteDialogVisible.value = true
}

const confirmDelete = async () => {
  if (!qualificationToDelete.value) return

  deleting.value = true
  
  try {
    // TODO: Implement actual delete API call
    // await qualificationService.deleteQualification(qualificationToDelete.value.id)
    
    ElMessage.success('Qualification deleted successfully')
    deleteDialogVisible.value = false
    qualificationToDelete.value = null
    emit('refresh')
  } catch (error) {
    console.error('Failed to delete qualification:', error)
    ElMessage.error('Failed to delete qualification')
  } finally {
    deleting.value = false
  }
}

const awardCredits = (qualification) => {
  router.push(`/credits?competition=${props.competitionId}&qualification=${qualification.id}`)
}

const viewHistory = (qualification) => {
  router.push(`/credits?competition=${props.competitionId}&qualification=${qualification.id}&tab=history`)
}
</script>

<style lang="scss" scoped>
.qualification-list {
  .empty-state {
    text-align: center;
    padding: 40px 20px;
  }

  .loading-container {
    padding: 20px;
  }

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 0;
    border-bottom: 1px solid var(--border-color);

    .header-info {
      display: flex;
      align-items: center;
      gap: 16px;

      .qualification-count {
        font-weight: 500;
        color: var(--text-color);
      }

      .total-credits {
        color: var(--text-color-secondary);
        font-size: 14px;
      }
    }
  }

  .qualifications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;

    .qualification-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;

        .qualification-title {
          flex: 1;

          h4 {
            margin: 0 0 8px 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--text-color);
          }
        }

        .card-actions {
          display: flex;
          gap: 8px;
          margin-left: 16px;
        }
      }

      .qualification-content {
        .qualification-details {
          margin-bottom: 16px;

          .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid var(--border-color-light);

            &:last-child {
              border-bottom: none;
            }

            .detail-label {
              font-size: 14px;
              color: var(--text-color-secondary);
              font-weight: 500;
            }

            .detail-value {
              font-size: 14px;
              color: var(--text-color);
            }
          }
        }

        .qualification-actions {
          display: flex;
          gap: 12px;

          .el-button {
            flex: 1;
          }
        }
      }
    }
  }

  .warning-text {
    color: var(--color-danger);
    font-size: 14px;
    margin-top: 8px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .qualification-list {
    .list-header {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;

      .header-info {
        justify-content: center;
      }
    }

    .qualifications-grid {
      grid-template-columns: 1fr;
    }

    .qualification-card {
      .card-header {
        flex-direction: column;
        align-items: stretch;

        .card-actions {
          margin-left: 0;
          margin-top: 12px;
          justify-content: center;
        }
      }

      .qualification-content {
        .qualification-actions {
          flex-direction: column;
        }
      }
    }
  }
}
</style>
