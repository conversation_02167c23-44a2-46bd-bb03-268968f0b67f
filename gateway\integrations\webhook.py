"""
Webhook Integration - External webhook calls with graceful error handling.

Handles webhook execution with retry logic, timeout management, and comprehensive error handling.
"""

import re
import time
import asyncio
from typing import Dict, Any, Optional
import logging
from datetime import datetime

from gateway.integrations.base import BaseIntegration, RetryConfig
from libs.auth.context import AuthContext
from libs.errors import IntegrationErrors
from gateway.integrations.base import HTTPResponse

logger = logging.getLogger(__name__)


class WebhookIntegration(BaseIntegration):
    """Integration service for webhook execution with retry logic and error handling."""

    def __init__(self):
        """Initialize Webhook integration service."""
        super().__init__(timeout=30)
        logger.debug("Initialized Webhook integration service")

    def _validate_webhook_url(self, url: str) -> bool:
        """
        Validate webhook URL format and security.

        Args:
            url: URL to validate

        Returns:
            True if valid, False otherwise
        """
        # URL pattern validation
        url_pattern = re.compile(
            r"^https?://"  # http:// or https://
            r"(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|"  # domain...
            r"localhost|"  # localhost...
            r"\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})"  # ...or ip
            r"(?::\d+)?"  # optional port
            r"(?:/?|[/?]\S+)$",
            re.IGNORECASE,
        )

        if not url_pattern.match(url):
            return False

        # Security checks - block internal/private networks in production
        # This is a basic check; in production, you might want more sophisticated validation
        blocked_patterns = [
            r"127\.\d+\.\d+\.\d+",  # localhost
            r"10\.\d+\.\d+\.\d+",  # private network
            r"192\.168\.\d+\.\d+",  # private network
            r"172\.(1[6-9]|2[0-9]|3[0-1])\.\d+\.\d+",  # private network
        ]

        for pattern in blocked_patterns:
            if re.search(pattern, url):
                # Allow localhost in development
                if "localhost" in url or "127.0.0.1" in url:
                    continue
                return False

        return True

    async def execute_webhook(
        self,
        url: str,
        payload: Dict[str, Any],
        auth_context: AuthContext,
        method: str = "POST",
        headers: Optional[Dict[str, str]] = None,
        timeout: Optional[int] = None,
        retry_config: Optional[RetryConfig] = None,
    ) -> HTTPResponse:
        """
        Execute webhook with retry logic and comprehensive error handling.

        Args:
            url: Webhook URL
            payload: Webhook payload data
            auth_context: Authentication context
            method: HTTP method (default: POST)
            headers: Additional request headers
            timeout: Request timeout in seconds
            retry_config: Retry configuration

        Returns:
            HTTPResponse containing execution results
        """
        start_time = time.time()
        webhook_id = f"webhook_{int(start_time * 1000)}"

        try:
            # Validate webhook URL
            if not self._validate_webhook_url(url):
                raise HTTPResponse(
                    IntegrationErrors.VALIDATION_ERROR,
                    "Invalid or blocked webhook URL",
                    "integration",
                    {
                        "service": "webhook",
                        "operation": "execute_webhook",
                        "url": url,
                        "webhook_id": webhook_id,
                    },
                )

            # Validate method
            allowed_methods = ["GET", "POST", "PUT", "PATCH", "DELETE"]
            if method.upper() not in allowed_methods:
                raise HTTPResponse(
                    IntegrationErrors.VALIDATION_ERROR,
                    f"Unsupported HTTP method: {method}",
                    "integration",
                    {
                        "service": "webhook",
                        "operation": "execute_webhook",
                        "method": method,
                        "allowed_methods": allowed_methods,
                    },
                )

            # Prepare request headers
            request_headers = {
                "Content-Type": "application/json",
                "User-Agent": "community-services/webhook-integration",
                "X-Webhook-ID": webhook_id,
                "X-Executed-By": auth_context.user_id,
                "X-Execution-Time": datetime.now().isoformat(),
            }

            if headers:
                request_headers.update(headers)

            # Enhance payload with metadata
            enhanced_payload = payload.copy()
            enhanced_payload.update(
                {
                    "_webhook_metadata": {
                        "webhook_id": webhook_id,
                        "executed_by": auth_context.user_id,
                        "execution_time": datetime.now().isoformat(),
                        "source": "community-services",
                    }
                }
            )

            # Configure retry settings
            if retry_config is None:
                retry_config = RetryConfig(
                    max_attempts=3,
                    base_delay=1.0,
                    max_delay=30.0,
                    exponential_base=2.0,
                    jitter=True,
                )

            # Use custom timeout if provided
            if timeout:
                self.timeout = self.timeout._replace(total=timeout)

            # Execute webhook with retry logic
            session = await self._get_session()
            last_error = None

            for attempt in range(retry_config.max_attempts):
                try:
                    logger.debug(
                        f"Executing webhook (attempt {attempt + 1})",
                        webhook_id=webhook_id,
                        url=url,
                        method=method,
                    )

                    async with session.request(
                        method=method.upper(),
                        url=url,
                        json=enhanced_payload
                        if method.upper() in ["POST", "PUT", "PATCH"]
                        else None,
                        params=enhanced_payload if method.upper() == "GET" else None,
                        headers=request_headers,
                        timeout=self.timeout,
                    ) as response:
                        execution_time_ms = (time.time() - start_time) * 1000

                        # Read response data
                        try:
                            response_data = await response.json()
                        except Exception:
                            response_data = {"raw_response": await response.text()}

                        # Check if successful
                        if response.status < 400:
                            logger.info(
                                "Webhook executed successfully",
                                webhook_id=webhook_id,
                                url=url,
                                status_code=response.status,
                                execution_time_ms=execution_time_ms,
                                attempt=attempt + 1,
                            )

                            return HTTPResponse(
                                IntegrationErrors.SUCCESS,
                                "Webhook executed successfully",
                                "integration",
                                {
                                    "webhook_id": webhook_id,
                                    "success": True,
                                    "status_code": response.status,
                                    "response_body": response_data,
                                    "execution_time_ms": execution_time_ms,
                                    "retry_count": attempt,
                                    "url": url,
                                    "method": method,
                                    "executed_at": datetime.now().isoformat(),
                                }
                                    )

                        # Check if we should retry
                        elif self._should_retry_webhook(
                            response.status, attempt, retry_config
                        ):
                            last_error = f"HTTP {response.status}: {response_data}"
                            if attempt < retry_config.max_attempts - 1:
                                delay = retry_config.get_delay(attempt)
                                logger.warning(
                                    f"Webhook failed, retrying in {delay:.2f}s",
                                    webhook_id=webhook_id,
                                    status_code=response.status,
                                    attempt=attempt + 1,
                                    delay=delay,
                                )
                                await asyncio.sleep(delay)
                                continue

                        # Final failure
                        execution_time_ms = (time.time() - start_time) * 1000

                        logger.error(
                            "Webhook execution failed",
                            webhook_id=webhook_id,
                            url=url,
                            status_code=response.status,
                            execution_time_ms=execution_time_ms,
                            attempts=attempt + 1,
                        )

                        return HTTPResponse(
                            IntegrationErrors.HTTP_CLIENT_ERROR,
                            f"Webhook execution failed: HTTP {response.status}",
                            "integration",
                            {
                                "service": "webhook",
                                "operation": "execute_webhook",
                                "webhook_id": webhook_id,
                                "url": url,
                                "status_code": response.status,
                                "response_body": response_data,
                                "execution_time_ms": execution_time_ms,
                                "retry_count": attempt,
                                "final_attempt": True,
                            },
                        )

                except asyncio.TimeoutError:
                    execution_time_ms = (time.time() - start_time) * 1000
                    last_error = f"Request timeout after {self.timeout.total}s"

                    if attempt < retry_config.max_attempts - 1:
                        delay = retry_config.get_delay(attempt)
                        logger.warning(
                            f"Webhook timeout, retrying in {delay:.2f}s",
                            webhook_id=webhook_id,
                            timeout=self.timeout.total,
                            attempt=attempt + 1,
                        )
                        await asyncio.sleep(delay)
                        continue

                    logger.error(
                        "Webhook execution timed out",
                        webhook_id=webhook_id,
                        url=url,
                        timeout=self.timeout.total,
                        execution_time_ms=execution_time_ms,
                    )

                    return HTTPResponse(
                        IntegrationErrors.TIMEOUT_ERROR,
                        f"Webhook execution timed out after {self.timeout.total}s",
                        "integration",
                        {
                            "service": "webhook",
                            "operation": "execute_webhook",
                            "webhook_id": webhook_id,
                            "url": url,
                            "timeout": self.timeout.total,
                            "execution_time_ms": execution_time_ms,
                            "retry_count": attempt,
                        },
                    )

                except Exception as e:
                    last_error = str(e)

                    if attempt < retry_config.max_attempts - 1:
                        delay = retry_config.get_delay(attempt)
                        logger.warning(
                            f"Webhook request error, retrying in {delay:.2f}s: {e}",
                            webhook_id=webhook_id,
                            attempt=attempt + 1,
                        )
                        await asyncio.sleep(delay)
                        continue

            # All attempts failed
            execution_time_ms = (time.time() - start_time) * 1000

            logger.error(
                "Webhook execution failed after all retries",
                webhook_id=webhook_id,
                url=url,
                attempts=retry_config.max_attempts,
                last_error=last_error,
                execution_time_ms=execution_time_ms,
            )

            return HTTPResponse(
                IntegrationErrors.HTTP_CLIENT_ERROR,
                f"Webhook execution failed after {retry_config.max_attempts} attempts: {last_error}",
                "integration",
                {
                    "service": "webhook",
                    "operation": "execute_webhook",
                    "webhook_id": webhook_id,
                    "url": url,
                    "attempts": retry_config.max_attempts,
                    "last_error": last_error,
                    "execution_time_ms": execution_time_ms,
                },
            )

        except Exception as e:
            execution_time_ms = (time.time() - start_time) * 1000
            logger.error(f"Webhook execution error: {e}", webhook_id=webhook_id)
            return HTTPResponse(
                IntegrationErrors.INTERNAL_SERVER_ERROR,
                f"Webhook execution error: {e}",
                "integration",
                {"service": "webhook", "operation": "execute_webhook"},
            )

    def _should_retry_webhook(
        self, status_code: int, attempt: int, retry_config: RetryConfig
    ) -> bool:
        """
        Determine if webhook should be retried based on status code.

        Args:
            status_code: HTTP status code
            attempt: Current attempt number (0-based)
            retry_config: Retry configuration

        Returns:
            True if should retry, False otherwise
        """
        # Don't retry if we've reached max attempts
        if attempt >= retry_config.max_attempts - 1:
            return False

        # Retry on server errors and some client errors
        retry_codes = {
            408,  # Request Timeout
            429,  # Too Many Requests
            500,  # Internal Server Error
            502,  # Bad Gateway
            503,  # Service Unavailable
            504,  # Gateway Timeout
        }

        return status_code in retry_codes

    async def health_check(self) -> HTTPResponse:
        """
        Check the health of webhook integration service.

        Returns:
            HTTPResponse with health status information
        """
        try:
            # Test HTTP client
            await self._get_session()

            health_data = {
                "service": "webhook",
                "status": "healthy",
                "http_client": "ready",
                "timeout": self.timeout.total,
                "supported_methods": ["GET", "POST", "PUT", "PATCH", "DELETE"],
                "timestamp": datetime.now().isoformat(),
            }

            return HTTPResponse(
                IntegrationErrors.SUCCESS,
                "Health check successful",
                "integration",
                health_data,
            )

        except Exception as e:
            logger.error(f"Health check failed for webhook integration: {e}")
            return HTTPResponse(
                IntegrationErrors.HTTP_CLIENT_ERROR,
                f"Health check failed: {str(e)}",
                "integration",
                {"service": "webhook"},
            )
