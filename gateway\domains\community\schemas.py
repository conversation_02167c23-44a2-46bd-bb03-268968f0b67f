from pydantic import BaseModel, Field
from typing import List, Dict, Any
from libs.schemas.api.common import PaginatedResponse


class UniversityResponse(BaseModel):
    """University information response."""
    
    university_id: str = Field(..., description="University ID")
    university_name: str = Field(..., description="University name", alias="University")
    country: str = Field(..., description="Country", alias="Country")
    province: str = Field(..., description="Province", alias="Province")


class MajorResponse(BaseModel):
    """Major information response."""
    
    major_id: str = Field(..., description="Major ID")
    major_name: str = Field(..., description="Major name", alias="Major")
    discipline: str = Field(..., description="Discipline", alias="Discipline")


# Standardized paginated responses using new common pattern
class UniversityListResponse(PaginatedResponse[UniversityResponse]):
    """Paginated list of universities with metadata."""
    pass
    

class MajorListResponse(PaginatedResponse[MajorResponse]):
    """Paginated list of majors with metadata."""
    pass


# Export schemas
__all__ = [
    # Response schemas
    "UniversityResponse",
    "MajorResponse", 
    "UniversityListResponse",
    "MajorListResponse",
]
