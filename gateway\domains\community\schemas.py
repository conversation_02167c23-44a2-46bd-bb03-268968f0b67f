from pydantic import BaseModel, Field
from typing import List, Dict, Any
from libs.schemas.api.base import BaseDataListResponse



class UniversityResponse(BaseModel):
    university_id: str = Field(..., description="University ID")
    university_name: str = Field(..., description="University name", alias="University")
    country: str = Field(..., description="Country", alias="Country")
    province: str = Field(..., description="Province", alias="Province")

class MajorResponse(BaseModel):
    major_id: str = Field(..., description="Major ID")
    major_name: str = Field(..., description="Major name", alias="Major")
    discipline: str = Field(..., description="Discipline", alias="Discipline")


class UniversityListResponse(BaseDataListResponse):
    data: List[UniversityResponse] = Field(..., description="List of universities")
    
class MajorListResponse(BaseDataListResponse):
    data: List[MajorResponse] = Field(..., description="List of majors")

# Export schemas
__all__ = [
    # Request schemas
    # Response schemas
    "UniversityListResponse",
    "MajorListResponse",
]
