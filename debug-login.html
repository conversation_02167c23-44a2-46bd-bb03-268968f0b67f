<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Debug Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
        .info {
            color: blue;
        }
    </style>
</head>
<body>
    <h1>Login Debug Tool</h1>
    <p>This tool helps debug the login redirection issue by testing the login flow step by step.</p>
    
    <div class="debug-section">
        <h3>1. Test Application Accessibility</h3>
        <button onclick="testAppAccess()">Test App Access</button>
        <div id="app-access-log" class="log"></div>
    </div>
    
    <div class="debug-section">
        <h3>2. Test Login API</h3>
        <button onclick="testLoginAPI()">Test Login API</button>
        <div id="login-api-log" class="log"></div>
    </div>
    
    <div class="debug-section">
        <h3>3. Test Dashboard Access</h3>
        <button onclick="testDashboardAccess()">Test Dashboard Access</button>
        <div id="dashboard-log" class="log"></div>
    </div>
    
    <div class="debug-section">
        <h3>4. Simulate Full Login Flow</h3>
        <button onclick="simulateLoginFlow()">Simulate Login Flow</button>
        <div id="full-flow-log" class="log"></div>
    </div>

    <script>
        const baseUrl = 'http://localhost:3001';
        const credentials = {
            email: '<EMAIL>',
            password: 'Dick920815##'
        };

        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            element.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            element.scrollTop = element.scrollHeight;
        }

        async function testAppAccess() {
            const logId = 'app-access-log';
            document.getElementById(logId).innerHTML = '';
            
            try {
                log(logId, '🔍 Testing application accessibility...');
                
                const response = await fetch(baseUrl);
                log(logId, `📡 Home page status: ${response.status}`, response.ok ? 'success' : 'error');
                
                const loginResponse = await fetch(`${baseUrl}/login`);
                log(logId, `📡 Login page status: ${loginResponse.status}`, loginResponse.ok ? 'success' : 'error');
                
                if (response.ok && loginResponse.ok) {
                    log(logId, '✅ Application is accessible', 'success');
                } else {
                    log(logId, '❌ Application accessibility issues detected', 'error');
                }
            } catch (error) {
                log(logId, `🚨 Error: ${error.message}`, 'error');
            }
        }

        async function testLoginAPI() {
            const logId = 'login-api-log';
            document.getElementById(logId).innerHTML = '';
            
            try {
                log(logId, '🔐 Testing login API...');
                log(logId, `📧 Email: ${credentials.email}`);
                
                const response = await fetch(`${baseUrl}/camp-admin/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(credentials)
                });
                
                log(logId, `📡 Login API status: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const data = await response.json();
                    log(logId, `✅ Login successful!`, 'success');
                    log(logId, `🔑 Token received: ${!!data.token || !!data.data?.token}`, 'success');
                    log(logId, `👤 User data: ${JSON.stringify(data.user || data.data?.user, null, 2)}`);
                    
                    // Store token for dashboard test
                    window.debugToken = data.token || data.data?.token;
                } else {
                    const errorText = await response.text();
                    log(logId, `❌ Login failed: ${errorText}`, 'error');
                }
            } catch (error) {
                log(logId, `🚨 Error: ${error.message}`, 'error');
            }
        }

        async function testDashboardAccess() {
            const logId = 'dashboard-log';
            document.getElementById(logId).innerHTML = '';
            
            try {
                log(logId, '📊 Testing dashboard access...');
                
                if (!window.debugToken) {
                    log(logId, '⚠️ No token available. Run login test first.', 'error');
                    return;
                }
                
                const response = await fetch(`${baseUrl}/dashboard`, {
                    headers: {
                        'Authorization': `Bearer ${window.debugToken}`
                    }
                });
                
                log(logId, `📡 Dashboard status: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    log(logId, '✅ Dashboard accessible', 'success');
                } else {
                    log(logId, '❌ Dashboard access failed', 'error');
                }
            } catch (error) {
                log(logId, `🚨 Error: ${error.message}`, 'error');
            }
        }

        async function simulateLoginFlow() {
            const logId = 'full-flow-log';
            document.getElementById(logId).innerHTML = '';
            
            try {
                log(logId, '🎬 Starting full login flow simulation...');
                
                // Step 1: Check app
                log(logId, '1️⃣ Checking application...');
                await testAppAccess();
                
                // Step 2: Login
                log(logId, '2️⃣ Attempting login...');
                await testLoginAPI();
                
                // Step 3: Test dashboard
                log(logId, '3️⃣ Testing dashboard access...');
                await testDashboardAccess();
                
                log(logId, '🏁 Full flow simulation complete', 'success');
                
            } catch (error) {
                log(logId, `🚨 Flow error: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
