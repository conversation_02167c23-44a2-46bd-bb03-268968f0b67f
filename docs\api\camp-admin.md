# Camp Admin API Documentation

## Overview

The Camp Admin API provides endpoints for managing competition routes, qualifications, credits, and administrative operations. All endpoints are prefixed with `/api/v1/camp-admin`.

## Authentication

All endpoints require authentication. Some endpoints require admin privileges as noted.

## Endpoints

### Routes Management

#### List Routes
```http
GET /api/v1/camp-admin/routes
```

**Description**: Get all available competition routes.

**Parameters**:
- `limit` (query, optional): Number of routes to return (max: 100, default: no limit)
- `offset` (query, optional): Number of routes to skip (default: 0)

**Response**: `SuccessResponse` with `RouteListResponse`

**Example Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "route_123",
      "name": "Data Science Track"
    }
  ]
}
```

---

### Competition Management

#### List Competitions
```http
GET /api/v1/camp-admin/
```

**Description**: Get all competitions with optional filtering by route and status.

**Parameters**:
- `limit` (query, optional): Number of competitions to return (max: 1000, default: no limit)
- `offset` (query, optional): Number of competitions to skip (default: 0)
- `route_id` (query, optional): Filter by route ID
- `status` (query, optional): Filter by competition status

**Response**: `SuccessResponse` with `CompetitionListResponse`

**Example Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "record_123",
      "competition_id": "comp_456",
      "competition_name": "AI Challenge 2023",
      "route_id": "route_123",
      "route_name": "Data Science Track"
    }
  ]
}
```

---

### Qualification Management

#### List Qualifications
```http
GET /api/v1/camp-admin/qualifications
```

**Description**: Get qualification scoring rules, optionally filtered by competition.

**Parameters**:
- `competition_id` (query, optional): Filter by competition ID
- `limit` (query, optional): Number of qualifications to return (max: 1000, default: no limit)
- `offset` (query, optional): Number of qualifications to skip (default: 0)

**Response**: `SuccessResponse` with `QualificationListResponse`

**Example Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "qual_123",
      "competition_id": "comp_456",
      "qualification_name": "Top Score Achievement",
      "credit": 100
    }
  ]
}
```

#### Create Qualification
```http
POST /api/v1/camp-admin/qualifications
```

**Description**: Create a new qualification scoring rule (admin only).

**Request Body**: `QualificationCreateRequest`
```json
{
  "competition_id": "comp_456",
  "credit": 100,
  "qualification_type": 1,
  "qualification_logic": 1,
  "qualification_name": "Top Score Achievement",
  "related_task_id": "task_123",
  "score_threshold": 80
}
```

**Response**: `SuccessResponse` with qualification ID

---

### Credit Management

#### Get Credit History
```http
GET /api/v1/camp-admin/credits/history
```

**Description**: Get credit award history with optional filtering.

**Parameters**:
- `competition_id` (query, optional): Filter by competition ID
- `user_id` (query, optional): Filter by user ID
- `limit` (query, optional): Number of records to return (1-1000, default: 200)
- `offset` (query, optional): Number of records to skip (default: 0)

**Response**: `SuccessResponse` with credit history list

**Example Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "credit_123",
      "user_id": "user_456",
      "competition_id": "comp_789",
      "qual_id": "qual_123",
      "credit": 100,
      "remark": "Excellent performance",
      "batch_id": "batch_001",
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z"
    }
  ]
}
```

#### Award Credits
```http
POST /api/v1/camp-admin/credits/award
```

**Description**: Award credits to users (admin only).

**Request Body**: `CreditAwardRequest`
```json
{
  "user_ids": ["user_123", "user_456"],
  "competition_id": "comp_789",
  "qual_id": "qual_123",
  "credit": 100,
  "remark": "Excellent performance in competition"
}
```

**Response**: `SuccessResponse` with award results

**Permissions**: Requires admin privileges

#### Revoke Credits
```http
POST /api/v1/camp-admin/credits/revoke
```

**Description**: Revoke previously awarded credits (admin only).

**Request Body**: `CreditRevokeRequest`
```json
{
  "record_id": "credit_123"
}
```

**Response**: `SuccessResponse` with revocation confirmation

**Permissions**: Requires admin privileges

---

### Admin Logs Management

#### Get Admin Logs
```http
GET /api/v1/camp-admin/logs
```

**Description**: Get administrative operation logs.

**Parameters**:
- `limit` (query, optional): Number of logs to return (1-100, default: 20)
- `offset` (query, optional): Number of logs to skip (default: 0)

**Response**: `SuccessResponse` with admin logs list

#### Create Admin Log
```http
POST /api/v1/camp-admin/logs/create
```

**Description**: Create an administrative operation log (super admin only).

**Request Body**: `AdminLogsCreateRequest`
```json
{
  "action_type": 1,
  "action_related_id": "comp_123",
  "remark": "Created new competition"
}
```

**Response**: `SuccessResponse` with log ID

**Permissions**: Requires super admin privileges

**Warning**: Use with caution - for super admins only

## Data Models

### Route Response
```json
{
  "id": "string",
  "name": "string"
}
```

### Competition Response
```json
{
  "id": "string",
  "competition_id": "string",
  "competition_name": "string",
  "route_id": "string",
  "route_name": "string"
}
```

### Qualification Response
```json
{
  "id": "string",
  "competition_id": "string",
  "qualification_name": "string",
  "credit": 100
}
```

### Credit Log Response
```json
{
  "id": "string",
  "user_id": "string",
  "competition_id": "string",
  "qual_id": "string",
  "credit": 100,
  "remark": "string",
  "batch_id": "string",
  "created_at": "2023-01-01T00:00:00Z",
  "updated_at": "2023-01-01T00:00:00Z"
}
```

## Error Responses

All endpoints may return standard error responses:
- `400 Bad Request`: Invalid parameters or request body
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient admin privileges
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server error

## Usage Examples

### Frontend Integration

#### Get Routes for Dropdown
```javascript
const routes = await fetch('/api/v1/camp-admin/routes');
const data = await routes.json();
```

#### Filter Competitions by Route
```javascript
const competitions = await fetch('/api/v1/camp-admin/?route_id=route_123');
```

#### Award Credits to Multiple Users
```javascript
const response = await fetch('/api/v1/camp-admin/credits/award', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer <admin_token>'
  },
  body: JSON.stringify({
    user_ids: ['user_123', 'user_456'],
    competition_id: 'comp_789',
    qual_id: 'qual_123',
    credit: 100,
    remark: 'Competition completion bonus'
  })
});
```

## Notes for Frontend Development

1. **Admin Permissions**: Many endpoints require admin or super admin privileges
2. **Pagination**: Use appropriate pagination for large datasets
3. **Filtering**: Most list endpoints support filtering by relevant IDs
4. **Credit Management**: Always include remarks when awarding/revoking credits
5. **Validation**: Ensure credit values are non-negative
6. **Audit Trail**: Admin logs provide audit trail for administrative actions

## Related APIs

- **Users API**: For user profile information
- **Competitions API**: For competition user data
- **Analytics API**: For competition statistics and rankings
