import pandas as pd
import streamlit as st
from libs.api import get_competitions, get_credit_history, add_admin_log, add_fake_stats
from bson import ObjectId
from libs.constants import AdminActionType, StatisticsName
from datetime import datetime

now_ts = datetime.now().strftime("%Y-%m-%d")


def handle_download(competition_id, signature=now_ts):
    if competition_id == "all":
        credit_history = get_credit_history().json()["data"]
    else:
        credit_history = get_credit_history(competition_id=competition_id).json()[
            "data"
        ]
    df = pd.DataFrame(credit_history)
    csv = df.to_csv(index=False).encode("utf-8-sig")
    return csv


def call_injection(container, type, name, value):
    admin = st.session_state.username
    log_payload = {
        "admin": admin,
        "action_type": type,
        "action_related_id": "",
        "remark": str(value),
    }
    stats_payload = {
        "statistics_name": name,
        "statistics_value": value,
        "statistics_related_id": "",
        "remark": admin,
    }

    resp_log = add_admin_log(log_payload)
    resp_stats = add_fake_stats(stats_payload)
    if resp_log.status_code == 200 and resp_stats.status_code == 200:
        container.success(f"成功注入 {value} {name}")
    elif resp_log.status_code != 200:
        container.error("注入失败:{}".format(resp_log.text))
    else:
        container.error("注入失败:{}".format(resp_stats.text))


def handle_injection(container, regs, submissions):
    if isinstance(regs, int) and isinstance(submissions, int):
        call_injection(
            container,
            AdminActionType.INJECT_REGISTERS.value,
            StatisticsName.TOTAL_REGISTERS.value,
            regs,
        )
        call_injection(
            container,
            AdminActionType.INJECT_SUBMISSIONS.value,
            StatisticsName.TOTAL_SUBMISSIONS_ON_QUALIFICATION_TASK.value,
            submissions,
        )


st.header("学分发放记录下载", divider="rainbow")
# credit_hist = get_credit_history()
# credit_history = pd.DataFrame(credit_hist)
competitions = get_competitions()["data"]
competition_options = {
    comp["competition_id"]: comp["competition_name"] for comp in competitions
}
competition_options["all"] = "全部比赛"


utils = st.container()
selectBox, downloadButton = utils.columns(2)

competition_id = selectBox.selectbox(
    "选择某个比赛",
    options=list(competition_options.keys()) + ["all"],
    format_func=lambda x: competition_options[x],
    index=len(competition_options.keys()) - 1,
    placeholder="选择某个比赛，如需下载全部比赛，则留空",
    label_visibility="collapsed",
)
sign = str(ObjectId())
downloads = downloadButton.download_button(
    label="下载",
    data=handle_download(competition_id),
    file_name="credit_history.csv",
    mime="text/csv",
    use_container_width=True,
)


@st.experimental_fragment
def inject_data_setup():
    injection = st.container()
    injection.header("数据注入", divider="rainbow")
    injection.subheader("报名数据", help="数据为负代表减去 x 人/次")
    regs = injection.number_input("报名人次", step=10, value=0)
    injection.subheader("通关任务提交数")
    submissions = injection.number_input("通关提交数", step=10, value=0)
    subsmision_button = injection.button(
        "注入数据",
        help="注入数据",
        on_click=lambda: handle_injection(injection, regs, submissions),
        use_container_width=True,
    )


inject_data_setup()
