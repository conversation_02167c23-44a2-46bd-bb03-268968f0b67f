/**
 * Vue Composable for Feature Flags
 * Provides reactive feature flag functionality for Vue components
 */

import { ref, computed, onMounted, onUnmounted } from 'vue'
import { 
  isFeatureEnabled, 
  enableFeature, 
  disableFeature, 
  toggleFeature,
  getAllFeatureFlags,
  addFeatureFlagListener,
  removeFeatureFlagListener,
  FEATURE_FLAGS
} from '@/utils/featureFlags.js'

/**
 * Use feature flags composable
 * @returns {Object} Feature flag utilities
 */
export function useFeatureFlags() {
  // Reactive feature flags state
  const flags = ref(getAllFeatureFlags())
  
  // Update flags when they change
  const updateFlags = () => {
    flags.value = getAllFeatureFlags()
  }

  // Set up listeners for all flags
  onMounted(() => {
    Object.values(FEATURE_FLAGS).forEach(flag => {
      addFeatureFlagListener(flag, updateFlags)
    })
  })

  // Clean up listeners
  onUnmounted(() => {
    Object.values(FEATURE_FLAGS).forEach(flag => {
      removeFeatureFlagListener(flag, updateFlags)
    })
  })

  /**
   * Check if a feature is enabled (reactive)
   * @param {string} flag - Feature flag name
   * @returns {ComputedRef<boolean>} Reactive feature flag state
   */
  const isEnabled = (flag) => {
    return computed(() => flags.value[flag] || false)
  }

  /**
   * Enable a feature
   * @param {string} flag - Feature flag name
   */
  const enable = (flag) => {
    enableFeature(flag)
  }

  /**
   * Disable a feature
   * @param {string} flag - Feature flag name
   */
  const disable = (flag) => {
    disableFeature(flag)
  }

  /**
   * Toggle a feature
   * @param {string} flag - Feature flag name
   */
  const toggle = (flag) => {
    toggleFeature(flag)
  }

  /**
   * Get all flags (reactive)
   * @returns {ComputedRef<Object>} All feature flags
   */
  const allFlags = computed(() => flags.value)

  return {
    // State
    flags: allFlags,
    
    // Methods
    isEnabled,
    enable,
    disable,
    toggle,
    
    // Constants
    FEATURE_FLAGS
  }
}

/**
 * Use specific feature flag
 * @param {string} flag - Feature flag name
 * @returns {Object} Specific feature flag utilities
 */
export function useFeatureFlag(flag) {
  const { isEnabled, enable, disable, toggle } = useFeatureFlags()
  
  return {
    isEnabled: isEnabled(flag),
    enable: () => enable(flag),
    disable: () => disable(flag),
    toggle: () => toggle(flag)
  }
}

/**
 * Use API switching feature flags
 * @returns {Object} API-related feature flags
 */
export function useApiFeatureFlags() {
  const { isEnabled } = useFeatureFlags()
  
  return {
    useRealApi: isEnabled(FEATURE_FLAGS.REAL_API),
    useMockApi: isEnabled(FEATURE_FLAGS.MOCK_API),
    useApiCaching: isEnabled(FEATURE_FLAGS.API_CACHING),
    useApiRetry: isEnabled(FEATURE_FLAGS.API_RETRY)
  }
}

/**
 * Use UI feature flags
 * @returns {Object} UI-related feature flags
 */
export function useUIFeatureFlags() {
  const { isEnabled } = useFeatureFlags()
  
  return {
    darkMode: isEnabled(FEATURE_FLAGS.DARK_MODE),
    advancedSearch: isEnabled(FEATURE_FLAGS.ADVANCED_SEARCH),
    bulkOperations: isEnabled(FEATURE_FLAGS.BULK_OPERATIONS),
    exportFeatures: isEnabled(FEATURE_FLAGS.EXPORT_FEATURES),
    lazyLoading: isEnabled(FEATURE_FLAGS.LAZY_LOADING),
    virtualScrolling: isEnabled(FEATURE_FLAGS.VIRTUAL_SCROLLING),
    imageOptimization: isEnabled(FEATURE_FLAGS.IMAGE_OPTIMIZATION)
  }
}

/**
 * Use core feature flags
 * @returns {Object} Core feature flags
 */
export function useCoreFeatureFlags() {
  const { isEnabled } = useFeatureFlags()
  
  return {
    analytics: isEnabled(FEATURE_FLAGS.ANALYTICS),
    competitions: isEnabled(FEATURE_FLAGS.COMPETITIONS),
    credits: isEnabled(FEATURE_FLAGS.CREDITS),
    schools: isEnabled(FEATURE_FLAGS.SCHOOLS)
  }
}

/**
 * Use experimental feature flags
 * @returns {Object} Experimental feature flags
 */
export function useExperimentalFeatureFlags() {
  const { isEnabled } = useFeatureFlags()
  
  return {
    newDashboard: isEnabled(FEATURE_FLAGS.NEW_DASHBOARD),
    betaFeatures: isEnabled(FEATURE_FLAGS.BETA_FEATURES),
    experimentalUi: isEnabled(FEATURE_FLAGS.EXPERIMENTAL_UI)
  }
}

/**
 * Feature flag directive for v-if usage
 * Usage: v-if="$featureFlag('analytics')"
 */
export const featureFlagDirective = {
  mounted(el, binding) {
    const flag = binding.value
    if (!isFeatureEnabled(flag)) {
      el.style.display = 'none'
    }
  },
  updated(el, binding) {
    const flag = binding.value
    if (!isFeatureEnabled(flag)) {
      el.style.display = 'none'
    } else {
      el.style.display = ''
    }
  }
}

export default useFeatureFlags
