import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'
import router from './router'
import { initializeAuth } from './utils/authMiddleware'
import { initializeApiServices } from './services/api/index.js'

const app = createApp(App)

// Register Element Plus icons
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(createPinia())
app.use(router)
app.use(ElementPlus)

// Initialize services before mounting
const initializeApp = async () => {
  try {
    console.log('🚀 Initializing Community Services Admin...')

    // Initialize API services
    console.log('📡 Initializing API services...')
    const apiConfig = initializeApiServices()
    console.log('📡 API services initialized:', apiConfig)

    // Initialize authentication
    console.log('🔐 Initializing authentication...')
    await initializeAuth()
    console.log('🔐 Authentication initialized')

    console.log('✅ Application initialization complete')

    // Mount the app
    app.mount('#app')
    console.log('🎯 App mounted successfully')
  } catch (error) {
    console.error('❌ Application initialization failed:', error)
    console.error('Error details:', error.message)
    console.error('Stack trace:', error.stack)

    // Create error display
    document.getElementById('app').innerHTML = `
      <div style="padding: 20px; background: #fee; border: 1px solid #fcc; margin: 20px;">
        <h2>Application Initialization Failed</h2>
        <p><strong>Error:</strong> ${error.message}</p>
        <details>
          <summary>Stack Trace</summary>
          <pre>${error.stack}</pre>
        </details>
      </div>
    `

    // Mount the app anyway to show error state
    app.mount('#app')
  }
}

// Start the application
console.log('🎬 Starting application...')
initializeApp()
