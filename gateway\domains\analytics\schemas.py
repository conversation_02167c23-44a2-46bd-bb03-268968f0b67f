"""
Analytics Domain Schemas.

Request and response models for analytics endpoints including:
- Rankings (school and user)
- Statistics
- Event tracking
- ShenCe analytics integration
"""

from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field, model_validator
from datetime import datetime

# ——— Import shared base schemas ———
from libs.schemas.api.base import BaseResp


# ——— Migrated from shared/schemas/api/rank.py ———


class TopNByRoute(BaseModel):
    top_num: int = Field(gte=1, description="topN数量")


class TopNByRouteData(BaseModel):
    route_name: str = Field(description="赛道名称")
    user_ids: list[str] = Field(description="用户id列表")
    user_names: list[str] = Field(description="用户名称列表")
    total_credits: list[float] = Field(description="用户积分列表", ge=0.0)

    @model_validator(mode="after")
    def check_same_length(self) -> "TopNByRouteData":
        user_ids = self.user_ids
        user_names = self.user_names
        total_credits = self.total_credits

        if user_ids is None or user_names is None or total_credits is None:
            raise ValueError(
                "user_ids, user_names, and total_credits must all be provided"
            )

        if not (len(user_ids) == len(user_names) == len(total_credits)):
            raise ValueError(
                "user_ids, user_names, and total_credits must have the same length"
            )
        return self


class TopNByRouteResp(BaseResp):
    data: List[TopNByRouteData]


class SchoolRanking(BaseModel):
    page_size: int = Field(ge=1, description="返回数量")
    current_page: int = Field(default=1, ge=1, description="当前页数, 从1开始")


class SchoolRankingElement(BaseModel):
    university: str = Field(description="学校名称")
    total_credits: float = Field(description="总积分", ge=0.0)


class SchoolRankingData(BaseModel):
    total: int = Field(description="数据总数")
    page_size: int = Field(description="返回数量")
    current_page: int = Field(description="当前页数, 从1开始")
    res: List[SchoolRankingElement]


class SchoolRankingResp(BaseResp):
    data: SchoolRankingData


class UserRankingEveryRoute(BaseModel):
    top_num: int = Field(default=30, gte=1, description="topN数量")


class UserRankingEveryRouteElement(BaseModel):
    user_id: str = Field(description="用户id")
    user_name: str = Field(description="用户名称")
    total_credits: float = Field(description="用户积分", ge=0.0)


class UserRankingEveryRouteEachData(BaseModel):
    route_name: str = Field(description="赛道名称")
    ranking: List[UserRankingEveryRouteElement]


class UserRankingEveryRouteResp(BaseResp):
    data: List[UserRankingEveryRouteEachData]


class UserRankingTotalRoute(BaseModel):
    page_size: int = Field(ge=1, description="返回数量")
    current_page: int = Field(default=1, ge=1, description="当前页数, 从1开始")


class UserRankingTotalRouteElement(BaseModel):
    route_name: str = Field(description="赛道名称")
    credit: float = Field(description="积分", ge=0.0)


class UserRankingTotalRouteEachData(BaseModel):
    user_id: str = Field(description="用户id")
    user_name: str = Field(description="用户名称")
    credits_each: List[UserRankingTotalRouteElement] = Field(description="各赛道的积分")
    total_credits: float = Field(description="总积分", ge=0.0)
    max_credits: float = Field(description="最高积分", ge=0.0)


class UserRankingTotalRouteData(BaseModel):
    total: int = Field(description="数据总数")
    page_size: int = Field(description="返回数量")
    current_page: int = Field(description="当前页数, 从1开始")
    res: List[UserRankingTotalRouteEachData]


class UserRankingTotalRouteResp(BaseResp):
    data: UserRankingTotalRouteData


class SummaryStatistics(BaseModel):
    statistics_name: str = Field(description="统计指标名称")
    statistics_value: int = Field(default=0, description="统计指标值")


class SummaryStatisticsResp(BaseResp):
    data: List[SummaryStatistics]


# ——— Original domain schemas ———


class AnalyticsHealthResponse(BaseModel):
    """Health check response for analytics services."""

    status: str = Field(description="Service status")
    rankings_available: bool = Field(description="Rankings service availability")
    statistics_available: bool = Field(description="Statistics service availability")
    event_tracking_available: bool = Field(
        description="Event tracking service availability"
    )


class RankingFilters(BaseModel):
    """Common filters for ranking queries."""

    route_id: str = Field(None, description="Filter by specific route")
    university: str = Field(None, description="Filter by university")
    date_from: str = Field(None, description="Start date for filtering (YYYY-MM-DD)")
    date_to: str = Field(None, description="End date for filtering (YYYY-MM-DD)")


class EventTrackingRequest(BaseModel):
    """Request model for event tracking operations."""

    event_type: str = Field(description="Type of event to track")
    user_id: str = Field(None, description="User ID associated with the event")
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional event metadata"
    )


# ——— ShenCe Integration Schemas ———


class ShenceTagCreateRequest(BaseModel):
    """Request schema for creating ShenCe analytics tags."""

    tag_name: str = Field(..., description="Name of the tag to create")
    tag_id: Optional[str] = Field(None, description="Existing tag ID to update")
    dir_id: str = Field(..., description="Directory ID for the tag")
    data: List[Dict[str, Any]] = Field(..., description="User data to upload")
    data_type: Optional[str] = Field("STRING", description="Data type for the tag")


class ShenceFileUploadRequest(BaseModel):
    """Request schema for ShenCe file uploads."""

    file_name_prefix: str = Field(..., description="Prefix for the uploaded file")
    csv_data: List[Dict[str, Any]] = Field(
        ..., description="CSV data as list of dictionaries"
    )
    project: Optional[str] = Field(None, description="ShenCe project name")


class ShenceTagUpdateRequest(BaseModel):
    """Request schema for updating ShenCe analytics tags."""

    file_name: str = Field(..., description="Name of the uploaded file")
    tag_name: str = Field(..., description="Name of the tag")
    tag_id: Optional[str] = Field(None, description="Existing tag ID")
    dir_id: str = Field(..., description="Directory ID")
    data_type: Optional[str] = Field("STRING", description="Data type")
    task_name: Optional[str] = Field(None, description="Task name for the update")


class ShenceDirectoryResponse(BaseModel):
    """Response schema for ShenCe directory metadata."""

    directories: List[Dict[str, Any]]
    total_count: int
    project: str


class ShenceTagMetaResponse(BaseModel):
    """Response schema for ShenCe tag metadata."""

    tag_id: str
    name: str
    cname: str
    data_type: str
    dir_id: str
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class ShenceUploadResponse(BaseModel):
    """Response schema for ShenCe file uploads."""

    success: bool
    file_name: str
    file_size: int
    upload_id: Optional[str] = None
    message: Optional[str] = None


class ShenceTagResponse(BaseModel):
    """Response schema for ShenCe tag operations."""

    success: bool
    tag_id: Optional[str] = None
    file_name: Optional[str] = None
    upload_response: Optional[Dict[str, Any]] = None
    update_response: Optional[Dict[str, Any]] = None
    message: Optional[str] = None


# ——— Export all schemas ———
__all__ = [
    # Migrated ranking request models
    "SchoolRanking",
    "UserRankingEveryRoute",
    "UserRankingTotalRoute",
    "TopNByRoute",
    # Migrated ranking response models
    "SchoolRankingResp",
    "UserRankingEveryRouteResp",
    "UserRankingTotalRouteResp",
    "TopNByRouteResp",
    "SummaryStatisticsResp",
    # Migrated data models
    "SummaryStatistics",
    "SchoolRankingData",
    "UserRankingEveryRouteEachData",
    "UserRankingTotalRouteData",
    "TopNByRouteData",
    "SchoolRankingElement",
    "UserRankingEveryRouteElement",
    "UserRankingTotalRouteElement",
    # Original domain models
    "AnalyticsHealthResponse",
    "RankingFilters",
    "EventTrackingRequest",
    # ShenCe integration models
    "ShenceTagCreateRequest",
    "ShenceFileUploadRequest",
    "ShenceTagUpdateRequest",
    "ShenceDirectoryResponse",
    "ShenceTagMetaResponse",
    "ShenceUploadResponse",
    "ShenceTagResponse",
]
