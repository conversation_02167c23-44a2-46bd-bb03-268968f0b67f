# Integrations API Documentation

## Overview

The Integrations API provides endpoints for file uploads, webhook management, health monitoring, and cross-integration synchronization. All endpoints are prefixed with `/api/v1/integrations`.

## Authentication

All endpoints require authentication and specific permission checks as noted per endpoint.

## Endpoints

### File Upload

#### Upload File
```http
POST /api/v1/integrations/files/upload
```

**Description**: Upload file for processing by integration services.

**Parameters**:
- `destination` (query, required): Upload destination
- `file` (form-data, required): File to upload

**Response**: `SuccessResponse` with file upload details

**Permissions**: Requires `file_upload_permission`

**Example Response**:
```json
{
  "success": true,
  "data": {
    "success": true,
    "file_id": "file_123",
    "file_name": "data.csv",
    "file_size": 1024,
    "upload_url": "https://storage.example.com/file_123",
    "status": "uploaded",
    "message": "File uploaded successfully"
  }
}
```

#### Process Uploaded File
```http
POST /api/v1/integrations/files/{file_id}/process
```

**Description**: Process an uploaded file with specified parameters.

**Parameters**:
- `file_id` (path, required): File ID to process

**Request Body**: `FileProcessingRequest`
```json
{
  "processing_type": "csv_analysis",
  "parameters": {
    "delimiter": ",",
    "encoding": "utf-8"
  },
  "callback_url": "https://example.com/callback"
}
```

**Response**: `SuccessResponse` with processing status

**Permissions**: Requires `file_upload_permission`

---

### Webhook Management

#### Execute Webhook
```http
POST /api/v1/integrations/webhooks/execute
```

**Description**: Execute a webhook with specified payload.

**Request Body**: `WebhookRequest`
```json
{
  "url": "https://example.com/webhook",
  "payload": {
    "event": "user_registered",
    "data": {"user_id": "123"}
  },
  "headers": {
    "Authorization": "Bearer token123"
  },
  "method": "POST",
  "retry_count": 3
}
```

**Response**: `SuccessResponse` with webhook execution results

**Permissions**: Requires `webhook_permission`

**Example Response**:
```json
{
  "success": true,
  "data": {
    "success": true,
    "webhook_id": "webhook_456",
    "status_code": 200,
    "response_body": "{\"status\": \"received\"}",
    "execution_time_ms": 150,
    "retry_count": 0
  }
}
```

---

### Health Monitoring

#### Check Integration Health
```http
GET /api/v1/integrations/health
```

**Description**: Check health status of integration services.

**Parameters**:
- `integration_type` (query, optional): Specific integration to check
- `include_details` (query, optional): Include detailed health information (default: false)

**Response**: `SuccessResponse` with health status

**Permissions**: Requires `integration_health_permission`

**Example Response**:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "services": {
      "shence": {"status": "healthy"},
      "heywhale": {"status": "healthy"}
    },
    "checked_at": "2023-01-01T12:00:00Z"
  }
}
```

---

### Synchronization

#### Manual Integration Sync
```http
POST /api/v1/integrations/sync
```

**Description**: Manually trigger synchronization for integration services.

**Request Body**:
```json
{
  "integration_type": "heywhale"
}
```

**Response**: `SuccessResponse` with sync status

**Permissions**: Requires `integration_access`

**Example Response**:
```json
{
  "success": true,
  "data": {
    "sync_id": "sync_20230101_120000",
    "integration_type": "heywhale",
    "status": "pending",
    "started_at": "2023-01-01T12:00:00Z",
    "message": "Sync started for heywhale"
  }
}
```

#### Get Sync History
```http
GET /api/v1/integrations/sync/history
```

**Description**: Get synchronization history for integration services.

**Parameters**:
- `integration_type` (query, optional): Filter by integration type
- `limit` (query, optional): Number of records to return (1-100, default: 20)
- `offset` (query, optional): Number of records to skip (default: 0)

**Response**: `SuccessResponse` with sync history

**Permissions**: Requires `integration_access`

**Example Response**:
```json
{
  "success": true,
  "data": {
    "sync_records": [],
    "total_count": 0,
    "success_rate": 100.0,
    "last_sync": null
  }
}
```

---

### Configuration

#### Get Integration Configuration
```http
GET /api/v1/integrations/config/{integration_type}
```

**Description**: Get configuration for a specific integration.

**Parameters**:
- `integration_type` (path, required): Integration type to get config for

**Response**: `SuccessResponse` with configuration

**Permissions**: Requires `integration_access`

**Example Response**:
```json
{
  "success": true,
  "data": {
    "integration_type": "heywhale",
    "is_enabled": true,
    "configuration": {},
    "last_updated": "2023-01-01T12:00:00Z",
    "health_status": "healthy"
  }
}
```

---

### Legacy Endpoints

#### Legacy Badge Sync (Deprecated)
```http
POST /api/v1/integrations/legacy/badges/sync
```

**Status**: Deprecated - Returns 410 Gone

**Replacement**: Use `/api/v1/users/integrations/heywhale/badges/sync` instead

## Data Models

### File Processing Request
```json
{
  "processing_type": "string",
  "parameters": {},
  "callback_url": "https://example.com/callback"
}
```

### Webhook Request
```json
{
  "url": "https://example.com/webhook",
  "payload": {},
  "headers": {},
  "method": "POST",
  "retry_count": 0
}
```

### File Upload Response
```json
{
  "success": true,
  "file_id": "string",
  "file_name": "string",
  "file_size": 1024,
  "upload_url": "string",
  "status": "uploaded",
  "message": "string"
}
```

### Webhook Response
```json
{
  "success": true,
  "webhook_id": "string",
  "status_code": 200,
  "response_body": "string",
  "execution_time_ms": 150,
  "retry_count": 0
}
```

## Error Responses

All endpoints may return standard error responses:
- `400 Bad Request`: Invalid parameters or request body
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `410 Gone`: Endpoint deprecated/moved
- `500 Internal Server Error`: Server error
- `503 Service Unavailable`: Integration service unavailable

## Usage Examples

### Frontend Integration

#### Upload and Process File
```javascript
// Upload file
const formData = new FormData();
formData.append('file', fileInput.files[0]);

const uploadResponse = await fetch('/api/v1/integrations/files/upload?destination=analytics', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer <token>'
  },
  body: formData
});

const uploadData = await uploadResponse.json();
const fileId = uploadData.data.file_id;

// Process file
const processResponse = await fetch(`/api/v1/integrations/files/${fileId}/process`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer <token>'
  },
  body: JSON.stringify({
    processing_type: 'csv_analysis',
    parameters: { delimiter: ',' }
  })
});
```

#### Execute Webhook
```javascript
const response = await fetch('/api/v1/integrations/webhooks/execute', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer <token>'
  },
  body: JSON.stringify({
    url: 'https://external-service.com/webhook',
    payload: { event: 'data_updated', timestamp: Date.now() },
    method: 'POST'
  })
});
```

#### Check Service Health
```javascript
const health = await fetch('/api/v1/integrations/health?include_details=true');
const healthData = await health.json();
```

## Notes for Frontend Development

1. **File Uploads**: Use FormData for file uploads, not JSON
2. **Webhook Validation**: URLs are validated before execution
3. **Health Monitoring**: Check service health before making integration calls
4. **Error Handling**: Handle service unavailability gracefully
5. **Permissions**: Different operations require different permission levels
6. **Legacy Endpoints**: Avoid deprecated endpoints, use domain-specific alternatives
7. **Sync Operations**: Monitor sync status for long-running operations

## Related APIs

- **Users API**: For HeyWhale badge and tag integration
- **Analytics API**: For ShenCe analytics integration
- **Camp Admin API**: For administrative integration operations
