/**
 * Credit-related TypeScript interfaces
 */

import { BaseEntity, User } from './common'

// Credit types
export interface Credit extends BaseEntity {
  amount: number
  type: CreditType
  source: CreditSource
  status: CreditStatus
  
  // Recipients
  userId: string
  user: User
  
  // Context
  reason: string
  description?: string
  reference?: CreditReference
  
  // Metadata
  issuedBy: User
  issuedAt: string
  expiresAt?: string
  
  // Audit
  auditLog: CreditAuditEntry[]
}

export type CreditType = 'reward' | 'bonus' | 'penalty' | 'adjustment' | 'refund'
export type CreditSource = 'competition' | 'manual' | 'system' | 'promotion' | 'referral'
export type CreditStatus = 'pending' | 'approved' | 'issued' | 'expired' | 'cancelled'

export interface CreditReference {
  type: 'competition' | 'submission' | 'achievement' | 'manual'
  id: string
  title?: string
}

export interface CreditAuditEntry {
  id: string
  action: CreditAuditAction
  performedBy: User
  performedAt: string
  details: Record<string, any>
  comment?: string
}

export type CreditAuditAction = 'created' | 'approved' | 'issued' | 'cancelled' | 'expired' | 'modified'

// Credit rules and policies
export interface CreditRule extends BaseEntity {
  name: string
  description: string
  type: CreditRuleType
  trigger: CreditTrigger
  amount: number | CreditAmountFormula
  conditions: CreditCondition[]
  isActive: boolean
  priority: number
  
  // Limits
  maxPerUser?: number
  maxPerPeriod?: number
  period?: CreditPeriod
  
  // Validity
  validFrom: string
  validTo?: string
  
  // Metadata
  createdBy: User
  tags: string[]
}

export type CreditRuleType = 'automatic' | 'manual' | 'conditional'
export type CreditPeriod = 'daily' | 'weekly' | 'monthly' | 'yearly'

export interface CreditTrigger {
  event: CreditTriggerEvent
  conditions: Record<string, any>
}

export type CreditTriggerEvent = 
  | 'competition_win' 
  | 'competition_participation' 
  | 'submission_approved' 
  | 'achievement_unlocked'
  | 'referral_signup'
  | 'manual_award'

export interface CreditAmountFormula {
  base: number
  multipliers?: Array<{
    condition: string
    factor: number
  }>
  bonuses?: Array<{
    condition: string
    amount: number
  }>
}

export interface CreditCondition {
  field: string
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'in'
  value: any
}

// Credit transactions and history
export interface CreditTransaction extends BaseEntity {
  userId: string
  user: User
  amount: number
  type: CreditTransactionType
  status: CreditTransactionStatus
  
  // Context
  description: string
  reference?: CreditReference
  
  // Balance tracking
  balanceBefore: number
  balanceAfter: number
  
  // Processing
  processedAt?: string
  processedBy?: User
  
  // Metadata
  metadata: Record<string, any>
}

export type CreditTransactionType = 'credit' | 'debit' | 'transfer' | 'adjustment'
export type CreditTransactionStatus = 'pending' | 'completed' | 'failed' | 'cancelled'

export interface CreditBalance {
  userId: string
  totalCredits: number
  availableCredits: number
  pendingCredits: number
  expiredCredits: number
  lastUpdated: string
}

// Credit distribution and bulk operations
export interface CreditDistribution extends BaseEntity {
  name: string
  description: string
  type: CreditDistributionType
  status: CreditDistributionStatus
  
  // Recipients
  recipients: CreditRecipient[]
  totalRecipients: number
  
  // Credits
  creditAmount: number
  totalCredits: number
  
  // Processing
  scheduledAt?: string
  startedAt?: string
  completedAt?: string
  
  // Results
  successCount: number
  failureCount: number
  errors: CreditDistributionError[]
  
  // Metadata
  createdBy: User
  approvedBy?: User
}

export type CreditDistributionType = 'bulk_award' | 'competition_rewards' | 'promotional' | 'adjustment'
export type CreditDistributionStatus = 'draft' | 'pending_approval' | 'approved' | 'processing' | 'completed' | 'failed' | 'cancelled'

export interface CreditRecipient {
  userId: string
  user: User
  amount: number
  status: CreditRecipientStatus
  reason?: string
  processedAt?: string
  error?: string
}

export type CreditRecipientStatus = 'pending' | 'processed' | 'failed' | 'skipped'

export interface CreditDistributionError {
  userId: string
  error: string
  details?: Record<string, any>
}

// Form types
export interface CreditFormData {
  userId: string
  amount: number
  type: CreditType
  source: CreditSource
  reason: string
  description?: string
  expiresAt?: string
  reference?: CreditReference
}

export interface CreditRuleFormData {
  name: string
  description: string
  type: CreditRuleType
  trigger: CreditTrigger
  amount: number
  conditions: CreditCondition[]
  maxPerUser?: number
  maxPerPeriod?: number
  period?: CreditPeriod
  validFrom: string
  validTo?: string
  tags: string[]
}

export interface BulkCreditFormData {
  name: string
  description: string
  type: CreditDistributionType
  creditAmount: number
  recipients: Array<{
    userId: string
    amount?: number
    reason?: string
  }>
  scheduledAt?: string
}

// API request/response types
export interface CreditListParams {
  page?: number
  limit?: number
  userId?: string
  type?: CreditType
  source?: CreditSource
  status?: CreditStatus
  dateRange?: {
    start: string
    end: string
  }
  search?: string
}

export interface CreditStatsResponse {
  totalIssued: number
  totalPending: number
  totalExpired: number
  averageAmount: number
  distributionByType: Record<CreditType, number>
  distributionBySource: Record<CreditSource, number>
  monthlyTrend: Array<{
    month: string
    issued: number
    amount: number
  }>
}
