"""
Base classes for the Admin layer.

Provides transaction support, audit logging, and common CRUD operations.
"""

from typing import Any, Dict, Optional, TypeVar
from datetime import datetime
from contextlib import asynccontextmanager
import logging

from motor.motor_asyncio import AsyncIOMotorDatabase, AsyncIOMotorClientSession
from core.database.mongo_db import get_mongo_database
from libs.errors import ServiceResult
from libs.auth.context import AuthContext, require_auth
from libs.errors import AdminError

logger = logging.getLogger(__name__)

T = TypeVar("T")


class AuditLog:
    """Structure for audit log entries."""

    def __init__(
        self,
        user_id: str,
        operation: str,
        resource: str,
        resource_id: str,
        details: Optional[Dict[str, Any]] = None,
    ):
        """Initialize audit log entry."""
        self.user_id = user_id
        self.operation = operation  # CREATE, UPDATE, DELETE
        self.resource = resource  # users, competitions, credits, etc.
        self.resource_id = resource_id
        self.details = details or {}
        self.timestamp = datetime.now()

    def to_dict(self) -> Dict[str, Any]:
        """Convert audit log to dictionary for storage."""
        return {
            "user_id": self.user_id,
            "operation": self.operation,
            "resource": self.resource,
            "resource_id": self.resource_id,
            "details": self.details,
            "timestamp": self.timestamp,
        }


class BaseAdmin:
    """
    Base class for all Admin layer services.

    Provides transaction support, audit logging, and common CRUD patterns.
    """

    def __init__(self, db: Optional[AsyncIOMotorDatabase] = None):
        """Initialize base admin with database connection."""
        self.db = db or get_mongo_database()
        self.service_name = self.__class__.__name__.lower().replace("admin", "")

        logger.debug(f"Initialized {self.__class__.__name__}")

    @asynccontextmanager
    async def transaction_context(self):
        """
        Context manager for MongoDB transactions.

        Provides automatic transaction management with rollback on errors.
        """
        client = self.db.client
        session = None

        try:
            session = await client.start_session()
            async with session.start_transaction():
                logger.debug(f"Started transaction for {self.service_name}")
                yield session
                logger.debug(f"Transaction committed for {self.service_name}")

        except Exception as e:
            logger.error(f"Transaction failed for {self.service_name}: {e}")
            if session:
                await session.abort_transaction()
            raise
        finally:
            if session:
                await session.end_session()

    async def _log_audit(
        self,
        auth_context: AuthContext,
        operation: str,
        resource: str,
        resource_id: str,
        details: Optional[Dict[str, Any]] = None,
        session: Optional[AsyncIOMotorClientSession] = None,
    ) -> None:
        """
        Log an audit entry for admin operations.

        Args:
            auth_context: Authentication context
            operation: Type of operation (CREATE, UPDATE, DELETE)
            resource: Resource type being modified
            resource_id: ID of the resource
            details: Additional details about the operation
            session: Optional MongoDB session for transaction
        """
        try:
            audit_log = AuditLog(
                user_id=auth_context.user_id,
                operation=operation,
                resource=resource,
                resource_id=resource_id,
                details=details,
            )

            collection = self.db["audit_logs"]

            if session:
                await collection.insert_one(audit_log.to_dict(), session=session)
            else:
                await collection.insert_one(audit_log.to_dict())

            logger.info(
                f"Audit logged: {operation} {resource} {resource_id} by {auth_context.user_id}"
            )

        except Exception as e:
            logger.error(f"Failed to log audit entry: {e}")
            # Don't fail the main operation due to audit logging failure

    @require_auth
    async def create_resource(
        self,
        auth_context: AuthContext,
        collection_name: str,
        data: Dict[str, Any],
        resource_id: Optional[str] = None,
    ) -> ServiceResult[Dict[str, Any]]:
        """
        Create a new resource with audit logging.

        Args:
            auth_context: Authentication context
            collection_name: Name of the collection
            data: Data for the new resource
            resource_id: Optional custom resource ID

        Returns:
            ServiceResult with the created resource
        """
        try:
            async with self.transaction_context() as session:
                # Prepare document
                document = data.copy()
                if resource_id:
                    document["_id"] = resource_id
                document["created_at"] = datetime.now()
                document["created_by"] = auth_context.user_id

                # Insert document
                collection = self.db[collection_name]
                result = await collection.insert_one(document, session=session)

                # Log audit
                final_id = resource_id or str(result.inserted_id)
                await self._log_audit(
                    auth_context,
                    "CREATE",
                    collection_name,
                    final_id,
                    {"data": data},
                    session,
                )

                # Return created document
                document["_id"] = final_id
                return ServiceResult.success(document)

        except Exception as e:
            logger.error(f"Failed to create {collection_name}: {e}")
            return AdminError.transaction_failed(f"create {collection_name}")

    @require_auth
    async def update_resource(
        self,
        auth_context: AuthContext,
        collection_name: str,
        resource_id: str,
        updates: Dict[str, Any],
    ) -> ServiceResult[Dict[str, Any]]:
        """
        Update an existing resource with audit logging.

        Args:
            auth_context: Authentication context
            collection_name: Name of the collection
            resource_id: ID of the resource to update
            updates: Updates to apply

        Returns:
            ServiceResult with the updated resource
        """
        try:
            async with self.transaction_context() as session:
                collection = self.db[collection_name]

                # Get current document
                current_doc = await collection.find_one(
                    {"_id": resource_id}, session=session
                )
                if not current_doc:
                    return AdminError.validation_failed(
                        "resource_id", "Resource not found"
                    )

                # Prepare updates
                update_data = updates.copy()
                update_data["updated_at"] = datetime.now()
                update_data["updated_by"] = auth_context.user_id

                # Update document
                result = await collection.update_one(
                    {"_id": resource_id}, {"$set": update_data}, session=session
                )

                if result.modified_count == 0:
                    return AdminError.validation_failed("updates", "No changes made")

                # Log audit
                await self._log_audit(
                    auth_context,
                    "UPDATE",
                    collection_name,
                    resource_id,
                    {"updates": updates, "previous": current_doc},
                    session,
                )

                # Return updated document
                updated_doc = await collection.find_one(
                    {"_id": resource_id}, session=session
                )
                return ServiceResult.success(updated_doc)

        except Exception as e:
            logger.error(f"Failed to update {collection_name} {resource_id}: {e}")
            return AdminError.transaction_failed(f"update {collection_name}")

    @require_auth
    async def delete_resource(
        self,
        auth_context: AuthContext,
        collection_name: str,
        resource_id: str,
        soft_delete: bool = True,
    ) -> ServiceResult[bool]:
        """
        Delete a resource with audit logging.

        Args:
            auth_context: Authentication context
            collection_name: Name of the collection
            resource_id: ID of the resource to delete
            soft_delete: Whether to soft delete (mark as deleted) or hard delete

        Returns:
            ServiceResult with success status
        """
        try:
            async with self.transaction_context() as session:
                collection = self.db[collection_name]

                # Get current document
                current_doc = await collection.find_one(
                    {"_id": resource_id}, session=session
                )
                if not current_doc:
                    return AdminError.validation_failed(
                        "resource_id", "Resource not found"
                    )

                if soft_delete:
                    # Soft delete - mark as deleted
                    result = await collection.update_one(
                        {"_id": resource_id},
                        {
                            "$set": {
                                "deleted_at": datetime.now(),
                                "deleted_by": auth_context.user_id,
                                "is_deleted": True,
                            }
                        },
                        session=session,
                    )
                    success = result.modified_count > 0
                else:
                    # Hard delete - remove document
                    result = await collection.delete_one(
                        {"_id": resource_id}, session=session
                    )
                    success = result.deleted_count > 0

                if not success:
                    return AdminError.transaction_failed(f"delete {collection_name}")

                # Log audit
                await self._log_audit(
                    auth_context,
                    "DELETE",
                    collection_name,
                    resource_id,
                    {"soft_delete": soft_delete, "document": current_doc},
                    session,
                )

                return ServiceResult.success(True)

        except Exception as e:
            logger.error(f"Failed to delete {collection_name} {resource_id}: {e}")
            return AdminError.transaction_failed(f"delete {collection_name}")

    async def health_check(self) -> ServiceResult[Dict[str, Any]]:
        """
        Check the health of the admin service.

        Returns:
            ServiceResult with health status information
        """
        try:
            # Test database connection
            await self.db.command("ping")

            # Test transaction capability
            async with self.transaction_context():
                # Simple transaction test
                pass

            health_data = {
                "service": self.service_name,
                "status": "healthy",
                "database": "connected",
                "transactions": "supported",
                "timestamp": datetime.now().isoformat(),
            }

            return ServiceResult.success(health_data)

        except Exception as e:
            logger.error(f"Health check failed for {self.service_name}: {e}")
            return AdminError.transaction_failed(f"Health check failed: {str(e)}")
