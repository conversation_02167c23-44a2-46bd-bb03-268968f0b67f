<template>
  <div class="credit-management">
    <div class="page-header">
      <h1>学分管理</h1>
      <div class="header-actions">
        <el-button @click="loadData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button type="primary" @click="showAwardDialog">
          <el-icon><Plus /></el-icon>
          发放学分
        </el-button>
      </div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-cards" v-if="stats">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><CreditCard /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.totalIssued || 0 }}</div>
                <div class="stat-label">总发放人数</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon pending">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.totalPending || 0 }}</div>
                <div class="stat-label">Pending</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon revoked">
                <el-icon><Close /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.totalRevoked || 0 }}</div>
                <div class="stat-label">撤销数</div>
              </div>
            </div>
          </el-card>
        </el-col> -->

        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon amount">
                <el-icon><Money /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ formatNumber(stats.totalAmount) || 0 }}</div>
                <div class="stat-label">总发放学分数量</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- Filters and Search -->
    <el-card style="margin-top: 20px;">
      <div class="filters-section">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-input
              v-model="filters.search"
              placeholder="Search credits..."
              clearable
              @input="debouncedSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>

          <!-- <el-col :span="4">
            <el-select v-model="filters.status" placeholder="Status" clearable @change="loadCredits">
              <el-option label="All" value="" />
              <el-option label="Active" value="active" />
              <el-option label="Pending" value="pending" />
              <el-option label="Revoked" value="revoked" />
            </el-select>
          </el-col> -->

          <el-col :span="4">
            <el-input
              v-model="filters.userId"
              placeholder="用户 ID"
              clearable
              @input="debouncedSearch"
            />
          </el-col>

          <el-col :span="4">
            <el-input
              v-model="filters.competitionId"
              placeholder="活动 ID"
              clearable
              @input="debouncedSearch"
            />
          </el-col>

          <el-col :span="3">
            <el-select v-model="filters.sortBy" placeholder="排序" @change="loadCredits">
              <el-option label="发放日期" value="awardedAt" />
              <el-option label="发放数量" value="amount" />
              <el-option label="用户" value="userName" />
              <el-option label="活动" value="competitionName" />
            </el-select>
          </el-col>

          <el-col :span="3">
            <el-select v-model="filters.sortOrder" placeholder="排序方式" @change="loadCredits">
              <el-option label="降序" value="desc" />
              <el-option label="升序" value="asc" />
            </el-select>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- Credits Table -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>Credit History</span>
          <el-tag>{{ pagination.total }} total</el-tag>
        </div>
      </template>

      <!-- Loading State -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="8" animated />
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="error-container">
        <el-alert
          :title="error"
          type="error"
          show-icon
          :closable="false"
        />
        <el-button @click="loadCredits" type="primary" style="margin-top: 16px">
          Retry
        </el-button>
      </div>

      <!-- Credits Table -->
      <div v-else>
        <el-table
          :data="credits"
          style="width: 100%"
          v-loading="creditsLoading"
        >
          <el-table-column prop="userName" label="用户信息" min-width="150">
            <template #default="{ row }">
              <div class="user-cell">
                <div class="user-name">{{ row.userName || 'Unknown User' }}</div>
                <div class="user-id">ID: {{ row.userId }}</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="competitionName" label="活动信息" min-width="180">
            <template #default="{ row }">
              <div class="competition-cell">
                <div class="competition-name">{{ row.competitionName || 'Unknown Competition' }}</div>
                <div class="qualification-name">{{ row.qualificationName || 'Unknown Qualification' }}</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="credit" label="发放学分数量" width="120">
            <template #default="{ row }">
              <el-tag type="success" size="small">
                {{ row.displayCredit || `${row.credit || 0} credits` }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag
                :type="getStatusType(row.status)"
                size="small"
              >
                {{ row.displayStatus || row.status }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="awardedAt" label="发放日期" width="120">
            <template #default="{ row }">
              {{ formatDate(row.awardedAt) }}
            </template>
          </el-table-column>

          <el-table-column prop="remark" label="备注" min-width="150">
            <template #default="{ row }">
              <span class="remark-text">{{ row.remark || 'No remark' }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="viewCredit(row)"
              >
                View
              </el-button>
              <el-button
                v-if="row.isRevokable"
                type="danger"
                size="small"
                @click="revokeCredit(row)"
              >
                Revoke
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- Pagination -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="filters.page"
            v-model:page-size="filters.limit"
            :page-sizes="[20, 50, 100, 200]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadCredits"
            @current-change="loadCredits"
          />
        </div>
      </div>
    </el-card>

    <!-- Award Credits Dialog -->
    <el-dialog
      v-model="awardDialogVisible"
      title="Award Credits"
      width="600px"
      @close="resetAwardForm"
    >
      <el-form
        ref="awardFormRef"
        :model="awardForm"
        :rules="awardRules"
        label-width="120px"
      >
        <el-form-item label="User IDs" prop="userIds">
          <el-input
            v-model="awardForm.userIdsText"
            type="textarea"
            :rows="3"
            placeholder="Enter user IDs, one per line or comma-separated"
          />
          <div class="form-help">Enter multiple user IDs separated by commas or new lines</div>
        </el-form-item>

        <el-form-item label="Competition" prop="competitionId">
          <el-input
            v-model="awardForm.competitionId"
            placeholder="Enter competition ID"
          />
        </el-form-item>

        <el-form-item label="Qualification" prop="qualificationId">
          <el-input
            v-model="awardForm.qualificationId"
            placeholder="Enter qualification ID"
          />
        </el-form-item>

        <el-form-item label="Credit Amount" prop="credit">
          <el-input-number
            v-model="awardForm.credit"
            :min="1"
            :max="1000"
            placeholder="Enter credit amount"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="Remark" prop="remark">
          <el-input
            v-model="awardForm.remark"
            type="textarea"
            :rows="2"
            placeholder="Enter award remark"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="awardDialogVisible = false">Cancel</el-button>
          <el-button type="primary" @click="submitAward" :loading="awardLoading">
            Award Credits
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Refresh,
  Search,
  CreditCard,
  Clock,
  Close,
  Money
} from '@element-plus/icons-vue'
import { creditService } from '@/services/creditService'
import { debounce } from 'lodash-es'

// State
const loading = ref(true)
const creditsLoading = ref(false)
const awardLoading = ref(false)
const error = ref(null)
const credits = ref([])
const stats = ref(null)

// Dialog state
const awardDialogVisible = ref(false)
const awardFormRef = ref()

// Filters and pagination
const filters = reactive({
  page: 1,
  limit: 20,
  search: '',
  status: '',
  userId: '',
  competitionId: '',
  qualificationId: '',
  sortBy: 'awardedAt',
  sortOrder: 'desc'
})

const pagination = reactive({
  total: 0,
  pages: 0,
  hasNext: false,
  hasPrev: false
})

// Award form
const awardForm = reactive({
  userIdsText: '',
  competitionId: '',
  qualificationId: '',
  credit: 1,
  remark: ''
})

const awardRules = {
  userIdsText: [
    { required: true, message: 'Please enter user IDs', trigger: 'blur' }
  ],
  competitionId: [
    { required: true, message: 'Please enter competition ID', trigger: 'blur' }
  ],
  qualificationId: [
    { required: true, message: 'Please enter qualification ID', trigger: 'blur' }
  ],
  credit: [
    { required: true, message: 'Please enter credit amount', trigger: 'blur' },
    { type: 'number', min: 1, max: 1000, message: 'Credit amount must be between 1 and 1000', trigger: 'blur' }
  ]
}

// Computed
const userIds = computed(() => {
  if (!awardForm.userIdsText) return []
  return awardForm.userIdsText
    .split(/[,\n]/)
    .map(id => id.trim())
    .filter(id => id.length > 0)
})

// Methods
const formatNumber = (num) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatDate = (dateString) => {
  if (!dateString) return 'N/A'
  try {
    return new Date(dateString).toLocaleDateString()
  } catch (error) {
    return 'Invalid Date'
  }
}

const getStatusType = (status) => {
  const statusTypes = {
    'active': 'success',
    'pending': 'warning',
    'revoked': 'danger',
    'expired': 'info'
  }
  return statusTypes[status] || 'info'
}

const loadData = async () => {
  loading.value = true
  error.value = null

  try {
    // Load statistics
    const statsResponse = await creditService.getCreditStats()

    if (statsResponse.success) {
      stats.value = statsResponse.data
    }

    // Load credits
    await loadCredits()

  } catch (err) {
    console.error('Failed to load credit data:', err)
    error.value = err.message || 'Failed to load data'
    ElMessage.error(error.value)
  } finally {
    loading.value = false
  }
}

const loadCredits = async () => {
  creditsLoading.value = true

  try {
    const response = await creditService.getCredits(filters)

    if (response.success) {
      credits.value = response.data
      pagination.total = response.pagination?.total || 0
      pagination.pages = response.pagination?.pages || 0
      pagination.hasNext = response.pagination?.hasNext || false
      pagination.hasPrev = response.pagination?.hasPrev || false
    } else {
      throw new Error(response.error?.message || 'Failed to load credits')
    }
  } catch (err) {
    console.error('Failed to load credits:', err)
    ElMessage.error(err.message || 'Failed to load credits')
  } finally {
    creditsLoading.value = false
  }
}

const viewCredit = (credit) => {
  ElMessageBox.alert(
    `
    <div>
      <p><strong>User:</strong> ${credit.userName} (${credit.userId})</p>
      <p><strong>Competition:</strong> ${credit.competitionName}</p>
      <p><strong>Qualification:</strong> ${credit.qualificationName}</p>
      <p><strong>Amount:</strong> ${credit.displayCredit}</p>
      <p><strong>Status:</strong> ${credit.displayStatus}</p>
      <p><strong>Awarded:</strong> ${formatDate(credit.awardedAt)}</p>
      <p><strong>Remark:</strong> ${credit.remark || 'No remark'}</p>
    </div>
    `,
    'Credit Details',
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: 'Close'
    }
  )
}

const revokeCredit = async (credit) => {
  try {
    await ElMessageBox.confirm(
      `Are you sure you want to revoke ${credit.displayCredit} from ${credit.userName}?`,
      'Revoke Credits',
      {
        type: 'warning',
        confirmButtonText: 'Revoke',
        cancelButtonText: 'Cancel'
      }
    )

    const response = await creditService.revokeCredits({
      recordId: credit.id
    })

    if (response.success) {
      ElMessage.success('Credits revoked successfully')
      await loadCredits()
      await loadData() // Refresh stats
    } else {
      throw new Error(response.error?.message || 'Failed to revoke credits')
    }
  } catch (err) {
    if (err !== 'cancel') {
      console.error('Failed to revoke credits:', err)
      ElMessage.error(err.message || 'Failed to revoke credits')
    }
  }
}

const showAwardDialog = () => {
  awardDialogVisible.value = true
}

const resetAwardForm = () => {
  awardForm.userIdsText = ''
  awardForm.competitionId = ''
  awardForm.qualificationId = ''
  awardForm.credit = 1
  awardForm.remark = ''

  if (awardFormRef.value) {
    awardFormRef.value.clearValidate()
  }
}

const submitAward = async () => {
  if (!awardFormRef.value) return

  try {
    const valid = await awardFormRef.value.validate()
    if (!valid) return

    awardLoading.value = true

    const awardData = {
      userIds: userIds.value,
      competitionId: awardForm.competitionId,
      qualificationId: awardForm.qualificationId,
      credit: awardForm.credit,
      remark: awardForm.remark
    }

    const response = await creditService.awardCredits(awardData)

    if (response.success) {
      ElMessage.success(`Credits awarded successfully to ${userIds.value.length} user(s)`)
      awardDialogVisible.value = false
      resetAwardForm()
      await loadCredits()
      await loadData() // Refresh stats
    } else {
      throw new Error(response.error?.message || 'Failed to award credits')
    }
  } catch (err) {
    console.error('Failed to award credits:', err)
    ElMessage.error(err.message || 'Failed to award credits')
  } finally {
    awardLoading.value = false
  }
}

// Debounced search
const debouncedSearch = debounce(() => {
  filters.page = 1 // Reset to first page
  loadCredits()
}, 500)

// Lifecycle
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  h1 {
    font-size: 28px;
    font-weight: 600;
    color: #2c3e50;
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.stats-cards {
  margin-bottom: 20px;

  .stat-card {
    .stat-content {
      display: flex;
      align-items: center;

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;

        .el-icon {
          font-size: 24px;
          color: white;
        }

        &.total {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        &.pending {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        &.revoked {
          background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        }

        &.amount {
          background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
      }

      .stat-info {
        flex: 1;

        .stat-value {
          font-size: 24px;
          font-weight: 600;
          color: #2c3e50;
          line-height: 1;
        }

        .stat-label {
          font-size: 14px;
          color: #7f8c8d;
          margin-top: 4px;
        }
      }
    }
  }
}

.filters-section {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.loading-container,
.error-container {
  padding: 40px;
  text-align: center;
}

.user-cell {
  .user-name {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 4px;
  }

  .user-id {
    font-size: 12px;
    color: #7f8c8d;
  }
}

.competition-cell {
  .competition-name {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 4px;
  }

  .qualification-name {
    font-size: 12px;
    color: #7f8c8d;
  }
}

.remark-text {
  font-size: 14px;
  color: #495057;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.form-help {
  font-size: 12px;
  color: #7f8c8d;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
