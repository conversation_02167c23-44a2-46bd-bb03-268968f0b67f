/**
 * Deployment Configuration
 * Environment-specific deployment settings and build configurations
 */

export const deployConfig = {
  // Development Environment
  development: {
    name: 'Development',
    apiBaseUrl: '/api/v1',
    mockApi: true,
    debug: true,
    sourcemap: true,
    minify: false,
    outputDir: 'dist-dev',
    publicPath: '/',
    proxy: {
      '/api': {
        target: 'http://localhost:8005',
        changeOrigin: true
      }
    }
  },

  // Local Development Environment
  local: {
    name: 'Local',
    apiBaseUrl: 'http://localhost:8000/api/v1',
    mockApi: false,
    debug: true,
    sourcemap: true,
    minify: false,
    outputDir: 'dist-local',
    publicPath: '/',
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true
      }
    }
  },

  // Staging Environment
  staging: {
    name: 'Staging',
    apiBaseUrl: 'http://staging-community-workers:8000/api/v1',
    mockApi: false,
    debug: true,
    sourcemap: true,
    minify: true,
    outputDir: 'dist-staging',
    publicPath: '/admin/',
    cdn: {
      enabled: false,
      baseUrl: 'https://staging-cdn.example.com'
    },
    security: {
      csp: "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
      hsts: true
    }
  },

  // Production Environment
  production: {
    name: 'Production',
    apiBaseUrl: 'http://community-workers:8000/api/v1',
    mockApi: false,
    debug: false,
    sourcemap: false,
    minify: true,
    outputDir: 'dist',
    publicPath: '/admin/',
    cdn: {
      enabled: true,
      baseUrl: 'https://cdn.example.com'
    },
    security: {
      csp: "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'",
      hsts: true,
      httpsOnly: true
    },
    optimization: {
      splitChunks: true,
      compression: 'gzip',
      caching: true
    }
  },

  // Test Environment
  test: {
    name: 'Test',
    apiBaseUrl: '/api/v1',
    mockApi: true,
    debug: false,
    sourcemap: false,
    minify: false,
    outputDir: 'dist-test',
    publicPath: '/',
    testConfig: {
      timeout: 5000,
      retries: 1,
      headless: true
    }
  }
}

/**
 * Get deployment configuration for environment
 * @param {string} env - Environment name
 * @returns {Object} Deployment configuration
 */
export const getDeployConfig = (env = 'development') => {
  const config = deployConfig[env]
  if (!config) {
    throw new Error(`Unknown environment: ${env}`)
  }
  return config
}

/**
 * Get build configuration for environment
 * @param {string} env - Environment name
 * @returns {Object} Build configuration
 */
export const getBuildConfig = (env = 'development') => {
  const config = getDeployConfig(env)
  
  return {
    outDir: config.outputDir,
    sourcemap: config.sourcemap,
    minify: config.minify,
    rollupOptions: {
      output: {
        manualChunks: config.optimization?.splitChunks ? {
          vendor: ['vue', 'vue-router', 'pinia'],
          elementPlus: ['element-plus'],
          utils: ['axios', 'dayjs', 'js-cookie']
        } : undefined
      }
    }
  }
}

/**
 * Get server configuration for environment
 * @param {string} env - Environment name
 * @returns {Object} Server configuration
 */
export const getServerConfig = (env = 'development') => {
  const config = getDeployConfig(env)
  
  return {
    port: process.env.PORT || 3000,
    host: true,
    proxy: config.proxy || {}
  }
}

/**
 * Environment validation
 * @param {string} env - Environment name
 * @returns {boolean} Is valid environment
 */
export const isValidEnvironment = (env) => {
  return Object.keys(deployConfig).includes(env)
}

/**
 * Get all available environments
 * @returns {string[]} Array of environment names
 */
export const getAvailableEnvironments = () => {
  return Object.keys(deployConfig)
}

export default deployConfig
