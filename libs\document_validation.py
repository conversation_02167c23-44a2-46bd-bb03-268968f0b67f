from typing import Any, Dict, List, Optional, Type, TypeVar
from pydantic import BaseModel, ValidationError
from functools import wraps

ModelT = TypeVar("ModelT", bound=BaseModel)

class LazyDoc:
    """
    延迟“转换”，支持单独校验：
      - .validate() 只做校验，不保留实例
      - .transform() 真正生成并缓存 BaseModel 实例
      - __getattr__ 在首次访问属性时自动调用 transform()
    """
    def __init__(
        self,
        raw: Dict[str, Any],
        model_cls: Type[ModelT],
        alias_map: Optional[Dict[str, List[str]]] = None
    ):
        self._raw = raw
        self._model_cls = model_cls
        self._alias_map = alias_map or {}
        self._instance: Optional[ModelT] = None

    def _normalize(self) -> Dict[str, Any]:
        out = dict(self._raw)
        for target, aliases in self._alias_map.items():
            for a in aliases:
                if a in self._raw:
                    out[target] = self._raw[a]
                    break
        return out

    def validate(self) -> None:
        """
        仅做校验：规范别名后，尝试实例化，但不缓存实例。
        抛出 ValidationError 则说明数据不符合模型。
        """
        norm = self._normalize()
        try:
            self._model_cls(**norm)
        except ValidationError as e:
            # 可以在这里附加上下文信息后再抛出
            raise

    def transform(self) -> ModelT:
        """
        懒加载转换：在首次调用时实例化并缓存，后续复用。
        """
        if self._instance is None:
            norm = self._normalize()
            self._instance = self._model_cls(**norm)
        return self._instance

    def __getattr__(self, name: str) -> Any:
        # 访问任意属性时自动 transform()
        return getattr(self.transform(), name)

    def __repr__(self):
        return f"<LazyDoc(raw={self._raw})>"

def lazy_aggregate(
    model_cls: Type[ModelT],
    alias_map: Optional[Dict[str, List[str]]] = None
):
    """
    装饰器：包装返回 dict 可迭代的聚合函数，
    产出 LazyDoc 对象流。
    """
    def decorator(fn):
        @wraps(fn)
        def inner(*args, **kwargs):
            for raw in fn(*args, **kwargs):
                yield LazyDoc(raw, model_cls, alias_map=alias_map)
        return inner
    return decorator
