<template>
  <div class="not-found">
    <div class="not-found-content">
      <div class="error-code">404</div>
      <h1 class="error-title">Page Not Found</h1>
      <p class="error-description">
        The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.
      </p>
      <div class="error-actions">
        <el-button type="primary" @click="goHome">
          <el-icon><House /></el-icon>
          Go to Dashboard
        </el-button>
        <el-button @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          Go Back
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { House, ArrowLeft } from '@element-plus/icons-vue'

const router = useRouter()

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  router.go(-1)
}
</script>

<style lang="scss" scoped>
.not-found {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.not-found-content {
  text-align: center;
  color: white;
  
  .error-code {
    font-size: 120px;
    font-weight: 900;
    line-height: 1;
    margin-bottom: 20px;
    opacity: 0.8;
  }
  
  .error-title {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 16px;
  }
  
  .error-description {
    font-size: 18px;
    margin-bottom: 40px;
    opacity: 0.9;
    max-width: 500px;
    line-height: 1.6;
  }
  
  .error-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .not-found-content {
    .error-code {
      font-size: 80px;
    }
    
    .error-title {
      font-size: 28px;
    }
    
    .error-description {
      font-size: 16px;
    }
    
    .error-actions {
      flex-direction: column;
      align-items: center;
    }
  }
}
</style>
