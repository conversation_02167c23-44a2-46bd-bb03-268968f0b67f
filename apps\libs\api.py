import streamlit as st
import requests
from datetime import timedelta
from dotenv import load_dotenv
import os

load_dotenv()

headers = {"Content-Type": "application/json", "Accept": "application/json"}
service_setup = (
    "community-workers:8000" if os.getenv("ENV") != "develop" else "localhost:5000"
)


@st.cache_data
def get_routes():
    url = "http://{}/v1/community/qualification/routes/list".format(service_setup)
    response = requests.get(url)
    return response.json()


# @st.cache_data
def get_competitions():
    url = "http://{}/v1/community/qualification/competitions/list".format(service_setup)
    response = requests.get(url, params={})
    return response.json()


def add_qualifications(data):
    url = "http://{}/v1/community/qualification/competitions/add".format(
        service_setup
    ).format(service_setup)
    response = requests.post(url, headers=headers, json=data)
    return response


@st.cache_data(ttl=timedelta(minutes=3))
def get_qualifications(competition_id):
    url = "http://{}/v1/community/qualification/competitions/qualifications?competition_id={}".format(
        service_setup, competition_id
    )
    response = requests.get(url)
    return response.json()


@st.cache_data(ttl=timedelta(minutes=180))
def get_credit_history(competition_id: str = None):
    url = "http://{}/v1/community/qualification/competitions/credits/history".format(
        service_setup
    )
    if competition_id:
        url = "http://{}/v1/community/qualification/competitions/credits/history?competition_id={}".format(
            service_setup, competition_id
        )
    response = requests.get(url)
    return response


def add_credit(competition_id, body):
    url = "http://{}/v1/community/qualification/competitions/credits/add?competition_id={}".format(
        service_setup, competition_id
    )
    response = requests.post(url, headers=headers, json=body)
    print(body)
    return response


def add_admin_log(body):
    url = "http://{}/v1/community/qualification/admins/add".format(service_setup)
    response = requests.post(url, headers=headers, json=body)
    return response


def add_fake_stats(body):
    url = "http://{}/v1/community/qualification/statisitics/add".format(service_setup)
    response = requests.post(url, headers=headers, json=body)
    return response


def get_school_data(body):
    url = "http://{}/v1/community/qualification/competitions/school/data".format(
        service_setup
    )
    response = requests.get(url, headers=headers, json=body)
    return response


def get_school_details(body):
    url = "http://{}/v1/community/qualification/competitions/school/data?is_detail=true".format(
        service_setup
    )
    response = requests.get(url, headers=headers, json=body)
    return response


@st.cache_data(ttl=timedelta(days=10))
def list_schools():
    url = "http://{}/v1/community/qualification/competitions/school/list".format(
        service_setup
    )
    response = requests.get(url)
    return response


def sync_badges(body):
    url = "http://{}/v1/community/qualification/badges/sync".format(service_setup)
    response = requests.post(url, headers=headers, json=body)
    return response


def get_register_info(body):
    url = "http://{}/v1/community/competitions/register?detail_type={}".format(
        service_setup, body.get("competition_type", "DATA_ANALYSIS")
    )
    response = requests.post(url, headers=headers, json=body)
    return response


def get_competition_user_by_identity(body):
    url = "http://{}/v1/community/competitions/register/identity".format(service_setup)
    response = requests.post(url, headers=headers, json=body)
    return response


def get_user(body):
    url = "http://{}/v1/community/user".format(service_setup)
    response = requests.post(url, headers=headers, json=body)
    return response


def get_universities():
    url = "http://{}/v1/community/universities".format(service_setup)
    response = requests.get(url)
    return response


def get_majors():
    url = "http://{}/v1/community/majors".format(service_setup)
    response = requests.get(url)
    return response


def get_event_tracks():
    url = "http://{}/v1/cs/event_tracks".format(service_setup)
    response = requests.get(url)
    return response


def get_content_creator(body):
    url = "http://{}/v1/community/content/creators".format(service_setup)
    response = requests.post(url, headers=headers, json=body)
    return response


def get_tags(body):
    url = "http://{}/v1/common/tags".format(service_setup)
    response = requests.get(url, params=body)
    return response


def update_user_tags(body):
    url = "http://{}/v1/common/tags/update".format(service_setup)
    response = requests.post(url, headers=headers, json=body)
    return response


def sync_shence_tag(body):
    url = "http://{}/v1/common/tags/shence".format(service_setup)
    response = requests.post(url, headers=headers, json=body)
    return response


def query_user_id_by(by, body):
    url = "http://{}/v1/common/user_id?by={}".format(service_setup, by)
    response = requests.post(url, headers=headers, json=body)
    return response


def query_tag_user(tag_id, body):
    url = "http://{}/v1/common/tags/{}/query".format(service_setup, tag_id)
    response = requests.post(url, headers=headers, json=body)
    return response


def get_shence_tag_dir():
    url = "http://{}/v1/common/tags/shence/dir".format(service_setup)
    response = requests.get(url)
    return response
