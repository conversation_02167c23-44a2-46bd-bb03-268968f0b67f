import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // Load environment variables
  const env = loadEnv(mode, process.cwd(), '')

  // Determine if we're in development mode
  const isDev = command === 'serve'

  return {
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
      imports: [
        'vue',
        'vue-router',
        'pinia',
        {
          axios: [
            ['default', 'axios']
          ]
        }
      ],
      dts: true
    }),
    Components({
      resolvers: [ElementPlusResolver()],
      dts: true
    })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@views': resolve(__dirname, 'src/views'),
      '@stores': resolve(__dirname, 'src/stores'),
      '@services': resolve(__dirname, 'src/services'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@assets': resolve(__dirname, 'src/assets')
    }
  },
  server: {
    port: parseInt(env.VITE_DEV_PORT) || 3000,
    host: true,
    proxy: {
      '/api': {
        target: env.VITE_PROXY_TARGET || 'http://localhost:8005',
        changeOrigin: true,
        secure: false
      }
    }
  },
  build: {
    outDir: env.VITE_BUILD_OUTPUT_DIR || 'dist',
    sourcemap: isDev || env.VITE_BUILD_SOURCEMAP === 'true',
    minify: !isDev,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          elementPlus: ['element-plus'],
          utils: ['axios', 'dayjs', 'js-cookie']
        }
      }
    }
  },
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.js']
  },

  // Environment-specific configuration
  define: {
    __BUILD_TIMESTAMP__: JSON.stringify(new Date().toISOString()),
    __BUILD_VERSION__: JSON.stringify(env.npm_package_version || '1.0.0'),
    __GIT_COMMIT__: JSON.stringify(process.env.GIT_COMMIT || 'unknown')
  }
  }
})
