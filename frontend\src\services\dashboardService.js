/**
 * Dashboard Service
 * Aggregates data from multiple APIs for dashboard display
 */

import { analyticsApi } from './api/analyticsApi.js'
import { campAdminApi } from './api/campAdminApi.js'
import { communityApi } from './api/communityApi.js'
import { errorHandler } from './errorHandler.js'
import { isMockEnabled } from './mockApiHandler.js'

// Check if we're using mock APIs
const MOCK_ENABLED = isMockEnabled()

// Mock fallback data for when APIs fail
const MOCK_STATS = {
  competitions: 12,
  credits: 15420,
  schools: 85,
  users: 2340
}

const MOCK_ACTIVITIES = [
  {
    id: 1,
    description: 'New competition "Summer Challenge 2024" created',
    timestamp: '2024-01-15 10:30',
    type: 'primary'
  },
  {
    id: 2,
    description: 'Credits distributed to 150 users',
    timestamp: '2024-01-15 09:15',
    type: 'success'
  },
  {
    id: 3,
    description: 'School statistics updated for Beijing University',
    timestamp: '2024-01-15 08:45',
    type: 'info'
  },
  {
    id: 4,
    description: 'Badge synchronization completed',
    timestamp: '2024-01-14 16:20',
    type: 'warning'
  }
]

/**
 * Get dashboard statistics from multiple APIs
 * @returns {Promise<Object>} Dashboard statistics
 */
export const getDashboardStats = async () => {
  try {
    console.log(`📊 Loading dashboard stats (Mock: ${MOCK_ENABLED})`)

    const stats = {
      competitions: 0,
      credits: 0,
      schools: 0,
      users: 0
    }

    // Use Promise.allSettled to handle partial failures gracefully
    const [
      competitionsResult,
      creditsResult,
      schoolsResult,
      usersResult
    ] = await Promise.allSettled([
      getCompetitionCount(),
      getTotalCreditsDistributed(),
      getSchoolCount(),
      getUserCount()
    ])

    // Process results and handle failures gracefully
    if (competitionsResult.status === 'fulfilled') {
      stats.competitions = competitionsResult.value
    } else {
      console.warn('Failed to load competition count:', competitionsResult.reason)
      stats.competitions = MOCK_STATS.competitions
    }

    if (creditsResult.status === 'fulfilled') {
      stats.credits = creditsResult.value
    } else {
      console.warn('Failed to load credits count:', creditsResult.reason)
      stats.credits = MOCK_STATS.credits
    }

    if (schoolsResult.status === 'fulfilled') {
      stats.schools = schoolsResult.value
    } else {
      console.warn('Failed to load schools count:', schoolsResult.reason)
      stats.schools = MOCK_STATS.schools
    }

    if (usersResult.status === 'fulfilled') {
      stats.users = usersResult.value
    } else {
      console.warn('Failed to load users count:', usersResult.reason)
      stats.users = MOCK_STATS.users
    }

    console.log('📊 Dashboard stats loaded:', stats)

    return {
      success: true,
      data: stats
    }
  } catch (error) {
    console.error('Error loading dashboard stats:', error)
    // Return mock data as fallback
    return {
      success: true,
      data: MOCK_STATS
    }
  }
}

/**
 * Get total competition count
 * @returns {Promise<number>} Competition count
 */
const getCompetitionCount = async () => {
  try {
    const response = await campAdminApi.getCompetitions({ limit: 1 })
    if (response.success && response.data) {
      // If the API returns total count in metadata, use that
      // Otherwise, we need to make a call to get all competitions
      const allCompetitions = await campAdminApi.getCompetitions({ limit: 1000 })
      return allCompetitions.success ? allCompetitions.data.length : 0
    }
    return 0
  } catch (error) {
    console.error('Error getting competition count:', error)
    return 0
  }
}

/**
 * Get total credits distributed
 * @returns {Promise<number>} Total credits distributed
 */
const getTotalCreditsDistributed = async () => {
  try {
    const response = await campAdminApi.getCreditHistory({ limit: 1000 })
    if (response.success && response.data) {
      // Sum up all credit amounts from the history
      const totalCredits = response.data.reduce((sum, record) => {
        return sum + (record.credit || 0)
      }, 0)
      return totalCredits
    }
    return 0
  } catch (error) {
    console.error('Error getting total credits:', error)
    return 0
  }
}

/**
 * Get total school/university count
 * @returns {Promise<number>} School count
 */
const getSchoolCount = async () => {
  try {
    const response = await communityApi.getUniversities({ limit: 1 })
    if (response.success && response.total !== undefined) {
      return response.total
    }
    // Fallback: get all universities and count them
    const allUniversities = await communityApi.getAllUniversities()
    return allUniversities.success ? allUniversities.data.length : 0
  } catch (error) {
    console.error('Error getting school count:', error)
    return 0
  }
}

/**
 * Get total user count
 * @returns {Promise<number>} User count
 */
const getUserCount = async () => {
  try {
    const response = await analyticsApi.getTotalUserRankings({ page_size: 1, current_page: 1 })
    if (response.success && response.total !== undefined) {
      return response.total
    }
    return 0
  } catch (error) {
    console.error('Error getting user count:', error)
    return 0
  }
}

/**
 * Get recent admin activities
 * @param {number} limit - Number of activities to return (default: 10)
 * @returns {Promise<Object>} Recent activities
 */
export const getRecentActivities = async (limit = 10) => {
  try {
    console.log(`📋 Loading recent activities (Mock: ${MOCK_ENABLED})`)

    const response = await campAdminApi.getAdminLogs({ limit, offset: 0 })

    if (response.success && response.data && response.data.length > 0) {
      // Transform admin logs to activity format
      const activities = response.data.map((log, index) => ({
        id: log.id || `activity_${index}`,
        description: formatActivityDescription(log),
        timestamp: formatTimestamp(log.created_at || log.timestamp),
        type: getActivityType(log.action_type)
      }))

      console.log(`📋 Loaded ${activities.length} activities from API`)

      return {
        success: true,
        data: activities
      }
    }

    // Fallback to mock data if no real activities found
    console.log('📋 No activities found, using mock data')
    return {
      success: true,
      data: MOCK_ACTIVITIES.slice(0, limit)
    }
  } catch (error) {
    console.error('Error loading recent activities:', error)
    // Return mock data as fallback
    console.log('📋 API failed, using mock activities')
    return {
      success: true,
      data: MOCK_ACTIVITIES.slice(0, limit)
    }
  }
}

/**
 * Format activity description based on admin log data
 * @param {Object} log - Admin log entry
 * @returns {string} Formatted description
 */
const formatActivityDescription = (log) => {
  const actionType = log.action_type
  const remark = log.remark || ''
  const relatedId = log.action_related_id || ''

  // Map action types to user-friendly descriptions
  const actionDescriptions = {
    1: 'Competition created',
    2: 'Credits awarded',
    3: 'Credits revoked',
    4: 'Qualification created',
    5: 'User management action',
    6: 'System configuration updated'
  }

  const baseDescription = actionDescriptions[actionType] || 'Admin action performed'
  
  if (remark) {
    return `${baseDescription}: ${remark}`
  } else if (relatedId) {
    return `${baseDescription} (ID: ${relatedId})`
  }
  
  return baseDescription
}

/**
 * Format timestamp for display
 * @param {string} timestamp - ISO timestamp or date string
 * @returns {string} Formatted timestamp
 */
const formatTimestamp = (timestamp) => {
  if (!timestamp) {
    return new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  try {
    const date = new Date(timestamp)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return timestamp
  }
}

/**
 * Get activity type for timeline display
 * @param {number} actionType - Action type from admin log
 * @returns {string} Timeline type
 */
const getActivityType = (actionType) => {
  const typeMap = {
    1: 'primary',    // Competition created
    2: 'success',    // Credits awarded
    3: 'warning',    // Credits revoked
    4: 'info',       // Qualification created
    5: 'primary',    // User management
    6: 'info'        // System config
  }
  
  return typeMap[actionType] || 'primary'
}

/**
 * Get dashboard summary data (stats + recent activities)
 * @returns {Promise<Object>} Complete dashboard data
 */
export const getDashboardData = async () => {
  try {
    const [statsResult, activitiesResult] = await Promise.allSettled([
      getDashboardStats(),
      getRecentActivities(5)
    ])

    const result = {
      success: true,
      stats: { competitions: 0, credits: 0, schools: 0, users: 0 },
      activities: []
    }

    if (statsResult.status === 'fulfilled' && statsResult.value.success) {
      result.stats = statsResult.value.data
    }

    if (activitiesResult.status === 'fulfilled' && activitiesResult.value.success) {
      result.activities = activitiesResult.value.data
    }

    return result
  } catch (error) {
    console.error('Error loading dashboard data:', error)
    return errorHandler.handleError(error, 'Failed to load dashboard data')
  }
}

// Export service object
export const dashboardService = {
  getDashboardStats,
  getRecentActivities,
  getDashboardData
}
