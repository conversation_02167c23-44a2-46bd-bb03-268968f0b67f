import asyncio
import logging
import sys
import os
import time
from dotenv import load_dotenv

# Ensure the project root is in the Python path for direct imports
# This script (fill_qualification_data.py) is assumed to be in the project root.
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Load environment variables from .env file first, with override=True to ensure .env values take precedence
env_path = os.path.join(project_root, ".env")
if os.path.exists(env_path):
    load_dotenv(env_path, override=True)
    print(f"Loaded environment variables from {env_path} with override=True")
else:
    print(f"Warning: .env file not found at {env_path}")

# Check if DATABASE_URL is set from .env file
# Only use hardcoded value as a last resort
env_db_url = os.environ.get("DATABASE_URL")
if not env_db_url:
    # If DATABASE_URL is not set, use the hardcoded value as fallback
    # Note: Tortoise ORM expects 'postgres://' not 'postgresql://'
    os.environ["DATABASE_URL"] = "********************************************/heywhale"
    print("Set DATABASE_URL from hardcoded value (fallback)")
else:
    # Fix the scheme if it's postgresql:// to postgres:// for Tortoise ORM compatibility
    if env_db_url.startswith("postgresql://"):
        env_db_url = env_db_url.replace("postgresql://", "postgres://", 1)
        os.environ["DATABASE_URL"] = env_db_url
        print(f"Fixed DATABASE_URL scheme for Tortoise ORM: {env_db_url}")
    else:
        print(f"Using DATABASE_URL from environment: {env_db_url}")

# Print DATABASE_URL right after loading .env
print(f"DEBUG [After setup]: DATABASE_URL = {os.environ.get('DATABASE_URL')}")


# Configure logging first so we can see all debug output
logging.basicConfig(
    level=logging.INFO,  # Changed to DEBUG for more detailed logs
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)],
)
logger = logging.getLogger(__name__)

# Set app logger to DEBUG level for more verbose output, but keep Tortoise at INFO to suppress SQL messages
logging.getLogger("app").setLevel(logging.INFO)
logging.getLogger("tortoise").setLevel(logging.INFO)
logging.getLogger("tortoise.db_client").setLevel(logging.INFO)

# Attempt to import asyncpg and log status
try:
    import asyncpg

    logger.info("asyncpg driver found.")
except ImportError:
    logger.error("asyncpg driver NOT found. Please install it: pip install asyncpg")
    print(
        "\n❌ ERROR: asyncpg driver not found. This is required for PostgreSQL connections."
    )
    print("Please run: pip install asyncpg")
    sys.exit(1)  # Exit since this is critical for the script to work

# Print environment variables for debugging
print("\nEnvironment variables for debugging:")
print(f"SSH_TUNNEL_HOST: {os.getenv('SSH_TUNNEL_HOST')}")
print(f"SSH_TUNNEL_PORT: {os.getenv('SSH_TUNNEL_PORT')}")
print(f"SSH_TUNNEL_USER: {os.getenv('SSH_TUNNEL_USER')}")
print(f"SSH_TUNNEL_PRIVATE_KEY_PATH: {os.getenv('SSH_TUNNEL_PRIVATE_KEY_PATH')}")
print(f"MONGO_HOST: {os.getenv('MONGO_HOST')}")
print(f"MONGO_PORT: {os.getenv('MONGO_PORT')}")
print(f"MONGO_USER: {os.getenv('MONGO_USER')}")
print(f"MONGO_DB: {os.getenv('MONGO_DB')}")
print(
    f"MONGO_PASSWORD: {'*' * (len(os.getenv('MONGO_PASSWORD') or '') if os.getenv('MONGO_PASSWORD') else 0)}"
)

# Print MongoDB connection strings
mongo_user = os.getenv("MONGO_USER")
mongo_password = os.getenv("MONGO_PASSWORD")
mongo_host = os.getenv("MONGO_HOST")
mongo_port = os.getenv("MONGO_PORT")
mongo_db = os.getenv("MONGO_DB")

# Direct connection string (without SSH tunnel)
direct_mongo_uri = (
    f"mongodb://{mongo_user}:{mongo_password}@{mongo_host}:{mongo_port}/{mongo_db}"
)
print(
    f"Direct MongoDB connection string: {direct_mongo_uri.replace(mongo_password, '********')}"
)

# SSH tunnel connection string (will use localhost and tunnel port)
print(
    f"SSH tunnel MongoDB connection will use: mongodb://{mongo_user}:********@localhost:<tunnel_port>/{mongo_db}"
)

# Set SSH key path if not absolute
ssh_key_path = os.getenv("SSH_TUNNEL_PRIVATE_KEY_PATH")
if ssh_key_path and not os.path.isabs(ssh_key_path):
    # Ensure proper path format with correct separators for Windows
    ssh_key_path = os.path.normpath(os.path.join(project_root, ssh_key_path))
    # Update environment variable with absolute path
    os.environ["SSH_TUNNEL_PRIVATE_KEY_PATH"] = ssh_key_path
    logger.info(f"Updated SSH key path to absolute path: {ssh_key_path}")
    # Check if SSH key file exists
    if not os.path.exists(ssh_key_path):
        logger.warning(f"SSH key file not found at {ssh_key_path}")
        print(f"⚠️ WARNING: SSH key file not found at {ssh_key_path}")

# Try importing the required modules with proper error handling
try:
    from tortoise import Tortoise

    logger.info("Successfully imported Tortoise ORM")
except ImportError as e:
    logger.error(f"Failed to import Tortoise ORM: {e}")
    print(
        "\n❌ ERROR: Failed to import Tortoise ORM. Please install it with: pip install tortoise-orm"
    )
    sys.exit(1)

try:
    from core.config import settings
    from core.config.tunnel_manager import SSHTunnelManager
    from core.database.database import init_database, close_database
    from core.database.mongo_db import connect_to_mongo, close_mongo_connection

    logger.info("Successfully imported core modules")
except ImportError as e:
    logger.error(f"Failed to import required core modules: {e}")
    sys.exit(1)

# Ensure settings match environment variables from .env
# Only update settings.DATABASE_URL if it doesn't match the environment variable
env_db_url = os.environ.get("DATABASE_URL")
if env_db_url and settings.DATABASE_URL != env_db_url:
    settings.DATABASE_URL = env_db_url
    logger.info(
        f"Updated settings.DATABASE_URL from environment: {settings.DATABASE_URL}"
    )

# Verify that we have a valid DATABASE_URL
if not settings.DATABASE_URL or not (
    settings.DATABASE_URL.startswith("postgres://")
    or settings.DATABASE_URL.startswith("postgresql://")
):
    logger.error(f"Invalid DATABASE_URL: {settings.DATABASE_URL}")
    print(f"\n❌ ERROR: Invalid DATABASE_URL: {settings.DATABASE_URL}")
    print(
        "Please ensure your .env file contains a valid PostgreSQL DATABASE_URL or set it in the environment."
    )
    sys.exit(1)

# Fix the scheme if needed for Tortoise ORM compatibility
if settings.DATABASE_URL.startswith("postgresql://"):
    settings.DATABASE_URL = settings.DATABASE_URL.replace(
        "postgresql://", "postgres://", 1
    )
    logger.info(f"Fixed DATABASE_URL scheme for Tortoise ORM: {settings.DATABASE_URL}")

# Ensure TORTOISE_MODELS is set
if not hasattr(settings, "TORTOISE_MODELS") or not settings.TORTOISE_MODELS:
    settings.TORTOISE_MODELS = ["app.db.models.base_models"]
    logger.info(f"Set settings.TORTOISE_MODELS: {settings.TORTOISE_MODELS}")

# Explicitly import all models to ensure they're registered
try:
    # Import the models module first to ensure it's loaded
    import app.db.models.base_models

    # Then import specific models
    from app.db.models.base_models import (
        User,
        Teams,
        Submission,
        Statistics,
        Competition,
        Route,
        Qualification,
        TimestampMixin,
        QualifiedUser,
        AccessLog,
        Admin,
        AdminLog,
        CreditHistory,
        Ranking,
        University,
        Major,
    )

    logger.info("Successfully imported critical models from base_models")

    # Try importing other models but don't fail if they don't exist
    try:
        from app.db.models.base_models import (
            TimestampMixin,
            QualifiedUser,
            AccessLog,
            Admin,
            AdminLog,
            CreditHistory,
            Ranking,
            University,
            Major,
        )

        logger.info("Successfully imported additional models from base_models")
    except ImportError as e:
        logger.warning(f"Some additional models could not be imported: {e}")
except ImportError as e:
    logger.error(f"Critical models could not be imported: {e}")
    print(f"\n❌ ERROR: Failed to import critical models: {e}")
    print(
        "Please check that the models are correctly defined in app/db/models/base_models.py"
    )
    sys.exit(1)

# Enable SSH tunnel by default
settings.ssh_tunnel_enabled = True
logger.info("Enabled SSH tunnel by default")

# Instantiate the tunnel manager
tunnel_manager = SSHTunnelManager()

from app.scripts.qualification_setup import initialize_tables, clean_tables

# Debug prints to verify DATABASE_URL
print(f"DEBUG_PRINT: DATABASE_URL from os.environ: {os.environ['DATABASE_URL']}")
print(f"DEBUG_PRINT: DATABASE_URL from settings: {settings.DATABASE_URL}")
print(f"DEBUG_PRINT: TORTOISE_MODELS from settings: {settings.TORTOISE_MODELS}")
print(f"DEBUG_PRINT: SSH_TUNNEL_ENABLED from settings: {settings.ssh_tunnel_enabled}")

# Configure logging
logging.basicConfig(
    level=logging.INFO,  # Changed to DEBUG for more detailed logs
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)],
)
logger = logging.getLogger(__name__)

# Set app logger to DEBUG level for more verbose output, but keep Tortoise at INFO to suppress SQL messages
logging.getLogger("app").setLevel(logging.INFO)
logging.getLogger("tortoise").setLevel(logging.INFO)
logging.getLogger("tortoise.db_client").setLevel(logging.INFO)


async def init_database_connections():
    """Initialize both Tortoise ORM and MongoDB connections"""
    # NOTE: The new `connect_to_mongo` function from `core.database.mongo_db`
    # will automatically start the SSH tunnel if `settings.ssh_tunnel.enabled` is True.
    if settings.ssh_tunnel_enabled:
        logger.info("SSH tunnel is enabled. MongoDB connection will attempt to use it.")

    try:
        logger.info("Initializing MongoDB connection...")
        await connect_to_mongo()
        logger.info("MongoDB connection initialized successfully.")
    except Exception as e:
        logger.error(f"Failed to initialize MongoDB: {e}", exc_info=True)
        logger.warning("Continuing without MongoDB connection")

    # The new `init_database` function from `core.database.database`
    # reads from the global `settings` object for its configuration.
    try:
        logger.info("Initializing Tortoise ORM for PostgreSQL...")
        await init_database()
        logger.info("Tortoise ORM initialized successfully.")
    except Exception as e:
        logger.error(f"Failed to initialize Tortoise ORM: {e}", exc_info=True)
        # Depending on the script's goal, you might want to raise e or sys.exit(1)
        raise


async def close_database_connections():
    """Close all database connections and the SSH tunnel"""
    try:
        await close_database()
        logger.info("PostgreSQL connections closed.")
    except Exception as e:
        logger.error(f"Error closing PostgreSQL connections: {e}")

    try:
        await close_mongo_connection()
        logger.info("MongoDB connection closed.")
    except Exception as e:
        logger.error(f"Error closing MongoDB connection: {e}")

    # Stop the SSH tunnel if it was active
    if tunnel_manager.is_active:
        try:
            await tunnel_manager.stop()
            logger.info("SSH tunnel closed.")
        except Exception as e:
            logger.error(f"Error closing SSH tunnel: {e}")


async def run_data_filling_process(clear_existing_data: bool = False):
    """
    Main process to initialize the database and fill it with qualification data.

    Args:
        clear_existing_data: If True, will clear existing data in qualification-related tables before filling.
    """
    # Print current configuration for debugging
    print("\n" + "=" * 50)
    print("CONFIGURATION SUMMARY:")
    print(f"DATABASE_URL: {settings.DATABASE_URL}")
    print(f"TORTOISE_MODELS: {settings.TORTOISE_MODELS}")
    print(f"SSH_TUNNEL_ENABLED: {settings.ssh_tunnel_enabled}")
    print("CSV FILE: app/data/qualification_samples/2024_summer.csv")
    print(f"CLEAR EXISTING DATA: {clear_existing_data}")
    print("=" * 50 + "\n")

    # Verify CSV file exists before proceeding
    csv_path = "app/data/qualification_samples/2024_summer.csv"
    if not os.path.exists(csv_path):
        logger.error(f"CSV file not found at {csv_path}")
        print(f"\n❌ ERROR: CSV file not found at {csv_path}")
        print("Please ensure the CSV file exists before running this script.")
        sys.exit(1)
    else:
        logger.info(f"CSV file found at {csv_path}")

    try:
        # Initialize both Tortoise and MongoDB connections
        logger.info("Initializing database connections...")
        await init_database_connections()

        # Clear existing data if requested
        if clear_existing_data:
            logger.info("Clearing existing qualification-related tables...")
            print("\nClearing existing qualification-related tables...")
            try:
                # clean_tables (from app.scripts.qualification_setup) will delete data from
                # Submission, User, Team, Statistics tables by default.
                await clean_tables()
                logger.info(
                    "✅ Successfully cleared tables specified in clean_tables function."
                )
                print("✅ Successfully cleared existing data.")
            except Exception as e:
                logger.error(f"Error during clean_tables: {e}", exc_info=True)
                print(f"\n❌ ERROR during table cleaning: {e}")
                print("Attempting to continue with data initialization...")
        else:
            logger.info(
                "Skipping table cleaning process. Existing data will be preserved or updated."
            )
            print(
                "\nSkipping table cleaning. Existing data will be preserved or updated."
            )

        # Initialize tables with data from CSV
        logger.info("Starting data initialization process...")
        print("\nStarting data initialization from CSV...")
        try:
            # initialize_tables reads the CSV and populates/updates the database tables
            start_time = time.time()
            await initialize_tables()
            end_time = time.time()
            duration = end_time - start_time

            logger.info(
                f"✅ Data initialization completed successfully in {duration:.2f} seconds."
            )
            print(
                f"\n✅ Data initialization completed successfully in {duration:.2f} seconds."
            )
            return True
        except Exception as e:
            if "MongoDB client not initialized" in str(
                e
            ) or "mongo_client is None" in str(e):
                logger.error(
                    "Failed to initialize tables due to MongoDB connection issues."
                )
                print(
                    "\n❌ ERROR: Failed to initialize tables due to MongoDB connection issues."
                )
                print("This is expected if MongoDB connection failed earlier.")
                print("Some data may not be available or may be incomplete.")
            elif "Statistics" in str(e) and "Statistic" in str(e):
                # Handle the specific model name issue mentioned in the memory
                logger.error(f"Error with Statistics/Statistic model: {e}")
                print(
                    "\n❌ ERROR: There appears to be a model name mismatch between 'Statistics' and 'Statistic'."
                )
                print(
                    "Please check the model imports in qualification_setup.py and related files."
                )
            else:
                logger.error(f"Error during initialize_tables: {e}", exc_info=True)
                print(f"\n❌ ERROR during data initialization: {e}")
            return False

    except Exception as e:
        logger.error(f"Error during data filling process: {e}", exc_info=True)
        print(f"\n❌ UNHANDLED ERROR: {e}")
        return False
    finally:
        # Close all database connections
        logger.info("Closing database connections...")
        try:
            await close_database_connections()
            logger.info("Database connections closed successfully.")
        except Exception as e:
            logger.error(f"Error closing database connections: {e}", exc_info=True)


if __name__ == "__main__":
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(
        description="Fill qualification data from CSV into database tables"
    )
    parser.add_argument(
        "--clear",
        "-c",
        action="store_true",
        help="Clear existing data in qualification-related tables before filling",
    )
    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Enable more verbose output"
    )
    parser.add_argument(
        "--csv-file",
        default="app/data/qualification_samples/2024_summer.csv",
        help="Path to CSV file containing qualification data (default: app/data/qualification_samples/2024_summer.csv)",
    )
    args = parser.parse_args()

    # Set configuration based on command line arguments
    CLEAR_DATA_ON_RUN = args.clear

    # Set more verbose logging if requested
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.setLevel(logging.DEBUG)
        logger.info("Verbose logging enabled")

    # Print header
    print("=" * 80)
    print("🚀 STARTING QUALIFICATION DATA FILLING PROCESS")
    print(f"📂 CSV File: {args.csv_file}")
    print(f"🗑️ Clear existing data: {CLEAR_DATA_ON_RUN}")
    print(f"🔍 Verbose mode: {args.verbose}")
    print("=" * 80)

    logger.info(
        f"Starting qualification data filling script. Clear existing data: {CLEAR_DATA_ON_RUN}"
    )

    # Verify execution directory
    expected_dir = os.path.basename(os.getcwd())
    if expected_dir != "community-services":
        print(
            "\n⚠️ WARNING: You may not be running this script from the project root directory."
        )
        print(f"Current directory: {os.getcwd()}")
        print("Expected directory name: community-services")
        print("This might cause path resolution issues for the CSV file and imports.")
        response = input("Continue anyway? (y/n): ").strip().lower()
        if response != "y":
            print("Exiting script.")
            sys.exit(0)

    # Check if the CSV file exists
    if not os.path.exists(args.csv_file):
        print(f"\n❌ ERROR: CSV file not found at {args.csv_file}")
        print("Please ensure the CSV file exists before running this script.")
        sys.exit(1)

    # Execute the main process
    start_time = time.time()
    try:
        success = asyncio.run(
            run_data_filling_process(clear_existing_data=CLEAR_DATA_ON_RUN)
        )
        end_time = time.time()
        duration = end_time - start_time

        if success:
            print("=" * 80)
            print(
                f"✅ QUALIFICATION DATA FILLING COMPLETED SUCCESSFULLY in {duration:.2f} seconds"
            )
            print("=" * 80)
        else:
            print("=" * 80)
            print(
                f"⚠️ QUALIFICATION DATA FILLING COMPLETED WITH WARNINGS in {duration:.2f} seconds"
            )
            print(
                "Some operations may not have completed successfully. Check the logs for details."
            )
            print("=" * 80)
            sys.exit(2)  # Exit with code 2 to indicate partial success/warnings
    except KeyboardInterrupt:
        logger.info("Process interrupted by user.")
        print("\n❌ PROCESS INTERRUPTED BY USER")
        sys.exit(130)  # Standard exit code for SIGINT
    except Exception as e:
        # This catches any unhandled exceptions
        end_time = time.time()
        duration = end_time - start_time
        logger.error(f"Unhandled error in script execution: {e}", exc_info=True)
        print("=" * 80)
        print(f"❌ ERROR: {e}")
        print(f"Script failed after running for {duration:.2f} seconds")
        print("=" * 80)
        sys.exit(1)  # Exit with a non-zero code to indicate failure

    logger.info("Qualification data filling script finished.")
