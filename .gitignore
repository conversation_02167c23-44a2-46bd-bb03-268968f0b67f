# Virtual environments
.venv/
venv/
ENV/
env/

# Environment variables
.env*
!.env.example

# Python bytecode and compiled files
*.py[cod]
*$py.class
__pycache__/
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# IDEs and editors
.vscode/
.idea/
*.swp
*.swo
*~

# OS-specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Log files
*.log
logs/
log/

# Static analysis caches
.ruff_cache/

# FastAPI specific
.pytest_cache/

# Database
*.db
*.sqlite3

# SSH keys and sensitive files
opensshkey*
id_rsa*
id_ed25519*
*.pem
*.key

# Temporary files
*.tmp
*.temp
temp.*

# Node.js (if any frontend assets)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Application specific
migration_log/
.windsurf/
.cursor/
usage.json

# Added from summer_camp, temp.txt was specific, might not be needed globally
# temp.txt 