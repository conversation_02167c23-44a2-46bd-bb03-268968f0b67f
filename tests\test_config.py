"""
Tests for the new configuration system.

This module tests:
- Pydantic settings validation
- Environment variable loading
- Nested configuration access
- Logging configuration
"""

import os
import pytest
from unittest.mock import patch

from core.config import (
    settings,
    Settings,
    DatabaseSettings,
    LoggingSettings,
    Environment,
    LogLevel,
    logger,
    get_logger,
)


class TestSettings:
    """Test the main Settings class."""

    def test_default_values(self):
        """Test that default values are set correctly."""
        # Create a fresh settings instance
        test_settings = Settings()

        assert test_settings.environment == Environment.DEVELOPMENT
        assert test_settings.debug is False
        assert test_settings.database.port == 3306
        assert test_settings.logging.level == LogLevel.INFO
        assert test_settings.api.version == "3.1.0"

    def test_environment_variable_loading(self):
        """Test that environment variables are loaded correctly."""
        with patch.dict(
            os.environ,
            {
                "ENVIRONMENT": "production",
                "DEBUG": "true",
                "DB_HOST": "test-host",
                "DB_PORT": "5433",
                "LOG_LEVEL": "DEBUG",
            },
        ):
            test_settings = Settings()

            assert test_settings.environment == Environment.PRODUCTION
            assert test_settings.debug is True
            assert test_settings.database.host == "test-host"
            assert test_settings.database.port == 5433
            assert test_settings.logging.level == LogLevel.DEBUG

    def test_nested_configuration(self):
        """Test that nested configuration works correctly."""
        assert hasattr(settings, "database")
        assert hasattr(settings, "security")
        assert hasattr(settings, "cors")
        assert hasattr(settings, "logging")
        assert hasattr(settings, "ssh_tunnel")
        assert hasattr(settings, "scheduler")
        assert hasattr(settings, "api")

    def test_property_methods(self):
        """Test property methods for environment checking."""
        # Test development environment
        with patch.dict(os.environ, {"ENVIRONMENT": "development"}):
            test_settings = Settings()
            assert test_settings.is_development is True
            assert test_settings.is_production is False
            assert test_settings.is_testing is False

        # Test production environment
        with patch.dict(os.environ, {"ENVIRONMENT": "production"}):
            test_settings = Settings()
            assert test_settings.is_development is False
            assert test_settings.is_production is True
            assert test_settings.is_testing is False


class TestDatabaseSettings:
    """Test the DatabaseSettings class."""

    def test_database_url_building(self):
        """Test that database URL is built correctly when not provided."""
        with patch.dict(
            os.environ,
            {
                "DB_HOST": "localhost",
                "DB_PORT": "5433",
                "DB_USER": "testuser",
                "DB_PASSWORD": "testpass",
                "DB_NAME": "testdb",
            },
            clear=True,
        ):
            db_settings = DatabaseSettings()
            expected_url = "postgres://testuser:testpass@localhost:5433/testdb"
            assert db_settings.url == expected_url

    def test_tortoise_models_parsing(self):
        """Test that Tortoise models are parsed correctly from string."""
        with patch.dict(
            os.environ, {"TORTOISE_MODELS": "app.models,shared.models,aerich.models"}
        ):
            db_settings = DatabaseSettings()
            expected_models = ["app.models", "shared.models", "aerich.models"]
            assert db_settings.tortoise_models == expected_models


class TestLoggingSettings:
    """Test the LoggingSettings class."""

    def test_default_logging_settings(self):
        """Test default logging configuration."""
        log_settings = LoggingSettings()

        assert log_settings.level == LogLevel.INFO
        assert log_settings.file_enabled is True
        assert log_settings.console_enabled is True
        assert log_settings.json_enabled is False
        assert log_settings.file_path == "logs/app.log"

    def test_logging_environment_variables(self):
        """Test logging settings from environment variables."""
        with patch.dict(
            os.environ,
            {
                "LOG_LEVEL": "DEBUG",
                "LOG_FILE_ENABLED": "false",
                "LOG_JSON_ENABLED": "true",
                "LOG_FILE_PATH": "custom/path/app.log",
            },
        ):
            log_settings = LoggingSettings()

            assert log_settings.level == LogLevel.DEBUG
            assert log_settings.file_enabled is False
            assert log_settings.json_enabled is True
            assert log_settings.file_path == "custom/path/app.log"


class TestLogging:
    """Test the logging functionality."""

    def test_logger_import(self):
        """Test that logger can be imported and used."""
        assert logger is not None

        # Test basic logging (should not raise exceptions)
        logger.info("Test message")
        logger.debug("Debug message")
        logger.warning("Warning message")

    def test_get_logger(self):
        """Test the get_logger function."""
        test_logger = get_logger("test_module")
        assert test_logger is not None

        # Test logging with custom logger
        test_logger.info("Test message from custom logger")

    def test_structured_logging(self):
        """Test structured logging with extra data."""
        logger.bind(user_id="123", operation="test").info("Structured log message")

        # Should not raise exceptions
        assert True


class TestConfigurationValidation:
    """Test configuration validation."""

    def test_invalid_environment(self):
        """Test that invalid environment values are handled."""
        with patch.dict(os.environ, {"ENVIRONMENT": "invalid_env"}):
            with pytest.raises(ValueError):
                Settings()

    def test_invalid_log_level(self):
        """Test that invalid log levels are handled."""
        with patch.dict(os.environ, {"LOG_LEVEL": "INVALID_LEVEL"}):
            with pytest.raises(ValueError):
                LoggingSettings()

    def test_invalid_port_number(self):
        """Test that invalid port numbers are handled."""
        with patch.dict(os.environ, {"DB_PORT": "not_a_number"}):
            with pytest.raises(ValueError):
                DatabaseSettings()


class TestBackwardCompatibility:
    """Test backward compatibility with old configuration."""

    def test_global_settings_instance(self):
        """Test that global settings instance is available."""
        from core.config import settings as global_settings

        assert global_settings is not None
        assert isinstance(global_settings, Settings)

    def test_settings_attributes(self):
        """Test that common settings attributes are accessible."""
        # These should work for backward compatibility
        assert hasattr(settings, "database")
        assert hasattr(settings, "debug")
        assert hasattr(settings, "environment")

        # Database settings
        assert hasattr(settings.database, "url")
        assert hasattr(settings.database, "host")
        assert hasattr(settings.database, "port")

        # API settings
        assert hasattr(settings.api, "version")
        assert hasattr(settings.api, "title")


if __name__ == "__main__":
    # Run basic tests
    print("Testing configuration system...")

    # Test basic functionality
    print(f"Environment: {settings.environment}")
    print(f"Debug mode: {settings.debug}")
    print(f"Database URL: {settings.database.url}")
    print(f"Log level: {settings.logging.level}")

    # Test logging
    logger.info("Configuration test completed successfully")

    print("All tests passed!")
