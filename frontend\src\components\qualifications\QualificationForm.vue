<template>
  <div class="qualification-form">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="140px"
      @submit.prevent="handleSubmit"
    >
      <el-form-item label="Competition" prop="competition_id" required>
        <el-select v-model="form.competition_id" placeholder="Select competition" style="width: 100%">
          <el-option
            v-for="competition in competitions"
            :key="competition.id"
            :label="competition.title || competition.name"
            :value="competition.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="Qualification Name" prop="qualification_name" required>
        <el-input
          v-model="form.qualification_name"
          placeholder="Enter qualification name"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="Credit Amount" prop="credit" required>
            <el-input-number
              v-model="form.credit"
              :min="1"
              :max="10000"
              :step="10"
              placeholder="Credit amount"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="Type" prop="qualification_type" required>
            <el-select v-model="form.qualification_type" placeholder="Select type" style="width: 100%">
              <el-option
                v-for="type in qualificationTypes"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="Logic" prop="qualification_logic" required>
            <el-select v-model="form.qualification_logic" placeholder="Select logic" style="width: 100%">
              <el-option
                v-for="logic in qualificationLogics"
                :key="logic.value"
                :label="logic.label"
                :value="logic.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item 
            label="Score Threshold" 
            prop="score_threshold"
            :required="needsScoreThreshold"
          >
            <el-input-number
              v-model="form.score_threshold"
              :min="0"
              :max="100"
              :step="1"
              :disabled="!needsScoreThreshold"
              placeholder="Score threshold (%)"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="Related Task ID" prop="related_task_id">
        <el-input
          v-model="form.related_task_id"
          placeholder="Enter related task ID (optional)"
          maxlength="50"
        />
      </el-form-item>

      <el-form-item label="Description" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="Enter qualification description (optional)"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <!-- Form Actions -->
      <el-form-item>
        <div class="form-actions">
          <el-button @click="$emit('cancel')">Cancel</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            Create Qualification
          </el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { competitionService } from '@/services/competitionService'

// Emits
const emit = defineEmits(['success', 'cancel'])

// State
const formRef = ref()
const submitting = ref(false)
const competitions = ref([])

// Computed
const needsScoreThreshold = computed(() => {
  return form.qualification_type === 1 // Score Based
})

// Form data
const form = reactive({
  competition_id: '',
  qualification_name: '',
  credit: 100,
  qualification_type: 1,
  qualification_logic: 1,
  score_threshold: 80,
  related_task_id: '',
  description: ''
})

// Form validation rules
const rules = {
  competition_id: [
    { required: true, message: 'Please select a competition', trigger: 'change' }
  ],
  qualification_name: [
    { required: true, message: 'Please enter qualification name', trigger: 'blur' },
    { min: 3, max: 100, message: 'Length should be 3 to 100 characters', trigger: 'blur' }
  ],
  credit: [
    { required: true, message: 'Please enter credit amount', trigger: 'blur' },
    { type: 'number', min: 1, max: 10000, message: 'Credit must be between 1 and 10000', trigger: 'blur' }
  ],
  qualification_type: [
    { required: true, message: 'Please select qualification type', trigger: 'change' }
  ],
  qualification_logic: [
    { required: true, message: 'Please select qualification logic', trigger: 'change' }
  ],
  score_threshold: [
    { 
      validator: (rule, value, callback) => {
        if (needsScoreThreshold.value && (value === null || value === undefined)) {
          callback(new Error('Score threshold is required for score-based qualifications'))
        } else if (value !== null && value !== undefined && (value < 0 || value > 100)) {
          callback(new Error('Score threshold must be between 0 and 100'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ]
}

// Options
const qualificationTypes = [
  { value: 1, label: 'Score Based' },
  { value: 2, label: 'Rank Based' },
  { value: 3, label: 'Participation' },
  { value: 4, label: 'Completion' },
  { value: 5, label: 'Custom' }
]

const qualificationLogics = [
  { value: 1, label: 'Greater Than' },
  { value: 2, label: 'Less Than' },
  { value: 3, label: 'Equal To' },
  { value: 4, label: 'Between' },
  { value: 5, label: 'Top N' },
  { value: 6, label: 'Bottom N' }
]

// Methods
const loadCompetitions = async () => {
  try {
    const response = await competitionService.getCompetitions()
    if (response.success) {
      competitions.value = response.data
    }
  } catch (error) {
    console.error('Failed to load competitions:', error)
    ElMessage.error('Failed to load competitions')
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
  } catch (error) {
    ElMessage.error('Please fix form errors before submitting')
    return
  }

  submitting.value = true

  try {
    const response = await competitionService.createQualification(form)
    
    if (response.success) {
      ElMessage.success('Qualification created successfully')
      emit('success')
    } else {
      throw new Error(response.error?.message || 'Failed to create qualification')
    }
  } catch (error) {
    console.error('Failed to create qualification:', error)
    ElMessage.error('Failed to create qualification')
  } finally {
    submitting.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadCompetitions()
})
</script>

<style lang="scss" scoped>
.qualification-form {
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 16px;
  }
}
</style>
