# Core FastAPI
fastapi
uvicorn[standard]
pydantic>=2.0.0
pydantic-settings

# ORM and Database
tortoise-orm
asyncpg # PostgreSQL async driver
aerich # For Tortoise migrations
motor # For async MongoDB

# Data Handling & Utilities
python-dotenv==1.0.0
python-dateutil==2.8.2
requests==2.28.2
pandas
pytz==2022.4
APScheduler~=3.10.0 # For background task scheduling
pandasql3==0.7.3
xlrd
requests-toolbelt==1.0.0
jinja2 # For templating
aiohttp # For async http client
aiofiles

# Caching
redis>=4.5.0
redis[hiredis]>=4.5.0

# Logging
loguru>=0.7.0

# Others (from original community-services, evaluate if still needed long-term)
cffi>=1.12
paramiko==2.8.1
sshtunnel==0.4.0

# Testing
pytest
pytest-asyncio

# Removed (original content for reference during merge):
# Flask==2.3.2
# Flask-SQLAlchemy==3.1.1
# pymongo==3.12.0
# SQLAlchemy==2.0.19
# gunicorn