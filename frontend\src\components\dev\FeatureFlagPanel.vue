<template>
  <div class="feature-flag-panel" v-if="isDevelopment">
    <el-drawer
      v-model="drawerVisible"
      title="Feature Flags"
      direction="rtl"
      size="400px"
    >
      <div class="flag-controls">
        <el-button @click="resetFlags" type="warning" size="small">
          Reset to Defaults
        </el-button>
        <el-button @click="exportFlags" type="info" size="small">
          Export Config
        </el-button>
      </div>

      <el-divider />

      <!-- Core Features -->
      <div class="flag-section">
        <h3>Core Features</h3>
        <div class="flag-item" v-for="flag in coreFlags" :key="flag.key">
          <el-switch
            v-model="flag.enabled"
            @change="toggleFlag(flag.key)"
            :active-text="flag.label"
            :inactive-text="flag.label"
            inline-prompt
          />
          <span class="flag-description">{{ flag.description }}</span>
        </div>
      </div>

      <!-- API Features -->
      <div class="flag-section">
        <h3>API Features</h3>
        <div class="flag-item" v-for="flag in apiFlags" :key="flag.key">
          <el-switch
            v-model="flag.enabled"
            @change="toggleFlag(flag.key)"
            :active-text="flag.label"
            :inactive-text="flag.label"
            inline-prompt
          />
          <span class="flag-description">{{ flag.description }}</span>
        </div>
      </div>

      <!-- UI Features -->
      <div class="flag-section">
        <h3>UI Features</h3>
        <div class="flag-item" v-for="flag in uiFlags" :key="flag.key">
          <el-switch
            v-model="flag.enabled"
            @change="toggleFlag(flag.key)"
            :active-text="flag.label"
            :inactive-text="flag.label"
            inline-prompt
          />
          <span class="flag-description">{{ flag.description }}</span>
        </div>
      </div>

      <!-- Experimental Features -->
      <div class="flag-section">
        <h3>Experimental Features</h3>
        <div class="flag-item" v-for="flag in experimentalFlags" :key="flag.key">
          <el-switch
            v-model="flag.enabled"
            @change="toggleFlag(flag.key)"
            :active-text="flag.label"
            :inactive-text="flag.label"
            inline-prompt
          />
          <span class="flag-description">{{ flag.description }}</span>
        </div>
      </div>
    </el-drawer>

    <!-- Toggle Button -->
    <el-button
      class="flag-toggle-btn"
      type="primary"
      circle
      @click="drawerVisible = true"
      title="Feature Flags"
    >
      🚩
    </el-button>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useFeatureFlags } from '@/composables/useFeatureFlags.js'
import { isDevelopment } from '@/utils/envConfig.js'
import { ElMessage } from 'element-plus'

const { flags, toggle, FEATURE_FLAGS } = useFeatureFlags()
const drawerVisible = ref(false)

// Flag definitions with descriptions
const flagDefinitions = {
  [FEATURE_FLAGS.ANALYTICS]: { label: 'Analytics', description: 'Enable analytics dashboard and reporting' },
  [FEATURE_FLAGS.COMPETITIONS]: { label: 'Competitions', description: 'Enable competition management features' },
  [FEATURE_FLAGS.CREDITS]: { label: 'Credits', description: 'Enable credit management system' },
  [FEATURE_FLAGS.SCHOOLS]: { label: 'Schools', description: 'Enable school/university management' },
  
  [FEATURE_FLAGS.REAL_API]: { label: 'Real API', description: 'Use real backend APIs instead of mocks' },
  [FEATURE_FLAGS.MOCK_API]: { label: 'Mock API', description: 'Use mock APIs for development' },
  [FEATURE_FLAGS.API_CACHING]: { label: 'API Caching', description: 'Enable API response caching' },
  [FEATURE_FLAGS.API_RETRY]: { label: 'API Retry', description: 'Enable automatic API retry on failure' },
  
  [FEATURE_FLAGS.DARK_MODE]: { label: 'Dark Mode', description: 'Enable dark theme support' },
  [FEATURE_FLAGS.ADVANCED_SEARCH]: { label: 'Advanced Search', description: 'Enable advanced search filters' },
  [FEATURE_FLAGS.BULK_OPERATIONS]: { label: 'Bulk Operations', description: 'Enable bulk edit/delete operations' },
  [FEATURE_FLAGS.EXPORT_FEATURES]: { label: 'Export Features', description: 'Enable data export functionality' },
  [FEATURE_FLAGS.LAZY_LOADING]: { label: 'Lazy Loading', description: 'Enable lazy loading for components' },
  [FEATURE_FLAGS.VIRTUAL_SCROLLING]: { label: 'Virtual Scrolling', description: 'Enable virtual scrolling for large lists' },
  [FEATURE_FLAGS.IMAGE_OPTIMIZATION]: { label: 'Image Optimization', description: 'Enable image optimization features' },
  
  [FEATURE_FLAGS.NEW_DASHBOARD]: { label: 'New Dashboard', description: 'Enable redesigned dashboard interface' },
  [FEATURE_FLAGS.BETA_FEATURES]: { label: 'Beta Features', description: 'Enable beta/preview features' },
  [FEATURE_FLAGS.EXPERIMENTAL_UI]: { label: 'Experimental UI', description: 'Enable experimental UI components' }
}

// Computed flag groups
const coreFlags = computed(() => [
  FEATURE_FLAGS.ANALYTICS,
  FEATURE_FLAGS.COMPETITIONS,
  FEATURE_FLAGS.CREDITS,
  FEATURE_FLAGS.SCHOOLS
].map(createFlagItem))

const apiFlags = computed(() => [
  FEATURE_FLAGS.REAL_API,
  FEATURE_FLAGS.MOCK_API,
  FEATURE_FLAGS.API_CACHING,
  FEATURE_FLAGS.API_RETRY
].map(createFlagItem))

const uiFlags = computed(() => [
  FEATURE_FLAGS.DARK_MODE,
  FEATURE_FLAGS.ADVANCED_SEARCH,
  FEATURE_FLAGS.BULK_OPERATIONS,
  FEATURE_FLAGS.EXPORT_FEATURES,
  FEATURE_FLAGS.LAZY_LOADING,
  FEATURE_FLAGS.VIRTUAL_SCROLLING,
  FEATURE_FLAGS.IMAGE_OPTIMIZATION
].map(createFlagItem))

const experimentalFlags = computed(() => [
  FEATURE_FLAGS.NEW_DASHBOARD,
  FEATURE_FLAGS.BETA_FEATURES,
  FEATURE_FLAGS.EXPERIMENTAL_UI
].map(createFlagItem))

/**
 * Create flag item for display
 * @param {string} key - Flag key
 * @returns {Object} Flag item
 */
function createFlagItem(key) {
  const definition = flagDefinitions[key] || { label: key, description: '' }
  return {
    key,
    enabled: flags.value[key] || false,
    label: definition.label,
    description: definition.description
  }
}

/**
 * Toggle a feature flag
 * @param {string} flag - Flag key
 */
function toggleFlag(flag) {
  toggle(flag)
  ElMessage.success(`Feature "${flagDefinitions[flag]?.label || flag}" ${flags.value[flag] ? 'enabled' : 'disabled'}`)
}

/**
 * Reset all flags to defaults
 */
function resetFlags() {
  // This would need to be implemented in the feature flag store
  ElMessage.info('Reset functionality would be implemented here')
}

/**
 * Export current flag configuration
 */
function exportFlags() {
  const config = JSON.stringify(flags.value, null, 2)
  navigator.clipboard.writeText(config).then(() => {
    ElMessage.success('Feature flag configuration copied to clipboard')
  }).catch(() => {
    console.log('Feature Flag Configuration:', config)
    ElMessage.info('Feature flag configuration logged to console')
  })
}

// Keyboard shortcut to open panel
onMounted(() => {
  const handleKeydown = (event) => {
    if (event.ctrlKey && event.shiftKey && event.key === 'F') {
      event.preventDefault()
      drawerVisible.value = !drawerVisible.value
    }
  }
  
  document.addEventListener('keydown', handleKeydown)
  
  // Cleanup
  return () => {
    document.removeEventListener('keydown', handleKeydown)
  }
})
</script>

<style scoped>
.feature-flag-panel {
  position: relative;
}

.flag-toggle-btn {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  font-size: 18px;
}

.flag-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.flag-section {
  margin-bottom: 30px;
}

.flag-section h3 {
  margin: 0 0 15px 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
  font-weight: 600;
}

.flag-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  background: var(--el-bg-color-page);
}

.flag-description {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-left: 0;
}

:deep(.el-switch__label) {
  font-weight: 500;
}
</style>
