# Caching System How-To Guide

This guide provides comprehensive instructions for using the Redis-based caching system in the Community Services API.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Configuration](#configuration)
3. [Cache Decorators](#cache-decorators)
4. [Cache Management](#cache-management)
5. [Best Practices](#best-practices)
6. [Troubleshooting](#troubleshooting)
7. [Performance Optimization](#performance-optimization)

## Quick Start

### Basic Route Caching

The simplest way to add caching to your FastAPI routes is using the `@cache_response` decorator:

```python
from fastapi import APIRouter, Path, Query
from core.cache.decorators import cache_response

router = APIRouter()

@router.get("/users/{user_id}")
@cache_response(
    domain="users",
    operation="profile",
    ttl=600,  # 10 minutes
    key_params=["user_id", "profile_type"]
)
async def get_user_profile(
    user_id: str = Path(...),
    profile_type: str = Query("basic")
):
    # Your route logic here
    return {"user_id": user_id, "name": "<PERSON>", "profile_type": profile_type}
```

**Benefits of the improved approach:**
- No need to add `request: Request` parameter just for caching
- Cleaner function signatures focused on business logic
- Automatic parameter extraction from function arguments
- More intuitive cache key generation

### Function-Level Caching

For caching function results (not FastAPI routes):

```python
from core.cache.decorators import cached

@cached(
    domain="users",
    operation="search",
    ttl=180,  # 3 minutes
    key_params=["query", "limit"]
)
async def search_users(query: str, limit: int = 10):
    # Your function logic here
    return search_results
```

## Configuration

### Environment Variables

Add these variables to your `.env` file:

```bash
# Redis Connection
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DATABASE=0
REDIS_MAX_CONNECTIONS=20

# Cache Settings
CACHE_ENABLED=true
CACHE_DEFAULT_TTL=300
CACHE_USER_PROFILE_TTL=600
CACHE_USER_SEARCH_TTL=180
CACHE_HEYWHALE_TAGS_TTL=3600
CACHE_KEY_PREFIX=community_services
```

### Redis URL Format

Alternatively, use a complete Redis URL:

```bash
REDIS_URL=redis://username:password@localhost:6379/0
```

### TTL Configuration

Configure different TTL values for different operations:

```python
# In core/config/settings.py
cache_user_profile_ttl: int = Field(600, env="CACHE_USER_PROFILE_TTL")  # 10 minutes
cache_user_search_ttl: int = Field(180, env="CACHE_USER_SEARCH_TTL")    # 3 minutes
cache_heywhale_tags_ttl: int = Field(3600, env="CACHE_HEYWHALE_TAGS_TTL")  # 1 hour
```

## Cache Decorators

### @cache_response

For FastAPI route responses:

```python
@cache_response(
    domain="users",           # Cache domain for organization
    operation="profile",      # Operation type for TTL lookup
    ttl=600,                 # Optional: Override default TTL
    key_params=["user_id", "profile_type"],  # Parameters to include in cache key
    user_param="user_id",    # Parameter containing user ID for user-specific caching
    skip_cache_param="skip_cache",  # Parameter to bypass cache
    skip_cache_header="X-Skip-Cache"  # Header to bypass cache (requires Request object)
)
async def get_user_profile(user_id: str, profile_type: str = "basic", skip_cache: bool = False):
    # Route implementation
    pass
```

**Key Features:**
- **No Request Parameter Required**: The decorator automatically extracts cache parameters from function arguments
- **Flexible Key Generation**: Specify which parameters to include in the cache key with `key_params`
- **Auto-Parameter Detection**: If `key_params` is not specified, all function parameters are used (excluding internal ones like `database`, `current_user_id`)
- **User-Specific Caching**: Use `user_param` to enable per-user cache isolation
- **Multiple Skip Options**: Support both parameter-based and header-based cache skipping

### @cached

For function-level caching:

```python
@cached(
    domain="analytics",
    operation="ranking",
    ttl=1800,  # 30 minutes
    key_params=["competition_id", "category"],  # Parameters to include in cache key
    user_param="user_id",                       # Parameter containing user ID
    skip_cache_param="skip_cache",              # Parameter to bypass cache
    cache_condition=lambda result: len(result) > 0  # Only cache non-empty results
)
async def calculate_rankings(competition_id: str, category: str, user_id: str = None, skip_cache: bool = False):
    # Function implementation
    pass
```

### @invalidate_cache

For cache invalidation after data modifications:

```python
@invalidate_cache(
    domain="users",
    operation="profile"  # Invalidate all user profile caches
)
async def update_user_profile(user_id: str, updates: dict):
    # Update user profile
    pass

# Pattern-based invalidation
@invalidate_cache(
    domain="users",
    pattern="users:search:*"  # Invalidate all search caches
)
async def bulk_update_users(user_updates: list):
    # Bulk update logic
    pass
```

### @cache_clear

For clearing specific cache patterns:

```python
@cache_clear(
    domain="users",
    operations=["profile", "search"],  # Clear specific operations
    user_id="current_user_id"          # Clear for specific user
)
async def reset_user_cache(user_id: str):
    # Cache clearing logic
    pass
```

## Cache Management

### Using Cache Manager

Access advanced cache operations through the cache manager:

```python
from core.database.dependencies import get_cache_manager

async def manage_cache():
    cache_manager = await get_cache_manager()
    
    # Get cache statistics
    stats = await cache_manager.get_stats()
    print(f"Hit rate: {stats['hit_rate']:.2%}")
    
    # Warm cache with data
    await cache_manager.warm_cache("users", "popular_profiles", popular_user_data)
    
    # Clear cache by pattern
    cleared = await cache_manager.clear_pattern("users:search:*")
    print(f"Cleared {cleared} cache entries")
    
    # Bulk cache operations
    cache_data = {
        "users:profile:123": {"name": "John", "email": "<EMAIL>"},
        "users:profile:456": {"name": "Jane", "email": "<EMAIL>"}
    }
    await cache_manager.set_many(cache_data, ttl=600)
```

### Health Checks

Monitor cache health:

```python
from core.database.dependencies import check_redis_health

async def check_cache_status():
    health = await check_redis_health()
    if health["status"] == "healthy":
        print(f"Redis is healthy (latency: {health['latency_ms']}ms)")
    else:
        print(f"Redis is unhealthy: {health['error']}")
```

## Best Practices

### 1. Choose Appropriate TTL Values

```python
# Fast-changing data
@cache_response(domain="live", operation="scores", ttl=30)  # 30 seconds

# Moderate-changing data  
@cache_response(domain="users", operation="profile", ttl=600)  # 10 minutes

# Slow-changing data
@cache_response(domain="config", operation="settings", ttl=3600)  # 1 hour
```

### 2. Use Domain-Based Organization

```python
# Good: Organized by domain
@cache_response(domain="users", operation="profile")
@cache_response(domain="competitions", operation="leaderboard")
@cache_response(domain="analytics", operation="stats")

# Avoid: Generic domains
@cache_response(domain="api", operation="data")  # Too generic
```

### 3. Implement Cache Invalidation

```python
@router.post("/users/{user_id}/profile")
@invalidate_cache(domain="users", operation="profile")  # Clear after updates
async def update_user_profile(user_id: str, updates: dict):
    # Update logic
    pass
```

### 4. Handle Cache Failures Gracefully

The system automatically handles cache failures, but you can add custom logic:

```python
@cached(domain="users", operation="search")
async def search_users(query: str):
    try:
        # Your search logic
        return results
    except Exception as e:
        # Cache failures are already handled by the decorator
        # Your function will still execute normally
        logger.error(f"Search failed: {e}")
        raise
```

### 5. Use Request-Level Cache Control

```python
# Client can skip cache with header
curl -H "X-Skip-Cache: true" http://localhost:8000/api/users/123/profile

# Or with query parameter (if configured)
@cached(skip_cache_param="refresh")
async def get_data(refresh: bool = False):
    pass
```

## Cache Key Patterns

### Automatic Key Generation

The system automatically generates hierarchical cache keys:

```
{prefix}:{domain}:{operation}:{params_hash}:{user_id}
```

Examples:
```
community_services:users:profile:a1b2c3:user123
community_services:users:search:d4e5f6:
community_services:competitions:leaderboard:g7h8i9:
```

### Custom Key Generation

For complex scenarios, specify exactly which parameters to include in the cache key:

```python
@cache_response(
    domain="analytics",
    operation="report",
    key_params=["report_type", "start_date", "end_date", "filters"]
)
async def generate_report(
    report_type: str,
    start_date: str,
    end_date: str,
    filters: List[str] = Query([]),
    # These parameters won't be included in cache key
    current_user_id: str = Depends(get_current_user_id),
    database = Depends(get_mongo_kesci_db)
):
    # Implementation
    pass
```

**Automatic Parameter Exclusion:**
The system automatically excludes common internal parameters from cache keys:
- `request` (FastAPI Request object)
- `current_user_id` (Authentication dependency)
- `database` (Database dependency)

**Custom Parameter Selection:**
- Use `key_params` to explicitly specify which parameters to include
- If `key_params` is not provided, all non-excluded parameters are used
- Complex objects (like Pydantic models) are automatically serialized

## Troubleshooting

### Common Issues

#### 1. Cache Not Working

Check if caching is enabled:
```python
from core.cache.utils import is_cache_enabled
print(f"Cache enabled: {is_cache_enabled()}")
```

#### 2. Redis Connection Issues

Test Redis connectivity:
```python
from core.database.dependencies import check_redis_health

health = await check_redis_health()
print(health)
```

#### 3. High Cache Miss Rate

Monitor cache statistics:
```python
cache_manager = await get_cache_manager()
stats = await cache_manager.get_stats()
print(f"Hit rate: {stats['hit_rate']:.2%}")
print(f"Total hits: {stats['hits']}")
print(f"Total misses: {stats['misses']}")
```

#### 4. Memory Usage Issues

Clear old cache entries:
```python
# Clear by pattern
await cache_manager.clear_pattern("old_domain:*")

# Clear expired entries (Redis handles this automatically)
# But you can force cleanup
await cache_manager.cleanup_expired()
```

### Debug Mode

Enable debug logging for cache operations:

```python
import logging
logging.getLogger("core.cache").setLevel(logging.DEBUG)
```

### Cache Inspection

Inspect cache contents:

```python
from core.cache.client import get_cache_client

cache_client = await get_cache_client()

# Check if key exists
exists = await cache_client.exists("community_services:users:profile:abc123")

# Get cache value
value = await cache_client.get("community_services:users:profile:abc123")

# Get all keys matching pattern
keys = await cache_client.get_keys_by_pattern("community_services:users:*")
```

## Performance Optimization

### 1. Connection Pooling

The system uses connection pooling by default. Configure pool size:

```bash
REDIS_MAX_CONNECTIONS=50  # Increase for high-traffic applications
```

### 2. Batch Operations

Use bulk operations for better performance:

```python
cache_manager = await get_cache_manager()

# Bulk set
data = {
    "key1": "value1",
    "key2": "value2",
    "key3": "value3"
}
await cache_manager.set_many(data, ttl=600)

# Bulk get
keys = ["key1", "key2", "key3"]
values = await cache_manager.get_many(keys)
```

### 3. Cache Warming

Pre-populate cache with frequently accessed data:

```python
async def warm_user_cache():
    cache_manager = await get_cache_manager()
    
    # Get popular users
    popular_users = await get_popular_users()
    
    # Warm cache
    for user in popular_users:
        cache_key = generate_cache_key("users", "profile", {"user_id": user.id})
        await cache_manager.set(cache_key, user.to_dict(), ttl=600)
```

### 4. Monitor Performance

Track cache performance metrics:

```python
async def cache_metrics():
    cache_manager = await get_cache_manager()
    stats = await cache_manager.get_stats()
    
    return {
        "hit_rate": stats["hit_rate"],
        "total_operations": stats["hits"] + stats["misses"],
        "memory_usage": stats.get("memory_usage", "N/A"),
        "connection_count": stats.get("connections", "N/A")
    }
```

## Advanced Usage

### Custom Serialization

For complex objects, implement custom serialization:

```python
import json
from datetime import datetime

def custom_serializer(obj):
    if isinstance(obj, datetime):
        return obj.isoformat()
    return str(obj)

# The cache client automatically handles this
cache_client = await get_cache_client()
await cache_client.set("key", complex_object)  # Uses json.dumps with default=str
```

### Conditional Caching

Cache only under certain conditions:

```python
@cached(
    domain="analytics",
    operation="expensive_calculation",
    cache_condition=lambda result: result.get("confidence", 0) > 0.8
)
async def expensive_calculation():
    result = perform_calculation()
    return result  # Only cached if confidence > 0.8
```

### User-Specific Caching

Cache data per user:

```python
@cache_response(
    domain="recommendations",
    operation="personalized",
    user_from_request=lambda req: req.headers.get("X-User-ID")
)
async def get_recommendations(request: Request):
    user_id = request.headers.get("X-User-ID")
    return generate_recommendations(user_id)
```

This guide covers the essential aspects of using the caching system. For more advanced scenarios or specific questions, refer to the source code in `core/cache/` or consult the development team. 