import pytest
import asyncio
from tests.background_tasks.mock_tasks import (
    simple_mock_job,
    mock_job_run_count,
    reset_mock_job_state,
    last_run_times,
)


@pytest.mark.asyncio
async def test_scheduler_runs_mock_job_interval(test_scheduler):
    """Test that the scheduler can add, run, and stop a mock job on an interval."""
    reset_mock_job_state()  # Ensure a clean state for the mock job

    # Add the mock job to run every 0.5 seconds
    test_scheduler.add_job(
        simple_mock_job, trigger="interval", seconds=0.5, id="interval_mock_job"
    )

    assert mock_job_run_count == 0

    # Start the scheduler
    test_scheduler.start()

    # Let the scheduler run for a short period (e.g., ~1.6 seconds to catch ~3 runs)
    await asyncio.sleep(1.6)

    # Stop the scheduler
    # The fixture's teardown will also call shutdown, but explicit call is fine for clarity or specific needs.
    if test_scheduler.running:
        test_scheduler.shutdown(wait=False)

    # Assert that the job ran multiple times
    # Depending on timing, it might be 2, 3, or 4 times.
    # Let's check if it ran at least twice.
    assert mock_job_run_count >= 2, (
        f"Mock job ran {mock_job_run_count} times, expected at least 2"
    )

    # Optional: Check timing between runs if needed
    if len(last_run_times) >= 2:
        time_diff = last_run_times[1] - last_run_times[0]
        assert 0.4 < time_diff < 0.6, (
            f"Time difference between first two runs was {time_diff:.3f}s, expected ~0.5s"
        )

    print(f"Mock job ran {mock_job_run_count} times. Run times: {last_run_times}")
    reset_mock_job_state()  # Clean up state after test
