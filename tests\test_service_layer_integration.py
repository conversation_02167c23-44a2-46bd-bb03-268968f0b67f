"""
Integration tests for the new domain-focused architecture.

Tests the complete workflow from API endpoints through the domain services
to ensure proper integration and error handling.
"""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock

from core.container import (
    initialize_container,
    get_user_admin,
    get_shence_integration,
)
from libs.auth import create_auth_context

from gateway.domains.users.services import UserServices


class TestDomainIntegration:
    """Test integration between domain services and other layers."""

    @pytest.fixture(autouse=True)
    async def setup_services(self):
        """Initialize services for each test."""
        initialize_container()
        yield
        # Cleanup if needed

    @pytest.mark.asyncio
    async def test_user_workflow_integration(self):
        """Test complete user workflow: domain service -> admin -> integration."""
        # Mock MongoDB operations
        with (
            patch("gateway.admin.base.get_mongo_database") as mock_admin_db,
            patch("gateway.admin.base.get_mongo_db_client") as mock_client,
        ):
            # Setup admin mocks
            mock_admin_collection = AsyncMock()
            mock_admin_collection.update_one.return_value = MagicMock(modified_count=1)
            mock_admin_db.return_value.__getitem__.return_value = mock_admin_collection

            # Setup transaction mock
            mock_session = AsyncMock()
            mock_client.return_value.start_session.return_value.__aenter__.return_value = mock_session

            # 1. Use domain service for user operations
            user_service = UserServices()

            # Mock the domain service method
            with patch.object(user_service, "get_user_profile") as mock_profile:
                mock_profile.return_value = {
                    "user_id": "test_user_123",
                    "username": "testuser",
                    "email": "<EMAIL>",
                    "credits": 100,
                }

                profile_result = await user_service.get_user_profile("test_user_123")
                assert profile_result["username"] == "testuser"

            # 2. Update user via admin (still uses old admin layer)
            user_admin = get_user_admin()
            auth_context = create_auth_context("test_user_123")
            update_result = await user_admin.update_user(
                auth_context=auth_context,
                user_id="test_user_123",
                updates={"email": "<EMAIL>"},
            )

            assert update_result.is_success

            # 3. Track event via integration
            with patch.object(get_shence_integration(), "_post_request") as mock_post:
                mock_post.return_value.is_success = True
                mock_post.return_value.data = {"status": "success"}

                shence = get_shence_integration()
                track_result = await shence.track_event(
                    auth_context=auth_context,
                    event_name="user_updated",
                    properties={"user_id": "test_user_123"},
                )

                assert track_result.is_success


if __name__ == "__main__":
    pytest.main([__file__])
