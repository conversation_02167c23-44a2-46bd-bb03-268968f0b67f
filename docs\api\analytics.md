# Analytics API Documentation

## Overview

The Analytics API provides endpoints for rankings, statistics, event tracking, and ShenCe analytics integration. All endpoints are prefixed with `/api/v1/analytics`.

## Authentication

All endpoints require authentication and specific permission checks as noted per endpoint.

## Endpoints

### Health Check

#### Analytics Health Check
```http
GET /api/v1/analytics/health
```

**Description**: Check the health status of analytics services.

**Response**: `AnalyticsHealthResponse`

**Example Response**:
```json
{
  "status": "healthy",
  "rankings_available": true,
  "statistics_available": true,
  "event_tracking_available": true
}
```

---

### Camp-Only Statistics

#### Get Summary Statistics
```http
POST /api/v1/analytics/camp-only/statistics/summary
```

**Description**: Get core summer camp statistics.

**Response**: `SuccessResponse` with summary statistics

**Permissions**: Requires `statistics_access`

**Example Response**:
```json
{
  "success": true,
  "data": [
    {
      "statistics_name": "Total Participants",
      "statistics_value": 1500
    },
    {
      "statistics_name": "Active Competitions",
      "statistics_value": 12
    }
  ]
}
```

---

### Camp-Only Rankings

#### Get School Rankings
```http
POST /api/v1/analytics/camp-only/rankings/schools
```

**Description**: Get school rankings by total credits.

**Request Body**: `SchoolRanking`
```json
{
  "page_size": 20,
  "current_page": 1
}
```

**Response**: `SuccessResponse` with school ranking data

**Permissions**: Requires `ranking_access`

**Example Response**:
```json
{
  "success": true,
  "data": {
    "total": 100,
    "page_size": 20,
    "current_page": 1,
    "res": [
      {
        "university": "Beijing University",
        "total_credits": 15000.0
      }
    ]
  }
}
```

#### Get User Rankings by Route
```http
POST /api/v1/analytics/camp-only/rankings/users/by_route
```

**Description**: Get user rankings for each competition route.

**Request Body**: `UserRankingEveryRoute`
```json
{
  "top_num": 30
}
```

**Response**: `SuccessResponse` with user rankings by route

**Permissions**: Requires `ranking_access`

#### Get Total User Rankings
```http
POST /api/v1/analytics/camp-only/rankings/users/total
```

**Description**: Get user rankings by total credits across all routes.

**Request Body**: `UserRankingTotalRoute`
```json
{
  "page_size": 20,
  "current_page": 1
}
```

**Response**: `SuccessResponse` with total user rankings

**Permissions**: Requires `ranking_access`

#### Get Top Users by Route
```http
POST /api/v1/analytics/camp-only/rankings/top_by_route
```

**Description**: Get top N users by route with aggregated data.

**Request Body**: `TopNByRoute`
```json
{
  "top_num": 10
}
```

**Response**: `SuccessResponse` with top users by route

**Permissions**: Requires `ranking_access`

---

### Event Tracking

#### Get Event Tracks
```http
GET /api/v1/analytics/events/tracks
```

**Description**: Process and retrieve event tracks from file.

**Response**: `EventTracksResponse` with event data

**Permissions**: Requires `event_tracking_access`

**Example Response**:
```json
{
  "success": true,
  "data": {
    "events": [...],
    "total": 150
  }
}
```

---

### ShenCe Integration

#### Get ShenCe Directories
```http
GET /api/v1/analytics/integrations/shence/directories
```

**Description**: Get ShenCe tag directory metadata for analytics.

**Response**: `ShenceDirectoryResponse`

**Permissions**: Requires `shence_access_permission`

#### Get ShenCe Tag Metadata
```http
GET /api/v1/analytics/integrations/shence/tags/{tag_id}/meta
```

**Description**: Get metadata for a specific ShenCe tag.

**Parameters**:
- `tag_id` (path, required): ShenCe tag ID

**Response**: `ShenceTagMetaResponse`

**Permissions**: Requires `shence_access_permission`

#### Upload File to ShenCe
```http
POST /api/v1/analytics/integrations/shence/files/upload
```

**Description**: Upload CSV data to ShenCe for analytics tag processing.

**Request Body**: `ShenceFileUploadRequest`
```json
{
  "file_name_prefix": "analytics_data_2023",
  "csv_data": [
    {"user_id": "123", "value": "data1"},
    {"user_id": "456", "value": "data2"}
  ],
  "project": "production"
}
```

**Response**: `ShenceUploadResponse`

**Permissions**: Requires `shence_access_permission`

#### Create or Update ShenCe Tags
```http
POST /api/v1/analytics/integrations/shence/tags
```

**Description**: Create new ShenCe analytics tags or update existing ones.

**Request Body**: `ShenceTagCreateRequest`
```json
{
  "tag_name": "user_engagement",
  "tag_id": "tag_123",
  "dir_id": "dir_456",
  "data": [
    {"user_id": "123", "engagement_score": "high"},
    {"user_id": "456", "engagement_score": "medium"}
  ],
  "data_type": "STRING"
}
```

**Response**: `ShenceTagResponse`

**Permissions**: Requires `shence_access_permission`

#### Update ShenCe Tags
```http
POST /api/v1/analytics/integrations/shence/tags/update
```

**Description**: Update existing ShenCe analytics tags with new data.

**Request Body**: `ShenceTagUpdateRequest`
```json
{
  "file_name": "analytics_data_2023_01_01_12:00:00",
  "tag_name": "user_engagement",
  "tag_id": "tag_123",
  "dir_id": "dir_456",
  "data_type": "STRING",
  "task_name": "update_engagement_scores"
}
```

**Response**: `ShenceTagResponse`

**Permissions**: Requires `shence_access_permission`

## Data Models

### School Ranking Element
```json
{
  "university": "string",
  "total_credits": 15000.0
}
```

### User Ranking Element
```json
{
  "user_id": "string",
  "user_name": "string",
  "total_credits": 1500.0
}
```

### Summary Statistics
```json
{
  "statistics_name": "string",
  "statistics_value": 100
}
```

### ShenCe Directory Response
```json
{
  "directories": [...],
  "total_count": 50,
  "project": "production"
}
```

## Error Responses

All endpoints may return standard error responses:
- `400 Bad Request`: Invalid parameters or request body
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server error
- `503 Service Unavailable`: Service temporarily unavailable

## Usage Examples

### Frontend Integration

#### Get School Rankings with Pagination
```javascript
const response = await fetch('/api/v1/analytics/camp-only/rankings/schools', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer <token>'
  },
  body: JSON.stringify({
    page_size: 20,
    current_page: 1
  })
});
```

#### Upload Data to ShenCe
```javascript
const response = await fetch('/api/v1/analytics/integrations/shence/files/upload', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer <token>'
  },
  body: JSON.stringify({
    file_name_prefix: 'user_data',
    csv_data: [
      {user_id: '123', score: '95'},
      {user_id: '456', score: '87'}
    ]
  })
});
```

## Notes for Frontend Development

1. **Permissions**: Different endpoints require different permission levels
2. **Pagination**: Rankings endpoints support pagination
3. **ShenCe Integration**: Requires proper data validation before upload
4. **Event Tracking**: Reads from local file system
5. **Camp-Only**: Some endpoints are specifically for summer camp analytics
6. **Error Handling**: Check service availability via health endpoint

## Related APIs

- **Users API**: For user profile data in rankings
- **Community API**: For university data in school rankings
- **Camp Admin API**: For competition and credit data
